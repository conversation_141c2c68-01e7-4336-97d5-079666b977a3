# IDEALAB微服务部署配置

version: '3.8'

services:
  # API网关
  gateway:
    build: ./services/gateway
    ports:
      - "16576:16576"
    environment:
      - NODE_ENV=production
      - AUTH_SERVICE_URL=http://auth:8001
      - NLP_SERVICE_URL=http://nlp-service:8002
      - FINANCIAL_SERVICE_URL=http://financial-service:8003
      - DOCUMENT_SERVICE_URL=http://document-service:8004
      - PORT=16576
    depends_on:
      - auth
      - nlp-service
      - financial-service
      - document-service
    restart: unless-stopped
    networks:
      - idealab-network

  # 认证服务
  auth:
    build: ./services/auth
    ports:
      - "8001:8001"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/idealab_auth
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - idealab-network

  # NLP服务
  nlp-service:
    build: ./services/nlp-service
    ports:
      - "8002:8002"
    environment:
      - PYTHONUNBUFFERED=1
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/idealab_nlp
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - idealab-network
    volumes:
      - nlp-models:/app/models

  # 金融数据服务
  financial-service:
    build: ./services/financial-service
    ports:
      - "8003:8003"
    environment:
      - PYTHONUNBUFFERED=1
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/idealab_financial
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=${NEO4J_PASSWORD}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - neo4j
      - redis
    restart: unless-stopped
    networks:
      - idealab-network

  # 文档处理服务
  document-service:
    build: ./services/document-service
    ports:
      - "8004:8004"
    environment:
      - PYTHONUNBUFFERED=1
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - minio
      - redis
    restart: unless-stopped
    networks:
      - idealab-network
    volumes:
      - document-temp:/app/temp

  # 前端应用
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:16576
    depends_on:
      - gateway
    restart: unless-stopped
    networks:
      - idealab-network

  # 数据库服务
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_MULTIPLE_DATABASES=idealab_nlp,idealab_financial,idealab_auth
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./infrastructure/docker/postgres-init:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - idealab-network

  # MongoDB
  mongodb:
    image: mongo:7-jammy
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}
    ports:
      - "27017:27017"
    volumes:
      - mongodb-data:/data/db
    restart: unless-stopped
    networks:
      - idealab-network

  # Neo4j图数据库
  neo4j:
    image: neo4j:5.15-community
    environment:
      - NEO4J_AUTH=neo4j/${NEO4J_PASSWORD}
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
    ports:
      - "7474:7474"
      - "7687:7687"
    volumes:
      - neo4j-data:/data
      - neo4j-logs:/logs
    restart: unless-stopped
    networks:
      - idealab-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    networks:
      - idealab-network

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=${MINIO_ACCESS_KEY}
      - MINIO_ROOT_PASSWORD=${MINIO_SECRET_KEY}
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio-data:/data
    restart: unless-stopped
    networks:
      - idealab-network

  # Nginx负载均衡器
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infrastructure/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./infrastructure/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - gateway
      - frontend
    restart: unless-stopped
    networks:
      - idealab-network

volumes:
  postgres-data:
  mongodb-data:
  neo4j-data:
  neo4j-logs:
  redis-data:
  minio-data:
  nlp-models:
  document-temp:

networks:
  idealab-network:
    driver: bridge