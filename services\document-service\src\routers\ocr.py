from fastapi import APIRouter, HTTPException
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/")
async def ocr_root():
    """OCR服务根端点"""
    return {
        "service": "OCR Recognition",
        "status": "available",
        "features": ["批量OCR文字识别", "图片文字提取", "PDF文字识别"]
    }


@router.post("/batch")
async def batch_ocr():
    """批量OCR处理"""
    # TODO: 实现批量OCR功能
    raise HTTPException(status_code=501, detail="OCR功能开发中")
