// datasource db defines the database connection.
// It's configured to use PostgreSQL and reads the connection
// URL from the DATABASE_URL environment variable.
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// generator client specifies that Prisma Client should be generated.
// It's the query builder that will be used in the application code.
generator client {
  provider = "prisma-client-js"
}

model User {
  id        String   @id @default(cuid())
  username  String   @unique
  password  String
  email     String   @unique
  role      String   @default("user")
  passwordChangeRequired <PERSON>ole<PERSON> @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
} 