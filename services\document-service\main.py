from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import logging
from contextlib import asynccontextmanager

from src.routers import ocr, watermark, excel
from src.config.settings import settings
from src.services.storage_service import StorageService
from src.services.ocr_service import OCRService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("📄 Initializing Document Processing Service...")
    
    # 初始化存储服务
    storage_service = StorageService()
    await storage_service.initialize()
    app.state.storage_service = storage_service
    
    # 初始化OCR服务
    ocr_service = OCRService()
    await ocr_service.initialize()
    app.state.ocr_service = ocr_service
    
    logger.info("✅ Document Processing Service initialized successfully")
    
    yield
    
    # 关闭时清理
    logger.info("🛑 Shutting down Document Processing Service...")


# 创建FastAPI应用
app = FastAPI(
    title="IDEALAB Document Processing Service",
    description="Document processing service for OCR, watermarking and Excel operations",
    version="1.0.0",
    lifespan=lifespan
)

# 中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "service": "document-service",
        "status": "healthy",
        "version": "1.0.0",
        "capabilities": {
            "ocr": "tesseract-ready",
            "watermark": "opencv-ready",
            "excel": "pandas-ready",
            "storage": "minio-ready"
        }
    }


@app.get("/")
async def root():
    """根端点"""
    return {
        "message": "IDEALAB Document Processing Service",
        "version": "1.0.0",
        "features": [
            "批量OCR文字识别",
            "图片和文档水印添加",
            "Excel文件智能合并",
            "文档格式转换",
            "文件存储管理"
        ],
        "endpoints": {
            "ocr": "/api/ocr/*",
            "watermark": "/api/watermark/*",
            "excel": "/api/excel/*",
            "health": "/health"
        }
    }


# 注册路由
app.include_router(ocr.router, prefix="/api/ocr", tags=["OCR Recognition"])
app.include_router(watermark.router, prefix="/api/watermark", tags=["Watermark Processing"])
app.include_router(excel.router, prefix="/api/excel", tags=["Excel Operations"])


# 错误处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"Global exception: {exc}")
    return HTTPException(
        status_code=500,
        detail="Internal Document Service Error"
    )


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )