import React, { useState } from 'react';
import { Typography, Tooltip } from 'antd';
import { Star } from 'lucide-react';
import { Tool } from '@/types';

const { Text } = Typography;

export const ToolCard: React.FC<{ tool: Tool; onClick: () => void }> = ({ tool, onClick }) => {
  const [isHovered, setIsHovered] = useState(false);
  const IconComponent = tool.icon;

  return (
    <Tooltip title={tool.description}>
        <div
            onClick={onClick}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '12px',
                padding: '16px',
                borderRadius: '12px',
                cursor: 'pointer',
                transition: 'all 0.2s ease-in-out',
                background: isHovered ? 'var(--bg-elevated)' : 'transparent',
                position: 'relative',
            }}
        >
            <div style={{
                width: '64px',
                height: '64px',
                borderRadius: '16px',
                background: `linear-gradient(135deg, var(--bg-surface), var(--bg-elevated))`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                transform: isHovered ? 'scale(1.1) rotate(5deg)' : 'scale(1) rotate(0deg)',
                border: '1px solid var(--border-primary)',
                boxShadow: isHovered ? '0 4px 12px rgba(0,0,0,0.1)' : 'none',
                color: 'var(--text-primary)'
            }}>
                <IconComponent size={28} />
            </div>
            {tool.featured && (
                <div style={{
                    position: 'absolute',
                    top: '8px',
                    right: '8px',
                    background: 'linear-gradient(135deg, var(--warning-500) 0%, var(--warning-400) 100%)',
                    borderRadius: '50%',
                    width: '20px',
                    height: '20px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#fff',
                }}>
                    <Star size={12} />
                </div>
            )}
            <Text style={{ color: 'var(--text-primary)', fontWeight: '500', textAlign: 'center', maxWidth: '100px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                {tool.name}
            </Text>
        </div>
    </Tooltip>
  );
}; 