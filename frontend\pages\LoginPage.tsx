import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Typography, message, Checkbox } from 'antd';
import { User, Lock, ArrowRight, Shield, Zap, TrendingUp, Clock, Wifi, Star, Database } from 'lucide-react';
import useAuthStore from '@/store/auth';
import { MolecularBackground } from '@/components/MolecularBackground';

const { Title, Text } = Typography;

export const LoginPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [rememberMe, setRememberMe] = useState(false);
  const login = useAuthStore(state => state.login);

  // 实时时间更新
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const onFinish = async (values: { username: string; password: string }) => {
    setLoading(true);
    try {
      const success = await login(values.username, values.password, rememberMe);
      if (success) {
        if (rememberMe) {
          localStorage.setItem('rememberMe', 'true');
          localStorage.setItem('rememberedUsername', values.username);
        } else {
          localStorage.removeItem('rememberMe');
          localStorage.removeItem('rememberedUsername');
        }
        message.success('登录成功！欢迎进入IDEALAB');
      } else {
        message.error('用户名或密码错误，请重试');
      }
    } catch (error) {
      message.error('登录失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 初始化记住的用户名
  useEffect(() => {
    const remembered = localStorage.getItem('rememberMe');
    const rememberedUsername = localStorage.getItem('rememberedUsername');
    if (remembered === 'true' && rememberedUsername) {
      setRememberMe(true);
      form.setFieldsValue({ username: rememberedUsername });
    }
  }, [form]);

  return (
    <div 
      style={{
        minHeight: '100vh',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* 使用美化后的分子背景 */}
      <MolecularBackground />

      {/* 顶部状态栏 */}
      <div 
        style={{
          position: 'relative',
          zIndex: 20,
          padding: '16px 32px',
          background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
        }}
      >
        <div 
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            maxWidth: '1200px',
            margin: '0 auto'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <Clock size={20} style={{ color: '#3b82f6' }} />
            <div>
              <div style={{ color: 'white', fontSize: '16px', fontWeight: 600 }}>
                {currentTime.toLocaleTimeString('zh-CN', {
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit'
                })}
              </div>
              <div style={{ color: 'rgba(255, 255, 255, 0.7)', fontSize: '12px' }}>
                {currentTime.toLocaleDateString('zh-CN', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  weekday: 'long'
                })}
              </div>
            </div>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <div 
              style={{
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                background: '#10b981',
                animation: 'pulse 2s ease-in-out infinite'
              }}
            />
            <Wifi size={16} style={{ color: 'rgba(255, 255, 255, 0.7)' }} />
            <span style={{ color: 'rgba(255, 255, 255, 0.7)', fontSize: '14px' }}>系统在线</span>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div 
        style={{
          position: 'relative',
          zIndex: 10,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 'calc(100vh - 80px)',
          padding: '40px 24px'
        }}
      >
        <div 
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '80px',
            maxWidth: '1200px',
            width: '100%',
            flexWrap: 'wrap'
          }}
        >
          {/* 左侧品牌区域 */}
          <div 
            style={{
              flex: '1 1 500px',
              textAlign: 'center',
              minWidth: '400px'
            }}
          >
            {/* 品牌Logo */}
            <div 
              style={{
                position: 'relative',
                display: 'inline-block',
                marginBottom: '40px'
              }}
            >
              <div 
                style={{
                  width: '120px',
                  height: '120px',
                  background: 'linear-gradient(135deg, #2563eb 0%, #0891b2 100%)',
                  borderRadius: '28px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto',
                  boxShadow: '0 20px 60px rgba(37, 99, 235, 0.3)',
                  animation: 'logoFloat 6s ease-in-out infinite'
                }}
              >
                <Database size={48} style={{ color: 'white' }} />
              </div>
              <div 
                style={{
                  position: 'absolute',
                  top: '-8px',
                  right: '-8px',
                  width: '40px',
                  height: '40px',
                  background: 'linear-gradient(135deg, #0891b2 0%, #22d3ee 100%)',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  animation: 'sparkle 3s ease-in-out infinite'
                }}
              >
                <Star size={20} style={{ color: 'white' }} />
              </div>
            </div>

            {/* 品牌标题 */}
            <Title 
              level={1} 
              style={{ 
                fontSize: '64px',
                fontWeight: 800,
                background: 'linear-gradient(135deg, #2563eb 0%, #0891b2 50%, #22d3ee 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                margin: '0 0 16px 0',
                lineHeight: 1.1
              }}
            >
              IDEALAB
            </Title>

            <Text 
              style={{ 
                fontSize: '22px',
                color: 'rgba(255, 255, 255, 0.8)',
                display: 'block',
                marginBottom: '48px',
                fontWeight: 500
              }}
            >
              业务创新实验平台
            </Text>

            <Text 
              style={{ 
                fontSize: '18px',
                color: 'rgba(255, 255, 255, 0.6)',
                display: 'block',
                marginBottom: '40px',
                maxWidth: '480px',
                margin: '0 auto 40px',
                lineHeight: 1.6
              }}
            >
              提供智能化分析工具与业务解决方案，助力数据驱动的科学决策
            </Text>

            {/* 特性徽章 - 集成图标 */}
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                gap: '12px',
                flexWrap: 'wrap',
                marginBottom: '32px'
              }}
            >
              {[
                { text: '企业级安全', icon: Shield, color: '#3b82f6' },
                { text: 'AI智能分析', icon: Zap, color: '#0891b2' },
                { text: '数据驱动', icon: TrendingUp, color: '#22d3ee' }
              ].map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <div
                    key={index}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      padding: '10px 14px',
                      background: 'linear-gradient(135deg, rgba(37, 99, 235, 0.15) 0%, rgba(8, 145, 178, 0.1) 100%)',
                      border: '1px solid rgba(37, 99, 235, 0.2)',
                      borderRadius: '20px',
                      backdropFilter: 'blur(10px)',
                      color: 'rgba(255, 255, 255, 0.8)',
                      fontSize: '13px',
                      fontWeight: 500,
                      transition: 'all 0.3s ease',
                      cursor: 'pointer'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = 'linear-gradient(135deg, rgba(37, 99, 235, 0.2) 0%, rgba(8, 145, 178, 0.15) 100%)';
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.color = 'white';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = 'linear-gradient(135deg, rgba(37, 99, 235, 0.15) 0%, rgba(8, 145, 178, 0.1) 100%)';
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.color = 'rgba(255, 255, 255, 0.8)';
                    }}
                  >
                    <IconComponent size={16} style={{ color: feature.color, flexShrink: 0 }} />
                    <span>{feature.text}</span>
                  </div>
                );
              })}
            </div>
          </div>

          {/* 右侧登录表单 */}
          <div 
            style={{
              flex: '1 1 400px',
              maxWidth: '450px',
              margin: '0 auto'
            }}
          >
            <div 
              style={{
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
                border: '1px solid rgba(255, 255, 255, 0.15)',
                borderRadius: '24px',
                backdropFilter: 'blur(24px)',
                boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
                padding: '48px 40px',
                animation: 'slideInRight 0.8s ease-out'
              }}
            >
              {/* 表单头部 */}
              <div style={{ textAlign: 'center', marginBottom: '40px' }}>
                <Title 
                  level={2} 
                  style={{ 
                    color: 'white',
                    fontSize: '32px',
                    fontWeight: 700,
                    margin: '0 0 8px 0'
                  }}
                >
                  系统登录
                </Title>
                <Text 
                  style={{ 
                    color: 'rgba(255, 255, 255, 0.7)',
                    fontSize: '16px'
                  }}
                >
                  请输入您的账户信息以访问系统
                </Text>
              </div>

              {/* 登录表单 */}
              <Form
                form={form}
                name="login"
                onFinish={onFinish}
                layout="vertical"
                requiredMark={false}
                autoComplete="off"
                style={{ marginBottom: '32px' }}
              >
                <Form.Item
                  name="username"
                  rules={[
                    { required: true, message: '请输入用户名' },
                    { min: 3, message: '用户名至少3个字符' }
                  ]}
                >
                  <Input
                    prefix={<User size={20} style={{ color: '#3b82f6' }} />}
                    placeholder="请输入用户名"
                    size="large"
                    autoComplete="username"
                    style={{
                      height: '56px',
                      borderRadius: '12px',
                      border: '1px solid rgba(255, 255, 255, 0.15)',
                      background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%)',
                      backdropFilter: 'blur(10px)',
                      fontSize: '16px',
                      color: 'white'
                    }}
                  />
                </Form.Item>

                <Form.Item
                  name="password"
                  rules={[
                    { required: true, message: '请输入密码' },
                    { min: 6, message: '密码至少6个字符' }
                  ]}
                >
                  <Input.Password
                    prefix={<Lock size={20} style={{ color: '#3b82f6' }} />}
                    placeholder="请输入密码"
                    size="large"
                    autoComplete="current-password"
                    style={{
                      height: '56px',
                      borderRadius: '12px',
                      border: '1px solid rgba(255, 255, 255, 0.15)',
                      background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%)',
                      backdropFilter: 'blur(10px)',
                      fontSize: '16px'
                    }}
                  />
                </Form.Item>

                <div 
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '24px'
                  }}
                >
                  <Checkbox 
                    checked={rememberMe} 
                    onChange={(e) => setRememberMe(e.target.checked)}
                    style={{ color: 'rgba(255, 255, 255, 0.8)' }}
                  >
                    记住登录状态
                  </Checkbox>
                </div>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    size="large"
                    block
                    icon={!loading && <ArrowRight size={20} />}
                    style={{
                      height: '56px',
                      fontSize: '16px',
                      fontWeight: 600,
                      borderRadius: '12px',
                      background: 'linear-gradient(135deg, #2563eb 0%, #0891b2 100%)',
                      border: 'none',
                      boxShadow: '0 8px 24px rgba(37, 99, 235, 0.4)',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 12px 32px rgba(37, 99, 235, 0.5)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = '0 8px 24px rgba(37, 99, 235, 0.4)';
                    }}
                  >
                    {loading ? '登录中...' : '立即登录'}
                  </Button>
                </Form.Item>
              </Form>

              {/* 底部信息 */}
              <div 
                style={{
                  textAlign: 'center',
                  paddingTop: '24px',
                  borderTop: '1px solid rgba(255, 255, 255, 0.1)'
                }}
              >
                <Text style={{ color: 'rgba(255, 255, 255, 0.5)', fontSize: '12px' }}>
                  IDEALAB Professional v2.0
                </Text>
                <br />
                <Text style={{ color: 'rgba(255, 255, 255, 0.4)', fontSize: '11px' }}>
                  © 2024 企业级数据分析平台 • 安全登录系统
                </Text>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};