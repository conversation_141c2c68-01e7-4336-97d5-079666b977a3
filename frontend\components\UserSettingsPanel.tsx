import React, { useState } from 'react';
import { Drawer, Form, Input, Button, message, Divider, Avatar, Space } from 'antd';
import { User, Lock, Save, Eye, EyeOff } from 'lucide-react';
import { api } from '@/services/api';
import useAuthStore from '@/store/auth';

interface UserSettingsPanelProps {
  open: boolean;
  onClose: () => void;
}

export const UserSettingsPanel: React.FC<UserSettingsPanelProps> = ({ open, onClose }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  // const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  // const [showNewPassword, setShowNewPassword] = useState(false);
  const { user } = useAuthStore();

  const handleSaveProfile = async (values: any) => {
    setLoading(true);
    try {
      // 更新用户名
      if (values.username && values.username !== user?.username) {
        const response = await api.post('/user/update-profile', {
          username: values.username,
        });
        if (response.success) {
          message.success('用户名更新成功！');
        } else {
          message.error(response.message || '用户名更新失败');
          return;
        }
      }

      // 更新密码（如果填写了密码字段）
      if (values.currentPassword && values.newPassword) {
        if (values.newPassword !== values.confirmPassword) {
          message.error('两次输入的新密码不一致！');
          return;
        }
        
        const passwordResponse = await api.post('/user/change-password', {
          oldPassword: values.currentPassword,
          newPassword: values.newPassword,
        });
        
        if (passwordResponse.success) {
          message.success('密码更新成功！');
          form.resetFields(['currentPassword', 'newPassword', 'confirmPassword']);
        } else {
          message.error(passwordResponse.message || '密码更新失败，请检查当前密码是否正确');
          return;
        }
      }
      
      if (!values.currentPassword && !values.newPassword && values.username === user?.username) {
        message.info('没有检测到任何更改');
      }
    } catch (error) {
      message.error('保存失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  return (
    <Drawer
      title="用户设置"
      width={420}
      open={open}
      onClose={handleClose}
      bodyStyle={{ padding: '24px' }}
      extra={
        <Button type="primary" onClick={() => form.submit()} loading={loading} icon={<Save size={16} />}>
          保存更改
        </Button>
      }
    >
      <div className="user-settings-panel">
        {/* 用户头像区域 */}
        <div className="user-profile-section">
          <Space direction="vertical" align="center" style={{ width: '100%', marginBottom: '32px' }}>
            <Avatar size={80} src={user?.avatar} className="user-avatar-large">
              {user?.username?.charAt(0).toUpperCase()}
            </Avatar>
            <div style={{ textAlign: 'center' }}>
              <div className="user-current-name">{user?.username}</div>
              <div className="user-role-badge">{user?.role}</div>
            </div>
          </Space>
        </div>

        <Form 
          form={form} 
          layout="vertical" 
          onFinish={handleSaveProfile}
          initialValues={{ username: user?.username }}
          requiredMark={false}
        >
          {/* 基本信息 */}
          <div className="settings-section">
            <h4 className="section-title">基本信息</h4>
            <Form.Item
              name="username"
              label="用户名"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3个字符' },
                { max: 20, message: '用户名最多20个字符' }
              ]}
            >
              <Input 
                prefix={<User size={16} />} 
                placeholder="请输入新的用户名"
                className="settings-input"
              />
            </Form.Item>
          </div>

          <Divider />

          {/* 密码设置 */}
          <div className="settings-section">
            <h4 className="section-title">密码设置</h4>
            <Form.Item
              name="currentPassword"
              label="当前密码"
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (getFieldValue('newPassword') && !value) {
                      return Promise.reject('要修改密码，请先输入当前密码');
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <Input.Password 
                prefix={<Lock size={16} />}
                placeholder="请输入当前密码"
                className="settings-input"
                iconRender={(visible) => visible ? <Eye size={16} /> : <EyeOff size={16} />}
              />
            </Form.Item>

            <Form.Item
              name="newPassword"
              label="新密码"
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (getFieldValue('currentPassword') && !value) {
                      return Promise.reject('请输入新密码');
                    }
                    if (value && value.length < 6) {
                      return Promise.reject('新密码至少6个字符');
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <Input.Password 
                prefix={<Lock size={16} />}
                placeholder="请输入新密码"
                className="settings-input"
                iconRender={(visible) => visible ? <Eye size={16} /> : <EyeOff size={16} />}
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              label="确认新密码"
              dependencies={['newPassword']}
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    const newPassword = getFieldValue('newPassword');
                    if (newPassword && !value) {
                      return Promise.reject('请确认新密码');
                    }
                    if (newPassword && value && newPassword !== value) {
                      return Promise.reject('两次输入的密码不一致');
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <Input.Password 
                prefix={<Lock size={16} />}
                placeholder="请再次输入新密码"
                className="settings-input"
                iconRender={(visible) => visible ? <Eye size={16} /> : <EyeOff size={16} />}
              />
            </Form.Item>
          </div>
        </Form>
      </div>

      <style>{`
        .user-profile-section {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          margin: -24px -24px 32px -24px;
          padding: 32px 24px;
          border-radius: 0 0 16px 16px;
        }

        .user-avatar-large {
          border: 4px solid rgba(255, 255, 255, 0.3);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

          .user-current-name {
            font-size: 18px;
            font-weight: 600;
            color: white;
            margin-bottom: 4px;
          }
          
          .user-role-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            backdrop-filter: blur(10px);
          }
          
          .settings-section {
            margin-bottom: 24px;
          }
          
          .section-title {
            color: #1f2937;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
          }
          
          .settings-input {
            border-radius: 8px;
            height: 40px;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
          }
          
          .settings-input:hover,
          .settings-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
        }
      `}</style>
    </Drawer>
  );
}; 