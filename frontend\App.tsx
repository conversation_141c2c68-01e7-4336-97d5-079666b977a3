import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, theme } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import useAuthStore from '@/store/auth';
import { LoginPage } from '@/pages/LoginPage';
import { DashboardPage } from '@/pages/DashboardPage';
import { SentimentAnalysisTool } from '@/pages/tools/SentimentAnalysisTool';
import { EquityPenetrationTool } from '@/pages/tools/EquityPenetrationTool';
import { EquityPledgeTool } from '@/pages/tools/EquityPledgeTool';
import { BondAnalysisTool } from '@/pages/tools/BondAnalysisTool';
import { BatchOCRTool } from '@/pages/tools/BatchOCRTool';
import { BatchWatermarkTool } from '@/pages/tools/BatchWatermarkTool';
import { ExcelMergerTool } from '@/pages/tools/ExcelMergerTool';
import { AppLayout } from '@/components/AppLayout';
import { ChangePasswordModal } from '@/components/ChangePasswordModal';

const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuthStore(state => ({
    isAuthenticated: state.isAuthenticated,
  }));
  return isAuthenticated ? <>{children}</> : <Navigate to="/login" replace />;
};

export const App: React.FC = () => {
  const { isAuthenticated, checkAuth, passwordChangeRequired } = useAuthStore(state => ({
    isAuthenticated: state.isAuthenticated,
    checkAuth: state.checkAuth,
    passwordChangeRequired: state.passwordChangeRequired,
  }));

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        algorithm: theme.darkAlgorithm,
        token: {
          // 专业化主题色彩配置
          colorPrimary: '#2563eb',
          colorSuccess: '#10b981',
          colorWarning: '#f97316',
          colorError: '#dc2626',
          colorInfo: '#0891b2',
          
          // 边框和圆角
          borderRadius: 8,
          borderRadiusLG: 12,
          borderRadiusXS: 4,
          
          // 字体配置
          fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
          fontSizeHeading1: 32,
          fontSizeHeading2: 24,
          fontSizeHeading3: 20,
          fontSizeHeading4: 18,
          fontSize: 14,
          fontSizeLG: 16,
          fontSizeSM: 12,
          
          // 间距配置
          padding: 16,
          paddingLG: 24,
          paddingSM: 12,
          paddingXS: 8,
          margin: 16,
          marginLG: 24,
          marginSM: 12,
          marginXS: 8,
          
          // 阴影配置
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.15)',
          boxShadowSecondary: '0 2px 8px rgba(0, 0, 0, 0.1)',
          
          // 背景色配置
          colorBgContainer: 'rgba(255, 255, 255, 0.08)',
          colorBgElevated: 'rgba(255, 255, 255, 0.12)',
          colorBgLayout: 'transparent',
          colorBgSpotlight: 'rgba(255, 255, 255, 0.15)',
          
          // 文本颜色
          colorText: 'rgba(255, 255, 255, 0.95)',
          colorTextSecondary: 'rgba(255, 255, 255, 0.75)',
          colorTextTertiary: 'rgba(255, 255, 255, 0.55)',
          colorTextQuaternary: 'rgba(255, 255, 255, 0.35)',
          
          // 边框颜色
          colorBorder: 'rgba(255, 255, 255, 0.15)',
          colorBorderSecondary: 'rgba(255, 255, 255, 0.08)',
          
          // 填充颜色
          colorFill: 'rgba(255, 255, 255, 0.08)',
          colorFillSecondary: 'rgba(255, 255, 255, 0.05)',
          colorFillTertiary: 'rgba(255, 255, 255, 0.03)',
          colorFillQuaternary: 'rgba(255, 255, 255, 0.02)',
        },
        components: {
          // 卡片组件
          Card: {
            colorBgContainer: 'rgba(255, 255, 255, 0.08)',
            colorBorder: 'rgba(255, 255, 255, 0.15)',
            borderRadiusLG: 12,
            paddingLG: 24,
            colorTextHeading: 'rgba(255, 255, 255, 0.95)',
          },
          
          // 输入框组件
          Input: {
            colorBgContainer: 'rgba(255, 255, 255, 0.05)',
            colorBorder: 'rgba(255, 255, 255, 0.15)',
            colorText: 'rgba(255, 255, 255, 0.95)',
            colorTextPlaceholder: 'rgba(255, 255, 255, 0.55)',
            borderRadius: 8,
            paddingInline: 16,
            paddingBlock: 10,
            activeBorderColor: '#2563eb',
            hoverBorderColor: 'rgba(255, 255, 255, 0.25)',
          },
          
          // 按钮组件
          Button: {
            borderRadius: 8,
            paddingInline: 24,
            paddingBlock: 10,
            fontWeight: 500,
            primaryColor: '#ffffff',
            colorPrimaryBg: '#2563eb',
            colorPrimaryBgHover: '#0891b2',
            colorPrimaryBorder: '#2563eb',
            colorPrimaryBorderHover: '#0891b2',
            defaultBg: 'rgba(255, 255, 255, 0.05)',
            defaultBorderColor: 'rgba(255, 255, 255, 0.15)',
            defaultColor: 'rgba(255, 255, 255, 0.95)',
            defaultHoverBg: 'rgba(255, 255, 255, 0.08)',
            defaultHoverBorderColor: 'rgba(255, 255, 255, 0.25)',
            defaultHoverColor: 'rgba(255, 255, 255, 1)',
          },
          
          // 表格组件
          Table: {
            colorBgContainer: 'rgba(255, 255, 255, 0.03)',
            colorBorder: 'rgba(255, 255, 255, 0.08)',
            borderRadius: 12,
            colorText: 'rgba(255, 255, 255, 0.95)',
            colorTextHeading: 'rgba(255, 255, 255, 0.95)',
            headerBg: 'rgba(37, 99, 235, 0.12)',
            headerColor: 'rgba(255, 255, 255, 0.95)',
            rowHoverBg: 'rgba(37, 99, 235, 0.08)',
            borderColor: 'rgba(255, 255, 255, 0.08)',
          },
          
          // 选择框组件
          Select: {
            colorBgContainer: 'rgba(255, 255, 255, 0.05)',
            colorBorder: 'rgba(255, 255, 255, 0.15)',
            colorText: 'rgba(255, 255, 255, 0.95)',
            colorTextPlaceholder: 'rgba(255, 255, 255, 0.55)',
            borderRadius: 8,
            activeBorderColor: '#2563eb',
            hoverBorderColor: 'rgba(255, 255, 255, 0.25)',
            optionSelectedBg: 'rgba(37, 99, 235, 0.2)',
            optionActiveBg: 'rgba(37, 99, 235, 0.1)',
          },
          
          // 模态框组件
          Modal: {
            colorBgElevated: 'rgba(15, 23, 42, 0.95)',
            colorBorder: 'rgba(255, 255, 255, 0.15)',
            borderRadiusLG: 12,
            headerBg: 'rgba(37, 99, 235, 0.1)',
            titleColor: 'rgba(255, 255, 255, 0.95)',
            contentBg: 'transparent',
          },
          
          // 消息组件
          Message: {
            colorBgElevated: 'rgba(15, 23, 42, 0.95)',
            colorBorder: 'rgba(255, 255, 255, 0.15)',
            borderRadius: 8,
            colorText: 'rgba(255, 255, 255, 0.95)',
          },
          
          // 分页组件
          Pagination: {
            itemBg: 'rgba(255, 255, 255, 0.05)',
            itemActiveBg: '#2563eb',
            itemLinkBg: 'rgba(255, 255, 255, 0.05)',
            itemInputBg: 'rgba(255, 255, 255, 0.05)',
            miniOptionsSizeChangerTop: 0,
          },
          
          // 加载组件
          Spin: {
            colorPrimary: '#2563eb',
            colorWhite: '#ffffff',
          },
          
          // 布局组件
          Layout: {
            bodyBg: 'transparent',
            headerBg: 'rgba(255, 255, 255, 0.08)',
            footerBg: 'rgba(255, 255, 255, 0.05)',
            siderBg: 'rgba(255, 255, 255, 0.08)',
          },
        },
      }}
    >
      <Router 
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        }}
      >
        {isAuthenticated && passwordChangeRequired && <ChangePasswordModal />}
        <Routes>
          <Route 
            path="/login" 
            element={isAuthenticated ? <Navigate to="/dashboard" replace /> : <LoginPage />} 
          />
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <AppLayout pageTitle="控制台">
                  <DashboardPage />
                </AppLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/tools/sentiment-analysis"
            element={
              <ProtectedRoute>
                <AppLayout pageTitle="舆情分析">
                  <SentimentAnalysisTool />
                </AppLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/tools/equity-penetration"
            element={
              <ProtectedRoute>
                <AppLayout pageTitle="股权穿透分析">
                  <EquityPenetrationTool />
                </AppLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/tools/equity-pledge"
            element={
              <ProtectedRoute>
                <AppLayout pageTitle="股权质押分析">
                  <EquityPledgeTool />
                </AppLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/tools/bond-analysis"
            element={
              <ProtectedRoute>
                <AppLayout pageTitle="债券市场分析">
                  <BondAnalysisTool />
                </AppLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/tools/batch-ocr"
            element={
              <ProtectedRoute>
                <AppLayout pageTitle="批量OCR识别">
                  <BatchOCRTool />
                </AppLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/tools/batch-watermark"
            element={
              <ProtectedRoute>
                <AppLayout pageTitle="批量加水印">
                  <BatchWatermarkTool />
                </AppLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/tools/excel-merger"
            element={
              <ProtectedRoute>
                <AppLayout pageTitle="Excel合并">
                  <ExcelMergerTool />
                </AppLayout>
              </ProtectedRoute>
            }
          />
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Router>
    </ConfigProvider>
  );
};