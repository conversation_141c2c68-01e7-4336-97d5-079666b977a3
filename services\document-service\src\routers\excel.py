from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from fastapi.responses import FileResponse
from typing import List, Optional
import logging
import asyncio
from pathlib import Path
import tempfile
import os

from ..models.excel_models import (
    ExcelMergeRequest,
    ExcelMergeResponse,
    MergeMode,
    ExcelMergeConfig
)
from ..services.excel_service import ExcelService
from ..services.storage_service import StorageService

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/merge", response_model=ExcelMergeResponse)
async def merge_excel_files(
    files: List[UploadFile] = File(...),
    merge_mode: str = Form(...),
    sheet_indices: Optional[str] = Form(None),
    skip_header_rows: int = Form(0),
    skip_footer_rows: int = Form(0),
    output_filename: Optional[str] = Form(None),
    excel_service: ExcelService = Depends(lambda: ExcelService()),
    storage_service: StorageService = Depends(lambda: StorageService())
):
    """
    合并Excel文件
    
    Args:
        files: 要合并的Excel文件列表
        merge_mode: 合并模式 ('multiple_files' 或 'multiple_sheets')
        sheet_indices: 要合并的sheet索引，逗号分隔 (仅在multiple_sheets模式下使用)
        skip_header_rows: 跳过的头部行数
        skip_footer_rows: 跳过的尾部行数
        output_filename: 输出文件名
    """
    try:
        # 验证文件
        if not files:
            raise HTTPException(status_code=400, detail="至少需要上传一个文件")
        
        # 验证文件格式
        for file in files:
            if not file.filename.lower().endswith(('.xlsx', '.xls')):
                raise HTTPException(
                    status_code=400, 
                    detail=f"不支持的文件格式: {file.filename}"
                )
        
        # 解析sheet索引
        sheet_indices_list = None
        if sheet_indices:
            try:
                sheet_indices_list = [int(x.strip()) for x in sheet_indices.split(',')]
            except ValueError:
                raise HTTPException(status_code=400, detail="Sheet索引格式错误")
        
        # 创建合并配置
        config = ExcelMergeConfig(
            merge_mode=MergeMode(merge_mode),
            sheet_indices=sheet_indices_list,
            skip_header_rows=skip_header_rows,
            skip_footer_rows=skip_footer_rows,
            output_filename=output_filename
        )
        
        # 执行合并
        result = await excel_service.merge_excel_files(files, config)
        
        logger.info(f"Excel合并完成: {result.output_filename}")
        return result
        
    except Exception as e:
        logger.error(f"Excel合并失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"合并失败: {str(e)}")


@router.get("/download/{file_id}")
async def download_merged_file(
    file_id: str,
    storage_service: StorageService = Depends(lambda: StorageService())
):
    """下载合并后的Excel文件"""
    try:
        file_path = await storage_service.get_file_path(file_id)
        if not file_path or not Path(file_path).exists():
            raise HTTPException(status_code=404, detail="文件不存在")
        
        return FileResponse(
            path=file_path,
            filename=Path(file_path).name,
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        logger.error(f"文件下载失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载失败: {str(e)}")


@router.get("/preview/{file_id}")
async def preview_excel_file(
    file_id: str,
    sheet_index: int = 0,
    max_rows: int = 100,
    excel_service: ExcelService = Depends(lambda: ExcelService()),
    storage_service: StorageService = Depends(lambda: StorageService())
):
    """预览Excel文件内容"""
    try:
        file_path = await storage_service.get_file_path(file_id)
        if not file_path or not Path(file_path).exists():
            raise HTTPException(status_code=404, detail="文件不存在")
        
        preview_data = await excel_service.preview_excel_file(
            file_path, sheet_index, max_rows
        )
        
        return preview_data
        
    except Exception as e:
        logger.error(f"文件预览失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"预览失败: {str(e)}")


@router.get("/info/{file_id}")
async def get_excel_info(
    file_id: str,
    excel_service: ExcelService = Depends(lambda: ExcelService()),
    storage_service: StorageService = Depends(lambda: StorageService())
):
    """获取Excel文件信息"""
    try:
        file_path = await storage_service.get_file_path(file_id)
        if not file_path or not Path(file_path).exists():
            raise HTTPException(status_code=404, detail="文件不存在")
        
        info = await excel_service.get_excel_info(file_path)
        return info
        
    except Exception as e:
        logger.error(f"获取文件信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取信息失败: {str(e)}")


@router.get("/tools/vba-commands")
async def get_vba_commands():
    """获取VBA合并命令"""
    vba_code = '''
Sub MergeExcelFiles()
    Dim FolderPath As String
    Dim FileName As String
    Dim wb As Workbook
    Dim ws As Worksheet
    Dim MasterWB As Workbook
    Dim MasterWS As Worksheet
    Dim LastRow As Long
    
    ' 设置文件夹路径
    FolderPath = Application.GetOpenFilename("Excel Files (*.xlsx;*.xls), *.xlsx;*.xls", , "选择要合并的Excel文件", , True)
    
    If TypeName(FolderPath) = "Boolean" Then Exit Sub
    
    ' 创建新的工作簿
    Set MasterWB = Workbooks.Add
    Set MasterWS = MasterWB.Sheets(1)
    MasterWS.Name = "合并数据"
    
    ' 遍历选中的文件
    For i = LBound(FolderPath) To UBound(FolderPath)
        Set wb = Workbooks.Open(FolderPath(i))
        Set ws = wb.Sheets(1)
        
        ' 获取数据范围
        LastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
        
        ' 复制数据到主工作簿
        If MasterWS.Cells(1, 1).Value = "" Then
            ws.Range("A1:Z" & LastRow).Copy MasterWS.Range("A1")
        Else
            ws.Range("A2:Z" & LastRow).Copy MasterWS.Cells(MasterWS.Rows.Count, 1).End(xlUp).Offset(1, 0)
        End If
        
        wb.Close False
    Next i
    
    MsgBox "合并完成！"
End Sub
'''
    
    return {
        "vba_code": vba_code,
        "instructions": [
            "1. 打开Excel，按Alt+F11进入VBA编辑器",
            "2. 插入 -> 模块，粘贴上述代码",
            "3. 按F5运行宏，选择要合并的Excel文件",
            "4. 程序会自动合并所有文件的第一个工作表"
        ],
        "features": [
            "支持批量选择多个Excel文件",
            "自动跳过重复的表头",
            "保持原始数据格式",
            "生成新的合并文件"
        ]
    }


@router.get("/tools/download")
async def download_local_tool():
    """下载本地Excel合并工具"""
    # 这里可以返回一个预制的Excel文件，包含VBA宏
    return {
        "message": "本地工具下载功能开发中",
        "alternatives": [
            {
                "name": "VBA宏代码",
                "description": "使用/tools/vba-commands获取VBA代码",
                "endpoint": "/api/excel/tools/vba-commands"
            },
            {
                "name": "在线合并",
                "description": "使用当前页面的在线合并功能",
                "endpoint": "/api/excel/merge"
            }
        ]
    }
