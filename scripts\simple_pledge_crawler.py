#!/usr/bin/env python3
"""
股权质押数据爬取脚本 - 核心功能版
专为调度平台设计，只包含核心功能
功能：从AKShare获取重要股东股权质押明细数据并存储到PostgreSQL数据库

使用方法：
python simple_pledge_crawler.py

环境变量配置：
DB_HOST=localhost
DB_PORT=5432
DB_NAME=idealab
DB_USER=postgres
DB_PASSWORD=your_password

作者：IDEALAB团队
创建时间：2024-01-15
"""

import os
import sys
import logging
import pandas as pd
import psycopg2
from psycopg2.extras import execute_values
import akshare as ak
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)


class SimplePledgeCrawler:
    """简化版股权质押数据爬取器"""
    
    def __init__(self):
        """初始化"""
        self.connection = None
        self.cursor = None
        self.db_config = self._get_db_config()
    
    def _get_db_config(self):
        """获取数据库配置"""
        config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 5432)),
            'database': os.getenv('DB_NAME', 'idealab'),
            'user': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', 'password')
        }
        
        # 验证必需配置
        if not all([config['host'], config['database'], config['user'], config['password']]):
            raise ValueError("数据库配置不完整，请检查环境变量")
        
        return config
    
    def connect_db(self):
        """连接数据库"""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_db(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("数据库连接已关闭")
    
    def create_table(self):
        """创建数据表"""
        create_sql = """
        CREATE TABLE IF NOT EXISTS pledge_details (
            id SERIAL PRIMARY KEY,
            sequence_number INTEGER,
            stock_code VARCHAR(20) NOT NULL,
            stock_name VARCHAR(100) NOT NULL,
            shareholder_name VARCHAR(200) NOT NULL,
            pledged_shares BIGINT,
            pledge_ratio_of_holdings DECIMAL(10, 4),
            pledge_ratio_of_total_shares DECIMAL(10, 4),
            pledge_institution VARCHAR(200),
            latest_price DECIMAL(10, 4),
            pledge_date_closing_price DECIMAL(10, 4),
            estimated_liquidation_line DECIMAL(10, 4),
            announcement_date DATE,
            pledge_start_date DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            CONSTRAINT unique_pledge_record UNIQUE (
                stock_code, shareholder_name, pledge_start_date, announcement_date
            )
        );
        
        -- 创建索引
        CREATE INDEX IF NOT EXISTS idx_pledge_stock_code ON pledge_details(stock_code);
        CREATE INDEX IF NOT EXISTS idx_pledge_announcement_date ON pledge_details(announcement_date);
        """
        
        try:
            self.cursor.execute(create_sql)
            self.connection.commit()
            logger.info("数据表创建/检查完成")
        except Exception as e:
            logger.error(f"创建数据表失败: {e}")
            self.connection.rollback()
            raise
    
    def fetch_data(self):
        """获取AKShare数据"""
        try:
            logger.info("开始获取股权质押数据...")
            start_time = time.time()
            
            df = ak.stock_gpzy_pledge_ratio_detail_em()
            
            end_time = time.time()
            logger.info(f"数据获取完成，耗时: {end_time - start_time:.2f}秒，获取 {len(df)} 条记录")
            
            return df
        except Exception as e:
            logger.error(f"获取数据失败: {e}")
            return None
    
    def clean_data(self, df):
        """清洗数据"""
        logger.info("开始数据清洗...")
        
        # 重命名列
        column_mapping = {
            '序号': 'sequence_number',
            '股票代码': 'stock_code',
            '股票简称': 'stock_name',
            '股东名称': 'shareholder_name',
            '质押股份数量': 'pledged_shares',
            '占所持股份比例': 'pledge_ratio_of_holdings',
            '占总股本比例': 'pledge_ratio_of_total_shares',
            '质押机构': 'pledge_institution',
            '最新价': 'latest_price',
            '质押日收盘价': 'pledge_date_closing_price',
            '预估平仓线': 'estimated_liquidation_line',
            '公告日期': 'announcement_date',
            '质押开始日期': 'pledge_start_date'
        }
        
        df = df.rename(columns=column_mapping)
        
        # 数据类型转换
        try:
            # 日期字段
            df['announcement_date'] = pd.to_datetime(df['announcement_date'], errors='coerce')
            df['pledge_start_date'] = pd.to_datetime(df['pledge_start_date'], errors='coerce')
            
            # 数值字段
            numeric_cols = [
                'pledged_shares', 'pledge_ratio_of_holdings', 'pledge_ratio_of_total_shares',
                'latest_price', 'pledge_date_closing_price', 'estimated_liquidation_line'
            ]
            for col in numeric_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 文本字段
            text_cols = ['stock_code', 'stock_name', 'shareholder_name', 'pledge_institution']
            for col in text_cols:
                df[col] = df[col].astype(str).str.strip()
            
            # 移除关键字段为空的记录
            initial_count = len(df)
            df = df.dropna(subset=['stock_code', 'stock_name', 'shareholder_name'])
            final_count = len(df)
            
            if initial_count != final_count:
                logger.warning(f"移除了 {initial_count - final_count} 条无效记录")
            
            logger.info(f"数据清洗完成，有效记录: {final_count} 条")
            
        except Exception as e:
            logger.error(f"数据清洗失败: {e}")
            raise
        
        return df
    
    def insert_data(self, df):
        """插入数据到数据库"""
        try:
            logger.info("开始插入数据...")
            
            insert_sql = """
            INSERT INTO pledge_details (
                sequence_number, stock_code, stock_name, shareholder_name,
                pledged_shares, pledge_ratio_of_holdings, pledge_ratio_of_total_shares,
                pledge_institution, latest_price, pledge_date_closing_price,
                estimated_liquidation_line, announcement_date, pledge_start_date
            ) VALUES %s
            ON CONFLICT (stock_code, shareholder_name, pledge_start_date, announcement_date)
            DO UPDATE SET
                sequence_number = EXCLUDED.sequence_number,
                pledged_shares = EXCLUDED.pledged_shares,
                pledge_ratio_of_holdings = EXCLUDED.pledge_ratio_of_holdings,
                pledge_ratio_of_total_shares = EXCLUDED.pledge_ratio_of_total_shares,
                pledge_institution = EXCLUDED.pledge_institution,
                latest_price = EXCLUDED.latest_price,
                pledge_date_closing_price = EXCLUDED.pledge_date_closing_price,
                estimated_liquidation_line = EXCLUDED.estimated_liquidation_line,
                updated_at = CURRENT_TIMESTAMP
            """
            
            # 准备数据
            data_tuples = []
            for _, row in df.iterrows():
                data_tuple = (
                    int(row['sequence_number']) if pd.notna(row['sequence_number']) else None,
                    row['stock_code'],
                    row['stock_name'],
                    row['shareholder_name'],
                    int(row['pledged_shares']) if pd.notna(row['pledged_shares']) else None,
                    float(row['pledge_ratio_of_holdings']) if pd.notna(row['pledge_ratio_of_holdings']) else None,
                    float(row['pledge_ratio_of_total_shares']) if pd.notna(row['pledge_ratio_of_total_shares']) else None,
                    row['pledge_institution'],
                    float(row['latest_price']) if pd.notna(row['latest_price']) else None,
                    float(row['pledge_date_closing_price']) if pd.notna(row['pledge_date_closing_price']) else None,
                    float(row['estimated_liquidation_line']) if pd.notna(row['estimated_liquidation_line']) else None,
                    row['announcement_date'].date() if pd.notna(row['announcement_date']) else None,
                    row['pledge_start_date'].date() if pd.notna(row['pledge_start_date']) else None
                )
                data_tuples.append(data_tuple)
            
            # 批量插入
            execute_values(self.cursor, insert_sql, data_tuples, page_size=1000)
            self.connection.commit()
            
            logger.info(f"成功插入/更新 {len(data_tuples)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"数据插入失败: {e}")
            self.connection.rollback()
            return False
    
    def get_stats(self):
        """获取统计信息"""
        try:
            self.cursor.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(DISTINCT stock_code) as unique_stocks,
                    COUNT(DISTINCT shareholder_name) as unique_shareholders
                FROM pledge_details
            """)
            result = self.cursor.fetchone()
            
            if result:
                logger.info(f"数据库统计: 总记录数={result[0]}, 股票数={result[1]}, 股东数={result[2]}")
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
    
    def run(self):
        """执行完整流程"""
        start_time = time.time()
        
        try:
            logger.info("=== 股权质押数据爬取开始 ===")
            
            # 1. 连接数据库
            if not self.connect_db():
                return False
            
            # 2. 创建表
            self.create_table()
            
            # 3. 获取数据
            df = self.fetch_data()
            if df is None or df.empty:
                logger.error("未获取到有效数据")
                return False
            
            # 4. 清洗数据
            df_clean = self.clean_data(df)
            
            # 5. 插入数据
            success = self.insert_data(df_clean)
            
            # 6. 统计信息
            if success:
                self.get_stats()
            
            end_time = time.time()
            logger.info(f"=== 执行完成，总耗时: {end_time - start_time:.2f}秒 ===")
            
            return success
            
        except Exception as e:
            logger.error(f"执行失败: {e}")
            return False
        finally:
            self.close_db()


def main():
    """主函数"""
    try:
        crawler = SimplePledgeCrawler()
        success = crawler.run()
        
        if success:
            logger.info("股权质押数据更新成功")
            sys.exit(0)
        else:
            logger.error("股权质押数据更新失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"程序执行异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
