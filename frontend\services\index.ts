import { api, ApiResponse } from './api';

// NLP服务相关类型定义
export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp?: string;
}

export interface ChatRequest {
  message: string;
  history?: ChatMessage[];
  context?: string;
}

export interface ChatResponse {
  response: string;
  confidence?: number;
  sources?: string[];
}

export interface SentimentAnalysisRequest {
  texts: string[];
  language?: string;
}

export interface SentimentResult {
  text: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  confidence: number;
  emotions?: {
    joy: number;
    sadness: number;
    anger: number;
    fear: number;
    surprise: number;
  };
}

export interface SentimentAnalysisResponse {
  results: SentimentResult[];
  summary: {
    positive_count: number;
    negative_count: number;
    neutral_count: number;
    average_confidence: number;
  };
}

// 金融服务相关类型定义
export interface EquityPenetrationRequest {
  company_id: string;
  max_depth?: number;
  max_results?: number;
}

export interface EquityPenetrationResponse {
  company_name: string;
  graph_data: {
    nodes: any[];
    edges: any[];
  };
  analysis_depth: number;
  analysis_time: string;
}

export interface BondAnalysisRequest {
  bond_code: string;
  analysis_date?: string;
  analysis_type?: string[];
}

export interface BondAnalysisResponse {
  bond_code: string;
  bond_name: string;
  market_data: {
    current_price: number;
    yield_to_maturity: number;
    duration: number;
    credit_rating: string;
    trading_volume: number;
  };
  risk_metrics: {
    credit_risk: string;
    interest_rate_risk: string;
    liquidity_risk: string;
    overall_risk_score: number;
  };
  recommendations: string[];
  analysis_date: string;
}

// 股权质押相关类型定义
export interface PledgeAnalysisRequest {
  company_code: string;
  include_subsidiaries?: boolean;
  date_range?: {
    start_date: string;
    end_date: string;
  };
}

export interface PledgeRecord {
  company_code: string;
  company_name: string;
  pledger: string;
  pledgee: string;
  pledged_shares: number;
  pledged_ratio: number;
  pledge_date: string;
  release_date?: string;
  pledge_purpose?: string;
  status: 'active' | 'released' | 'expired';
}

export interface PledgeAnalysisResponse {
  company: {
    code: string;
    name: string;
    legal_representative?: string;
    registered_capital?: number;
    establishment_date?: string;
    company_type?: string;
    status?: string;
  };
  pledge_records: PledgeRecord[];
  total_pledged_ratio: number;
  active_pledges_count: number;
  risk_assessment: {
    risk_level: 'low' | 'medium' | 'high' | 'critical';
    risk_score: number;
    risk_factors: string[];
    recommendations: string[];
  };
  trend_analysis: {
    pledge_trend: 'increasing' | 'decreasing' | 'stable';
    monthly_changes: Array<{
      month: string;
      pledged_ratio: number;
      change: number;
    }>;
  };
}

export interface PledgeMarketProfile {
  date: string;
  total_companies: number;
  pledged_companies: number;
  total_pledged_shares: number;
  total_pledged_market_value: number;
  average_pledge_ratio: number;
  pledge_ratio_distribution: {
    low: number;    // 0-30%
    medium: number; // 30-50%
    high: number;   // 50-80%
    critical: number; // 80%+
  };
}

export interface CompanyPledgeRatio {
  company_code: string;
  company_name: string;
  total_shares: number;
  pledged_shares: number;
  pledge_ratio: number;
  market_value: number;
  pledged_market_value: number;
  latest_price: number;
  price_change: number;
  price_change_ratio: number;
  update_date: string;
}

export interface PledgeDetail {
  id: string;
  company_code: string;
  company_name: string;
  announcement_date: string;
  pledger: string;
  pledgee: string;
  pledge_shares: number;
  pledge_ratio: number;
  release_shares?: number;
  pledge_purpose: string;
  cumulative_pledge_ratio: number;
}

export interface InstitutionStats {
  institution_name: string;
  pledge_count: number;
  total_pledge_amount: number;
  average_pledge_amount: number;
  active_pledges: number;
  released_pledges: number;
  default_pledges: number;
  risk_ratio: number;
}

export interface CompanyInfo {
  code: string;
  name: string;
  industry?: string;
  market?: string;
}

// 文档服务相关类型定义
export interface OCRResult {
  file_name: string;
  text_content: string;
  confidence: number;
  processing_time: number;
}

export interface WatermarkRequest {
  watermark_text: string;
  position?: 'center' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  opacity?: number;
  font_size?: number;
}

export interface WatermarkResult {
  file_name: string;
  original_url: string;
  watermarked_url: string;
  processing_time: number;
}

// NLP服务API
export const nlpApi = {
  // 聊天机器人
  chat: async (request: ChatRequest): Promise<ApiResponse<ChatResponse>> => {
    return api.post('/nlp/chat', request);
  },

  // 情感分析
  sentimentAnalysis: async (request: SentimentAnalysisRequest): Promise<ApiResponse<SentimentAnalysisResponse>> => {
    return api.post('/nlp/sentiment', request);
  },
};

// 金融服务API
export const financialApi = {
  // 股权穿透分析（后端返回未包裹，前端这里统一包裹为 ApiResponse）
  equityPenetration: async (params: { company_id: string; up_depth?: number; down_depth?: number; }): Promise<ApiResponse<EquityPenetrationResponse>> => {
    // 发送空对象以避免部分代理/服务对 null body 的特殊处理
    const raw = await api.post('/financial/equity-penetration', {}, { params });
    if (raw && (raw as any).graph_data) {
      return { success: true, data: raw };
    }
    return raw as any;
  },

  // 节点展开（统一包裹）
  expandNode: async (nodeId: string, direction: 'up' | 'down'): Promise<ApiResponse<any>> => {
    const raw = await api.get(`/financial/expand-node/${nodeId}`, { params: { direction } });
    return { success: true, data: raw };
  },

  // 搜索公司（后端返回 { suggestions }, 这里统一包裹）
  searchCompanies: async (keyword: string): Promise<ApiResponse<{ suggestions: { name: string }[] }>> => {
    const raw = await api.get('/financial/search-companies', { params: { keyword } });
    if (raw && (raw as any).suggestions) {
      return { success: true, data: raw };
    }
    return raw;
  },

  // 股权质押分析
  equityPledge: async (request: PledgeAnalysisRequest): Promise<ApiResponse<PledgeAnalysisResponse>> => {
    return api.post('/financial/equity-pledge', request);
  },

  // 获取股权质押市场概况
  getPledgeMarketProfile: async (): Promise<ApiResponse<PledgeMarketProfile>> => {
    return api.get('/financial/pledge/market-profile');
  },

  // 获取公司质押比例
  getCompanyPledgeRatio: async (params: {
    date?: string;
    page?: number;
    size?: number;
  }): Promise<ApiResponse<CompanyPledgeRatio[]>> => {
    return api.get('/financial/pledge/company-ratio', { params });
  },

  // 获取重要股东质押明细
  getPledgeDetails: async (params: {
    company_code?: string;
    start_date?: string;
    end_date?: string;
    page?: number;
    size?: number;
  }): Promise<ApiResponse<PledgeDetail[]>> => {
    return api.get('/financial/pledge/details', { params });
  },

  // 获取质押机构分布统计
  getPledgeInstitutionStats: async (institution_type: 'securities' | 'bank'): Promise<ApiResponse<InstitutionStats[]>> => {
    return api.get(`/financial/pledge/institution-stats/${institution_type}`);
  },

  // 搜索质押公司
  searchPledgeCompanies: async (keyword: string): Promise<ApiResponse<{ suggestions: CompanyInfo[] }>> => {
    return api.get('/financial/pledge/search-companies', { params: { keyword } });
  },

  // 债券分析
  bondAnalysis: async (request: BondAnalysisRequest): Promise<ApiResponse<BondAnalysisResponse>> => {
    return api.post('/financial/bond-analysis', request);
  },

  // 获取公司信息（修正路径，后端是 /financial/company/...）
  getCompanyInfo: async (company_id: string): Promise<ApiResponse<any>> => {
    return api.get(`/financial/company/${company_id}/info`);
  },

  // 获取公司股东信息
  getCompanyShareholders: async (company_id: string): Promise<ApiResponse<any>> => {
    return api.get(`/financial/company/${company_id}/shareholders`);
  },

  // 获取公司投资信息
  getCompanyInvestments: async (company_id: string): Promise<ApiResponse<any>> => {
    return api.get(`/financial/company/${company_id}/investments`);
  },

  // 获取债券市场概览
  getBondMarketOverview: async (): Promise<ApiResponse<any>> => {
    return api.get('/financial/bond/market-overview');
  },

  // 获取债券投资建议
  getBondRecommendations: async (params: {
    risk_level?: string;
    investment_amount?: number;
    holding_period?: number;
  }): Promise<ApiResponse<any>> => {
    return api.get('/financial/bond/recommendations', { params });
  },
};

// 文档服务API
export const documentApi = {
  // 批量OCR
  batchOCR: async (files: File[]): Promise<ApiResponse<OCRResult[]>> => {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file);
    });
    
    return api.upload('/document/ocr', formData);
  },

  // 批量加水印
  batchWatermark: async (files: File[], watermarkConfig: WatermarkRequest): Promise<ApiResponse<WatermarkResult[]>> => {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file);
    });
    
    // 添加水印配置
    Object.entries(watermarkConfig).forEach(([key, value]) => {
      formData.append(key, value.toString());
    });
    
    return api.upload('/document/watermark', formData);
  },

  // Excel文件合并
  mergeExcel: async (files: File[], options?: {
    merge_type?: 'vertical' | 'horizontal';
    include_headers?: boolean;
    output_name?: string;
  }): Promise<ApiResponse<any>> => {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file);
    });
    
    if (options) {
      Object.entries(options).forEach(([key, value]) => {
        formData.append(key, value?.toString?.() ?? String(value));
      });
    }
    
    return api.upload('/document/excel-merge', formData);
  },
};

// 分析服务API
export const analyticsApi = {
  // 获取仪表板数据
  getDashboard: async (): Promise<ApiResponse<any>> => {
    return api.get('/financial/analytics/dashboard');
  },

  // 获取系统性能
  getPerformance: async (): Promise<ApiResponse<any>> => {
    return api.get('/financial/analytics/performance');
  },

  // 获取市场趋势
  getTrends: async (days: number = 30): Promise<ApiResponse<any>> => {
    return api.get('/financial/analytics/trends', { params: { days } });
  },

  // 生成报告
  generateReport: async (reportType: string = 'daily'): Promise<ApiResponse<any>> => {
    return api.get('/financial/analytics/reports', { params: { report_type: reportType } });
  },
};