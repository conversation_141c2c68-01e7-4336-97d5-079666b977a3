import React, { useState, useEffect } from 'react';
import { Card, Table, Progress, Badge, Tabs, Select, Space, Typography, Tag, Statistic, Row, Col, Tooltip } from 'antd';
import { BarChart3, Users, Zap, GitBranch, Layers, Eye } from 'lucide-react';

// const { TabPane } = Tabs; // Removed: TabPane is deprecated, using items prop instead
const { Title, Text } = Typography;
const { Option } = Select;

interface Community {
  id: number;
  nodes: CommunityNode[];
  size: number;
  density: number;
  totalEquity: number;
  avgEquity: number;
  dominantType: string;
  centralNode?: CommunityNode;
  cohesion: number;
  influence: number;
}

interface CommunityNode {
  id: string;
  name: string;
  type: string;
  percentage: number;
  level: number;
  community: number;
  importance: number;
  internalConnections: number;
  externalConnections: number;
}

interface CommunityAnalysisProps {
  nodes: CommunityNode[];
  onNodeSelect?: (nodeId: string) => void;
  onCommunityHighlight?: (communityId: number) => void;
  className?: string;
}

export const CommunityAnalysis: React.FC<CommunityAnalysisProps> = ({
  nodes,
  onNodeSelect,
  onCommunityHighlight,
  className = ''
}) => {
  const [communities, setCommunities] = useState<Community[]>([]);
  const [selectedCommunity, setSelectedCommunity] = useState<number | null>(null);
  const [sortBy, setSortBy] = useState<'size' | 'density' | 'totalEquity' | 'influence'>('size');
  const [filterMinSize, setFilterMinSize] = useState(2);
  const [communityStats, setCommunityStats] = useState({
    totalCommunities: 0,
    modularityScore: 0,
    avgCommunitySize: 0,
    maxCommunitySize: 0,
    isolatedNodes: 0
  });

  // 分析社群
  useEffect(() => {
    if (!nodes.length) return;

    // 按社群分组
    const communityMap = new Map<number, CommunityNode[]>();
    nodes.forEach(node => {
      if (node.community !== undefined) {
        if (!communityMap.has(node.community)) {
          communityMap.set(node.community, []);
        }
        communityMap.get(node.community)!.push(node);
      }
    });

    // 分析每个社群
    const analyzedCommunities: Community[] = [];
    
    communityMap.forEach((communityNodes, communityId) => {
      const community = analyzeCommunity(communityId, communityNodes);
      if (community.size >= filterMinSize) {
        analyzedCommunities.push(community);
      }
    });

    // 按指定字段排序
    analyzedCommunities.sort((a, b) => b[sortBy] - a[sortBy]);
    setCommunities(analyzedCommunities);

    // 计算整体统计
    const stats = calculateCommunityStats(analyzedCommunities, nodes.length);
    setCommunityStats(stats);
  }, [nodes, sortBy, filterMinSize]);

  // 分析单个社群
  const analyzeCommunity = (id: number, communityNodes: CommunityNode[]): Community => {
    const size = communityNodes.length;
    const totalEquity = communityNodes.reduce((sum, node) => sum + node.percentage, 0);
    const avgEquity = totalEquity / size;

    // 计算社群密度（内部连接密度）
    const totalInternalConnections = communityNodes.reduce((sum, node) => 
      sum + node.internalConnections, 0
    );
    const maxPossibleConnections = (size * (size - 1)) / 2;
    const density = maxPossibleConnections > 0 ? totalInternalConnections / maxPossibleConnections : 0;

    // 找出主导类型
    const typeCount = new Map<string, number>();
    communityNodes.forEach(node => {
      typeCount.set(node.type, (typeCount.get(node.type) || 0) + 1);
    });
    const dominantType = Array.from(typeCount.entries())
      .sort((a, b) => b[1] - a[1])[0]?.[0] || 'unknown';

    // 找出中心节点（重要性最高的节点）
    const centralNode = communityNodes.reduce((prev, current) =>
      (prev.importance > current.importance) ? prev : current
    );

    // 计算凝聚力（基于内部连接比例）
    const totalConnections = communityNodes.reduce((sum, node) => 
      sum + node.internalConnections + node.externalConnections, 0
    );
    const cohesion = totalConnections > 0 ? totalInternalConnections / totalConnections : 0;

    // 计算影响力（基于节点重要性和连接度）
    const influence = communityNodes.reduce((sum, node) => 
      sum + node.importance * (node.internalConnections + node.externalConnections), 0
    ) / size;

    return {
      id,
      nodes: communityNodes,
      size,
      density,
      totalEquity,
      avgEquity,
      dominantType,
      centralNode,
      cohesion,
      influence
    };
  };

  // 计算整体统计
  const calculateCommunityStats = (communities: Community[], totalNodes: number) => {
    const totalCommunities = communities.length;
    const totalCommunityNodes = communities.reduce((sum, c) => sum + c.size, 0);
    const isolatedNodes = totalNodes - totalCommunityNodes;
    const avgCommunitySize = totalCommunities > 0 ? totalCommunityNodes / totalCommunities : 0;
    const maxCommunitySize = Math.max(...communities.map(c => c.size), 0);

    // 简化的模块度计算
    const modularityScore = communities.length > 0 ? 
      communities.reduce((sum, c) => sum + c.density, 0) / communities.length : 0;

    return {
      totalCommunities,
      modularityScore,
      avgCommunitySize: Math.round(avgCommunitySize * 100) / 100,
      maxCommunitySize,
      isolatedNodes
    };
  };

  // 获取社群颜色
  const getCommunityColor = (communityId: number) => {
    const colors = [
      '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
      '#13c2c2', '#eb2f96', '#fa541c', '#a0d911', '#096dd9'
    ];
    return colors[communityId % colors.length];
  };

  // 获取类型名称
  const getTypeName = (type: string) => {
    const names: Record<string, string> = {
      parent: '母公司',
      subsidiary: '子公司',
      holding: '控股公司',
      partner: '参股公司',
      person: '个人'
    };
    return names[type] ?? type;
  };

  // 社群表格列
  const communityColumns = [
    {
      title: '社群',
      dataIndex: 'id',
      key: 'id',
      render: (id: number, record: Community) => (
        <div className="flex items-center space-x-2">
          <div 
            className="w-4 h-4 rounded-full"
            style={{ backgroundColor: getCommunityColor(id) }}
          />
          <span className="font-medium">社群 {id}</span>
          {record.centralNode && (
            <Tooltip title={`中心节点: ${record.centralNode.name}`}>
              <Badge size="small" count="C" style={{ backgroundColor: '#52c41a' }} />
            </Tooltip>
          )}
        </div>
      )
    },
    {
      title: '规模',
      dataIndex: 'size',
      key: 'size',
      render: (size: number) => (
        <Badge count={size} style={{ backgroundColor: '#1890ff' }} />
      ),
      sorter: (a: Community, b: Community) => a.size - b.size
    },
    {
      title: '密度',
      dataIndex: 'density',
      key: 'density',
      render: (density: number) => (
        <div className="flex items-center space-x-2">
          <Progress 
            percent={Math.round(density * 100)} 
            size="small" 
            strokeColor="#52c41a"
            showInfo={false}
            style={{ width: 60 }}
          />
          <span className="text-xs text-secondary">{(density * 100).toFixed(1)}%</span>
        </div>
      ),
      sorter: (a: Community, b: Community) => a.density - b.density
    },
    {
      title: '股权总和',
      dataIndex: 'totalEquity',
      key: 'totalEquity',
      render: (totalEquity: number) => (
        <span className="font-medium text-accent-primary">
          {totalEquity.toFixed(1)}%
        </span>
      ),
      sorter: (a: Community, b: Community) => a.totalEquity - b.totalEquity
    },
    {
      title: '主导类型',
      dataIndex: 'dominantType',
      key: 'dominantType',
      render: (type: string) => (
        <Tag color={getCommunityColor(type.charCodeAt(0) % 5)}>
          {getTypeName(type)}
        </Tag>
      )
    },
    {
      title: '凝聚力',
      dataIndex: 'cohesion',
      key: 'cohesion',
      render: (cohesion: number) => (
        <div className="flex items-center space-x-2">
          <Progress 
            percent={Math.round(cohesion * 100)} 
            size="small" 
            strokeColor="#722ed1"
            showInfo={false}
            style={{ width: 60 }}
          />
          <span className="text-xs text-secondary">{(cohesion * 100).toFixed(1)}%</span>
        </div>
      ),
      sorter: (a: Community, b: Community) => a.cohesion - b.cohesion
    },
    {
      title: '影响力',
      dataIndex: 'influence',
      key: 'influence',
      render: (influence: number) => (
        <span className="font-medium">{influence.toFixed(1)}</span>
      ),
      sorter: (a: Community, b: Community) => a.influence - b.influence
    }
  ];

  // 节点表格列
  const nodeColumns = [
    {
      title: '节点',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: CommunityNode) => (
        <div className="flex items-center space-x-2">
          <div 
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: getCommunityColor(record.community) }}
          />
          <span className="font-medium text-primary">{text}</span>
          <Tag color="blue">
            {record.percentage}%
          </Tag>
        </div>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={getCommunityColor(type.charCodeAt(0) % 5)}>
          {getTypeName(type)}
        </Tag>
      )
    },
    {
      title: '层级',
      dataIndex: 'level',
      key: 'level',
      render: (level: number) => (
        <Badge count={level} style={{ backgroundColor: '#52c41a' }} />
      )
    },
    {
      title: '内部连接',
      dataIndex: 'internalConnections',
      key: 'internalConnections',
      render: (count: number) => (
        <Badge count={count} style={{ backgroundColor: '#1890ff' }} />
      )
    },
    {
      title: '外部连接',
      dataIndex: 'externalConnections',
      key: 'externalConnections',
      render: (count: number) => (
        <Badge count={count} style={{ backgroundColor: '#fa8c16' }} />
      )
    },
    {
      title: '重要性',
      dataIndex: 'importance',
      key: 'importance',
      render: (importance: number) => (
        <Progress 
          percent={Math.round(importance)} 
          size="small" 
          strokeColor="#722ed1"
          style={{ width: 80 }}
        />
      )
    }
  ];

  const handleCommunitySelect = (community: Community) => {
    setSelectedCommunity(community.id);
    if (onCommunityHighlight) {
      onCommunityHighlight(community.id);
    }
  };

  const handleNodeClick = (nodeId: string) => {
    if (onNodeSelect) {
      onNodeSelect(nodeId);
    }
  };

  const selectedCommunityData = selectedCommunity !== null ? 
    communities.find(c => c.id === selectedCommunity) : null;

  return (
    <div className={`space-y-6 ${className}`}>
      <Card className="professional-card">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <BarChart3 className="text-2xl text-accent-primary" />
            <div>
              <Title level={4} className="m-0 text-primary">社群分析</Title>
              <Text className="text-secondary">
                识别网络中的社群结构和关系模式
              </Text>
            </div>
          </div>
          <Space>
            <Select
              value={sortBy}
              onChange={setSortBy}
              style={{ width: 120 }}
            >
              <Option value="size">按规模</Option>
              <Option value="density">按密度</Option>
              <Option value="totalEquity">按股权</Option>
              <Option value="influence">按影响力</Option>
            </Select>
            <Select
              value={filterMinSize}
              onChange={setFilterMinSize}
              style={{ width: 100 }}
            >
              <Option value={1}>显示全部</Option>
              <Option value={2}>≥2 节点</Option>
              <Option value={3}>≥3 节点</Option>
              <Option value={5}>≥5 节点</Option>
            </Select>
          </Space>
        </div>

        {/* 统计概览 */}
        <Card size="small" className="mb-4 bg-surface">
          <Row gutter={16}>
            <Col span={4}>
              <Statistic
                title="社群数量"
                value={communityStats.totalCommunities}
                prefix={<Users />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
            <Col span={4}>
              <Statistic
                title="平均规模"
                value={communityStats.avgCommunitySize}
                precision={1}
                prefix={<GitBranch />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col span={4}>
              <Statistic
                title="最大社群"
                value={communityStats.maxCommunitySize}
                prefix={<Layers />}
                valueStyle={{ color: '#faad14' }}
              />
            </Col>
            <Col span={4}>
              <Statistic
                title="模块度"
                value={communityStats.modularityScore}
                precision={3}
                prefix={<Zap />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Col>
            <Col span={4}>
              <Statistic
                title="孤立节点"
                value={communityStats.isolatedNodes}
                prefix={<Eye />}
                valueStyle={{ color: '#f5222d' }}
              />
            </Col>
          </Row>
        </Card>

        <Tabs 
          defaultActiveKey="communities" 
          className="professional-tabs"
          items={[
            {
              label: "社群列表",
              key: "communities",
              children: (
                <Table
                  dataSource={communities}
                  columns={communityColumns}
                  rowKey="id"
                  size="small"
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showTotal: (total, range) => 
                      `${range[0]}-${range[1]} 共 ${total} 个社群`
                  }}
                  onRow={(record) => ({
                    onClick: () => handleCommunitySelect(record),
                    style: { 
                      cursor: 'pointer',
                      backgroundColor: selectedCommunity === record.id ? 'rgba(24, 144, 255, 0.1)' : undefined
                    }
                  })}
                  className="professional-table"
                />
              )
            },
            {
              label: "社群详情",
              key: "details",
              children: (
                selectedCommunityData ? (
                  <div className="space-y-4">
                    {/* 社群信息卡片 */}
                    <Card size="small" className="bg-surface">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div 
                            className="w-6 h-6 rounded-full"
                            style={{ backgroundColor: getCommunityColor(selectedCommunityData.id) }}
                          />
                          <Title level={5} className="m-0">
                            社群 {selectedCommunityData.id}
                          </Title>
                        </div>
                        <Space>
                          <Tag color="blue">
                            {selectedCommunityData.size} 个节点
                          </Tag>
                          <Tag color="green">
                            {getTypeName(selectedCommunityData.dominantType)} 主导
                          </Tag>
                        </Space>
                      </div>

                      <Row gutter={16}>
                        <Col span={6}>
                          <Statistic
                            title="社群密度"
                            value={selectedCommunityData.density * 100}
                            precision={1}
                            suffix="%"
                            valueStyle={{ fontSize: '16px' }}
                          />
                        </Col>
                        <Col span={6}>
                          <Statistic
                            title="股权总和"
                            value={selectedCommunityData.totalEquity}
                            precision={1}
                            suffix="%"
                            valueStyle={{ fontSize: '16px' }}
                          />
                        </Col>
                        <Col span={6}>
                          <Statistic
                            title="凝聚力"
                            value={selectedCommunityData.cohesion * 100}
                            precision={1}
                            suffix="%"
                            valueStyle={{ fontSize: '16px' }}
                          />
                        </Col>
                        <Col span={6}>
                          <Statistic
                            title="影响力"
                            value={selectedCommunityData.influence}
                            precision={1}
                            valueStyle={{ fontSize: '16px' }}
                          />
                        </Col>
                      </Row>

                      {selectedCommunityData.centralNode && (
                        <div className="mt-4 p-3 bg-bg-elevated rounded-lg">
                          <Text className="text-sm font-medium text-secondary">中心节点：</Text>
                          <div className="mt-2 flex items-center space-x-2">
                            <Badge color={getCommunityColor(selectedCommunityData.id)} />
                            <span className="font-medium text-primary">
                              {selectedCommunityData.centralNode.name}
                            </span>
                            <Tag color="gold">
                              {selectedCommunityData.centralNode.percentage}%
                            </Tag>
                            <Tag color="blue">
                              重要性: {selectedCommunityData.centralNode.importance.toFixed(1)}
                            </Tag>
                          </div>
                        </div>
                      )}
                    </Card>

                    {/* 社群节点列表 */}
                    <Table
                      dataSource={selectedCommunityData.nodes}
                      columns={nodeColumns}
                      rowKey="id"
                      size="small"
                      pagination={false}
                      onRow={(record) => ({
                        onClick: () => handleNodeClick(record.id),
                        style: { cursor: 'pointer' }
                      })}
                      className="professional-table"
                    />
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Users size={48} className="text-tertiary mx-auto mb-4" />
                    <Text className="text-secondary">
                      请在社群列表中选择一个社群查看详细信息
                    </Text>
                  </div>
                )
              )
            },
            {
              label: "社群比较",
              key: "comparison",
              children: (
                <div className="space-y-4">
                  {/* 社群规模分布 */}
                  <Card size="small" title="社群规模分布" className="bg-surface">
                    <div className="space-y-2">
                      {communities.slice(0, 10).map(community => (
                        <div key={community.id} className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div 
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: getCommunityColor(community.id) }}
                            />
                            <Text className="text-sm">社群 {community.id}</Text>
                          </div>
                          <div className="flex items-center space-x-2 flex-1 mx-4">
                            <Progress
                              percent={(community.size / communityStats.maxCommunitySize) * 100}
                              size="small"
                              strokeColor={getCommunityColor(community.id)}
                              showInfo={false}
                              style={{ flex: 1 }}
                            />
                            <Text className="text-xs text-secondary w-12 text-right">
                              {community.size}
                            </Text>
                          </div>
                        </div>
                      ))}
                    </div>
                  </Card>

                  {/* 社群质量指标 */}
                  <Card size="small" title="社群质量指标" className="bg-surface">
                    <div className="space-y-3">
                      {communities.slice(0, 5).map(community => (
                        <div key={community.id} className="border-b border-border-secondary pb-3 last:border-b-0">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              <div 
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: getCommunityColor(community.id) }}
                              />
                              <Text className="font-medium">社群 {community.id}</Text>
                            </div>
                            <Text className="text-xs text-tertiary">
                              {community.size} 节点
                            </Text>
                          </div>
                          <div className="grid grid-cols-3 gap-2 text-xs">
                            <div>
                              <Text className="text-tertiary">密度</Text>
                              <div className="flex items-center space-x-1">
                                <Progress
                                  percent={community.density * 100}
                                  size="small"
                                  strokeColor="#52c41a"
                                  showInfo={false}
                                  style={{ width: 40 }}
                                />
                                <span>{(community.density * 100).toFixed(1)}%</span>
                              </div>
                            </div>
                            <div>
                              <Text className="text-tertiary">凝聚力</Text>
                              <div className="flex items-center space-x-1">
                                <Progress
                                  percent={community.cohesion * 100}
                                  size="small"
                                  strokeColor="#722ed1"
                                  showInfo={false}
                                  style={{ width: 40 }}
                                />
                                <span>{(community.cohesion * 100).toFixed(1)}%</span>
                              </div>
                            </div>
                            <div>
                              <Text className="text-tertiary">影响力</Text>
                              <div className="flex items-center space-x-1">
                                <Progress
                                  percent={Math.min(100, community.influence)}
                                  size="small"
                                  strokeColor="#faad14"
                                  showInfo={false}
                                  style={{ width: 40 }}
                                />
                                <span>{community.influence.toFixed(1)}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </Card>
                </div>
              )
            }
          ]}
        />
      </Card>
    </div>
  );
};