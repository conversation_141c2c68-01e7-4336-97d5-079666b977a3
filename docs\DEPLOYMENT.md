# 部署指南

## 快速开始

### 1. 环境准备

#### 系统要求
- **操作系统**: Linux/macOS/Windows
- **Node.js**: >= 16.0.0
- **内存**: >= 2GB
- **磁盘空间**: >= 1GB

#### 依赖安装
```bash
# 安装 Node.js (推荐使用 nvm)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# 验证安装
node --version
npm --version
```

### 2. 项目部署

#### 克隆项目
```bash
git clone https://github.com/your-org/idealab-app.git
cd idealab-app
```

#### 安装依赖
```bash
npm install
```

#### 构建项目
```bash
npm run build
```

## 生产环境部署

### 方案一：传统Web服务器部署

#### Apache 部署

1. **安装Apache**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install apache2

# CentOS/RHEL
sudo yum install httpd
```

2. **配置虚拟主机**
```apache
<VirtualHost *:80>
    ServerName idealab.yourdomain.com
    DocumentRoot /var/www/idealab/dist
    
    # 支持SPA路由
    <Directory /var/www/idealab/dist>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        # 重写规则支持前端路由
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
    
    # Gzip压缩
    LoadModule deflate_module modules/mod_deflate.so
    <Location />
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png)$ no-gzip dont-vary
    </Location>
    
    # 缓存设置
    <LocationMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 month"
    </LocationMatch>
</VirtualHost>
```

3. **部署文件**
```bash
# 复制构建文件
sudo cp -r dist/* /var/www/idealab/
sudo chown -R www-data:www-data /var/www/idealab/
sudo chmod -R 755 /var/www/idealab/

# 启用站点
sudo a2ensite idealab
sudo a2enmod rewrite
sudo systemctl reload apache2
```

#### Nginx 部署

1. **安装Nginx**
```bash
# Ubuntu/Debian
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
```

2. **配置Nginx**
```nginx
server {
    listen 80;
    server_name idealab.yourdomain.com;
    root /var/www/idealab/dist;
    index index.html;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript 
               application/javascript application/xml+rss 
               application/json application/xml;
    
    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1M;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理（如果需要）
    location /api/ {
        proxy_pass http://backend-server:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

3. **部署和启动**
```bash
# 复制文件
sudo cp -r dist/* /var/www/idealab/
sudo chown -R nginx:nginx /var/www/idealab/

# 启用配置
sudo ln -s /etc/nginx/sites-available/idealab /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 方案二：Docker 部署

#### 1. 创建 Dockerfile
```dockerfile
# 多阶段构建
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# 生产镜像
FROM nginx:1.21-alpine

# 复制构建文件
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制Nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

#### 2. 创建 nginx.conf
```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    sendfile        on;
    keepalive_timeout  65;
    
    # Gzip配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    server {
        listen       80;
        server_name  localhost;
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        
        # 前端路由支持
        location / {
            try_files $uri $uri/ /index.html;
        }
        
        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   /usr/share/nginx/html;
        }
    }
}
```

#### 3. 构建和运行
```bash
# 构建镜像
docker build -t idealab-app:latest .

# 运行容器
docker run -d \
  --name idealab-app \
  -p 80:80 \
  --restart unless-stopped \
  idealab-app:latest

# 查看运行状态
docker ps
docker logs idealab-app
```

#### 4. Docker Compose 部署
```yaml
version: '3.8'

services:
  idealab-app:
    build: .
    ports:
      - "80:80"
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    volumes:
      - ./logs:/var/log/nginx
    networks:
      - idealab-network

  # 如果需要后端API服务
  idealab-api:
    image: idealab-api:latest
    ports:
      - "3001:3001"
    environment:
      - DATABASE_URL=******************************/idealab
    depends_on:
      - db
    networks:
      - idealab-network

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: idealab
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - idealab-network

volumes:
  postgres_data:

networks:
  idealab-network:
    driver: bridge
```

### 方案三：云平台部署

#### Vercel 部署

1. **安装 Vercel CLI**
```bash
npm install -g vercel
```

2. **配置 vercel.json**
```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist"
      }
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ],
  "env": {
    "VITE_API_BASE_URL": "https://api.idealab.com"
  }
}
```

3. **部署**
```bash
vercel --prod
```

#### Netlify 部署

1. **安装 Netlify CLI**
```bash
npm install -g netlify-cli
```

2. **配置 netlify.toml**
```toml
[build]
  publish = "dist"
  command = "npm run build"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[build.environment]
  VITE_API_BASE_URL = "https://api.idealab.com"
```

3. **部署**
```bash
npm run build
netlify deploy --prod --dir=dist
```

#### AWS S3 + CloudFront 部署

1. **构建项目**
```bash
npm run build
```

2. **上传到S3**
```bash
aws s3 sync dist/ s3://your-bucket-name --delete
```

3. **CloudFront配置**
```json
{
  "Origins": [{
    "DomainName": "your-bucket.s3.amazonaws.com",
    "Id": "S3-idealab-app",
    "S3OriginConfig": {
      "OriginAccessIdentity": ""
    }
  }],
  "DefaultCacheBehavior": {
    "TargetOriginId": "S3-idealab-app",
    "ViewerProtocolPolicy": "redirect-to-https",
    "Compress": true,
    "CachePolicyId": "managed-caching-optimized"
  },
  "CustomErrorResponses": [{
    "ErrorCode": 404,
    "ResponseCode": 200,
    "ResponsePagePath": "/index.html"
  }]
}
```

## SSL证书配置

### Let's Encrypt 免费证书

```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 申请证书
sudo certbot --nginx -d idealab.yourdomain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 监控和日志

### 1. 应用监控

#### 使用 PM2 (Node.js应用)
```bash
npm install -g pm2

# 启动应用
pm2 start npm --name "idealab-app" -- start

# 监控
pm2 monit

# 查看日志
pm2 logs idealab-app
```

### 2. 服务器监控

```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 查看资源使用情况
htop
free -h
df -h
```

### 3. 日志收集

```bash
# 配置日志轮转
sudo nano /etc/logrotate.d/idealab
```

```
/var/log/idealab/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

## 备份策略

### 1. 文件备份
```bash
#!/bin/bash
# backup.sh
BACKUP_DIR="/backup/idealab/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# 备份应用文件
tar -czf $BACKUP_DIR/app.tar.gz /var/www/idealab/

# 备份配置文件
cp /etc/nginx/sites-available/idealab $BACKUP_DIR/
```

### 2. 数据库备份
```bash
# PostgreSQL
pg_dump -h localhost -U username idealab > backup.sql

# MySQL
mysqldump -u username -p idealab > backup.sql
```

## 性能优化

### 1. 前端优化
- 启用Gzip压缩
- 配置静态资源缓存
- 使用CDN加速
- 图片懒加载
- 代码分割

### 2. 服务器优化
```nginx
# Nginx优化配置
worker_processes auto;
worker_connections 1024;

# 开启sendfile
sendfile on;

# 开启TCP优化
tcp_nopush on;
tcp_nodelay on;

# 调整缓冲区大小
client_body_buffer_size 128k;
client_max_body_size 50m;
```

## 故障排除

### 常见问题

1. **页面刷新404错误**
   - 检查Web服务器路由配置
   - 确保支持SPA历史模式

2. **静态资源加载失败**
   - 检查资源路径配置
   - 验证文件权限

3. **API请求跨域**
   - 配置CORS头部
   - 设置API代理

### 调试命令

```bash
# 检查端口占用
netstat -tlnp | grep :80

# 检查服务状态
systemctl status nginx
systemctl status apache2

# 查看错误日志
tail -f /var/log/nginx/error.log
tail -f /var/log/apache2/error.log
```

## 安全配置

### 1. 防火墙设置
```bash
# UFW配置
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 2. 安全头部
```nginx
# Nginx安全配置
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
```

通过以上部署指南，您可以根据具体需求选择合适的部署方案，确保IDEALAB应用实验室稳定、安全地运行在生产环境中。