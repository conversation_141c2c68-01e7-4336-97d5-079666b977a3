import React, { useState, useEffect } from 'react';
import { Typography } from 'antd';
import { Clock as ClockIcon } from 'lucide-react';

const { Text } = Typography;

export const Clock: React.FC = () => {
  const [time, setTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => setTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  const formatDate = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long',
    };
    return date.toLocaleDateString('zh-CN', options);
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
      <ClockIcon size={18} />
      <Text style={{ fontSize: '14px' }}>
        {formatDate(time)} {time.toLocaleTimeString('zh-CN')}
      </Text>
    </div>
  );
}; 