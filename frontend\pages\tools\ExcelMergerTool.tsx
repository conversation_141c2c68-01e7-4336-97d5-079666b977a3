import React, { useState, useCallback } from 'react';
import {
    Layout, Typography, Button, Statistic, message, Upload,
    Card, Radio, InputNumber, Input, Progress, List, Tag,
    Space, Divider, Modal, Tabs, Alert, Tooltip
} from 'antd';
import {
    FileSpreadsheet, Upload as UploadIcon, Merge, Download,
    Settings, Eye, Code, HelpCircle, X, Plus, FileText
} from 'lucide-react';
import { GlassmorphicCard } from '@/components/GlassmorphicCard';

const { Sider, Content } = Layout;
const { Title, Text, Paragraph } = Typography;
const { Dragger } = Upload;
const { TabPane } = Tabs;

interface ExcelFile {
    uid: string;
    name: string;
    size: number;
    file: File;
    sheets?: string[];
}

interface MergeConfig {
    mode: 'multiple_files' | 'multiple_sheets';
    sheetIndices: number[];
    skipHeaderRows: number;
    skipFooterRows: number;
    outputFilename: string;
}

export const ExcelMergerTool: React.FC = () => {
    const [files, setFiles] = useState<ExcelFile[]>([]);
    const [merging, setMerging] = useState(false);
    const [progress, setProgress] = useState(0);
    const [config, setConfig] = useState<MergeConfig>({
        mode: 'multiple_files',
        sheetIndices: [0],
        skipHeaderRows: 0,
        skipFooterRows: 0,
        outputFilename: ''
    });
    const [showVBAModal, setShowVBAModal] = useState(false);
    const [showPreview, setShowPreview] = useState(false);
    const [showDownloadModal, setShowDownloadModal] = useState(false);
    const [mergeResult, setMergeResult] = useState<any>(null);

    const handleFileUpload = useCallback((info: any) => {
        const { fileList } = info;
        const validFiles = fileList.filter((file: any) => {
            const isExcel = file.name.toLowerCase().endsWith('.xlsx') || file.name.toLowerCase().endsWith('.xls');
            if (!isExcel) {
                message.error(`${file.name} 不是有效的Excel文件`);
                return false;
            }
            return true;
        });

        setFiles(validFiles.map((file: any) => ({
            uid: file.uid,
            name: file.name,
            size: file.size,
            file: file.originFileObj || file,
            sheets: [] // 实际使用时需要解析Excel获取sheet信息
        })));
    }, []);

    const handleRemoveFile = (uid: string) => {
        setFiles(files.filter(file => file.uid !== uid));
    };

    const handleMerge = async () => {
        if (files.length === 0) {
            message.error('请先上传Excel文件');
            return;
        }

        if (config.mode === 'multiple_sheets' && files.length > 1) {
            message.error('多Sheet合并模式只能选择一个文件');
            return;
        }

        setMerging(true);
        setProgress(0);

        try {
            // 模拟合并进度
            const progressInterval = setInterval(() => {
                setProgress(prev => {
                    if (prev >= 90) {
                        clearInterval(progressInterval);
                        return 90;
                    }
                    return prev + 10;
                });
            }, 200);

            // 准备FormData
            const formData = new FormData();
            files.forEach(file => {
                formData.append('files', file.file);
            });
            formData.append('merge_mode', config.mode);
            formData.append('sheet_indices', config.sheetIndices.join(','));
            formData.append('skip_header_rows', config.skipHeaderRows.toString());
            formData.append('skip_footer_rows', config.skipFooterRows.toString());
            if (config.outputFilename) {
                formData.append('output_filename', config.outputFilename);
            }

            // 发送请求到后端
            const response = await fetch('/api/document/excel/merge', {
                method: 'POST',
                body: formData
            });

            clearInterval(progressInterval);
            setProgress(100);

            if (response.ok) {
                const result = await response.json();
                setMergeResult(result);
                message.success('Excel合并完成！');
            } else {
                const error = await response.json();
                message.error(`合并失败: ${error.detail}`);
            }
        } catch (error) {
            message.error('合并过程中发生错误，请稍后重试');
            console.error('Merge error:', error);
        } finally {
            setMerging(false);
            setTimeout(() => setProgress(0), 2000);
        }
    };

    const downloadResult = () => {
        if (mergeResult?.download_url) {
            window.open(mergeResult.download_url, '_blank');
        }
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const vbaCode = `' Excel暴力合并工具 - VBA版本
' 功能：批量合并多个Excel文件的第一个工作表
' 作者：IDEALAB团队
' 版本：1.0

Sub MergeExcelFiles()
    Dim FolderPath As Variant
    Dim FileName As String
    Dim wb As Workbook
    Dim ws As Worksheet
    Dim MasterWB As Workbook
    Dim MasterWS As Worksheet
    Dim LastRow As Long
    Dim LastCol As Long
    Dim i As Integer
    Dim SkipHeaderRows As Integer
    Dim SkipFooterRows As Integer

    ' 配置参数
    SkipHeaderRows = InputBox("跳过头部行数（默认0）:", "配置", "0")
    SkipFooterRows = InputBox("跳过尾部行数（默认0）:", "配置", "0")

    If SkipHeaderRows = "" Then SkipHeaderRows = 0
    If SkipFooterRows = "" Then SkipFooterRows = 0

    ' 选择要合并的Excel文件
    FolderPath = Application.GetOpenFilename( _
        "Excel Files (*.xlsx;*.xls), *.xlsx;*.xls", , _
        "选择要合并的Excel文件（可多选）", , True)

    If TypeName(FolderPath) = "Boolean" Then
        MsgBox "未选择文件，操作取消。"
        Exit Sub
    End If

    ' 创建新的工作簿
    Set MasterWB = Workbooks.Add
    Set MasterWS = MasterWB.Sheets(1)
    MasterWS.Name = "合并数据"

    ' 设置表头样式
    With MasterWS.Range("A1:Z1")
        .Font.Bold = True
        .Interior.Color = RGB(79, 129, 189)
        .Font.Color = RGB(255, 255, 255)
    End With

    Application.ScreenUpdating = False
    Application.DisplayAlerts = False

    ' 遍历选中的文件
    For i = LBound(FolderPath) To UBound(FolderPath)
        Application.StatusBar = "正在处理文件 " & i & "/" & UBound(FolderPath) & ": " & Dir(FolderPath(i))

        Set wb = Workbooks.Open(FolderPath(i))
        Set ws = wb.Sheets(1)

        ' 获取数据范围
        LastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
        LastCol = ws.Cells(1, ws.Columns.Count).End(xlToLeft).Column

        ' 应用跳过行数配置
        Dim StartRow As Long, EndRow As Long
        StartRow = 1 + SkipHeaderRows
        EndRow = LastRow - SkipFooterRows

        If EndRow >= StartRow Then
            ' 复制数据到主工作簿
            If MasterWS.Cells(1, 1).Value = "" Then
                ' 第一个文件，包含表头
                ws.Range(ws.Cells(StartRow, 1), ws.Cells(EndRow, LastCol)).Copy _
                    MasterWS.Range("A1")
            Else
                ' 后续文件，跳过表头
                If StartRow < EndRow Then
                    ws.Range(ws.Cells(StartRow + 1, 1), ws.Cells(EndRow, LastCol)).Copy _
                        MasterWS.Cells(MasterWS.Rows.Count, 1).End(xlUp).Offset(1, 0)
                End If
            End If
        End If

        wb.Close False
    Next i

    ' 自动调整列宽
    MasterWS.Columns.AutoFit

    ' 添加边框
    With MasterWS.UsedRange
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlThin
    End With

    Application.ScreenUpdating = True
    Application.DisplayAlerts = True
    Application.StatusBar = False

    MsgBox "合并完成！共处理 " & UBound(FolderPath) & " 个文件。", vbInformation, "Excel合并工具"
End Sub

' 合并单个文件的多个工作表
Sub MergeMultipleSheets()
    Dim wb As Workbook
    Dim ws As Worksheet
    Dim MasterWS As Worksheet
    Dim LastRow As Long
    Dim LastCol As Long
    Dim SheetCount As Integer
    Dim i As Integer

    Set wb = ActiveWorkbook
    SheetCount = wb.Sheets.Count

    If SheetCount < 2 Then
        MsgBox "当前工作簿只有一个工作表，无需合并。"
        Exit Sub
    End If

    ' 创建合并工作表
    Set MasterWS = wb.Sheets.Add(Before:=wb.Sheets(1))
    MasterWS.Name = "合并数据_" & Format(Now, "yyyymmdd_hhmmss")

    Application.ScreenUpdating = False

    ' 遍历所有工作表
    For i = 2 To wb.Sheets.Count
        Set ws = wb.Sheets(i)

        If ws.Name <> MasterWS.Name Then
            LastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
            LastCol = ws.Cells(1, ws.Columns.Count).End(xlToLeft).Column

            If LastRow > 0 Then
                If MasterWS.Cells(1, 1).Value = "" Then
                    ' 第一个工作表，包含表头
                    ws.Range(ws.Cells(1, 1), ws.Cells(LastRow, LastCol)).Copy _
                        MasterWS.Range("A1")
                Else
                    ' 后续工作表，跳过表头
                    If LastRow > 1 Then
                        ws.Range(ws.Cells(2, 1), ws.Cells(LastRow, LastCol)).Copy _
                            MasterWS.Cells(MasterWS.Rows.Count, 1).End(xlUp).Offset(1, 0)
                    End If
                End If
            End If
        End If
    Next i

    ' 格式化合并结果
    With MasterWS.Range("A1:Z1")
        .Font.Bold = True
        .Interior.Color = RGB(79, 129, 189)
        .Font.Color = RGB(255, 255, 255)
    End With

    MasterWS.Columns.AutoFit

    With MasterWS.UsedRange
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlThin
    End With

    Application.ScreenUpdating = True

    MsgBox "工作表合并完成！", vbInformation, "Excel合并工具"
End Sub`;

    return (
        <div style={{ width: '100%', height: '100%', minHeight: '600px', position: 'relative' }}>
            <Layout style={{ background: 'transparent', padding: '16px', height: '100%', minHeight: '600px' }}>
                <Sider width={320} style={{ background: 'transparent', marginRight: '16px' }}>
                    <GlassmorphicCard title={<Title level={5} className="!text-white">控制面板</Title>}>
                        {/* 统计信息 */}
                        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '20px' }}>
                            <Statistic
                                title={<Text className="!text-gray-300">待合并文件</Text>}
                                value={files.length}
                                prefix={<FileSpreadsheet size={16} />}
                                valueStyle={{ color: '#fff', fontSize: '18px' }}
                            />
                            <Statistic
                                title={<Text className="!text-gray-300">总大小</Text>}
                                value={formatFileSize(files.reduce((sum, file) => sum + file.size, 0))}
                                prefix={<FileText size={16} />}
                                valueStyle={{ color: '#fff', fontSize: '14px' }}
                            />
                        </div>

                        {/* 合并配置 */}
                        <Title level={5} className="!text-white" style={{ marginTop: '24px', marginBottom: '16px' }}>
                            合并配置
                        </Title>

                        <div style={{ marginBottom: '16px' }}>
                            <Text className="!text-gray-300" style={{ display: 'block', marginBottom: '8px' }}>
                                合并模式
                            </Text>
                            <Radio.Group
                                value={config.mode}
                                onChange={(e) => setConfig({...config, mode: e.target.value})}
                                style={{ width: '100%' }}
                            >
                                <Radio value="multiple_files" style={{ color: 'white', display: 'block', marginBottom: '8px' }}>
                                    多文件合并
                                </Radio>
                                <Radio value="multiple_sheets" style={{ color: 'white', display: 'block' }}>
                                    多Sheet合并
                                </Radio>
                            </Radio.Group>
                        </div>

                        <div style={{ marginBottom: '16px' }}>
                            <Text className="!text-gray-300" style={{ display: 'block', marginBottom: '8px' }}>
                                跳过头部行数
                            </Text>
                            <InputNumber
                                min={0}
                                max={100}
                                value={config.skipHeaderRows}
                                onChange={(value) => setConfig({...config, skipHeaderRows: value || 0})}
                                style={{ width: '100%' }}
                            />
                        </div>

                        <div style={{ marginBottom: '16px' }}>
                            <Text className="!text-gray-300" style={{ display: 'block', marginBottom: '8px' }}>
                                跳过尾部行数
                            </Text>
                            <InputNumber
                                min={0}
                                max={100}
                                value={config.skipFooterRows}
                                onChange={(value) => setConfig({...config, skipFooterRows: value || 0})}
                                style={{ width: '100%' }}
                            />
                        </div>

                        <div style={{ marginBottom: '20px' }}>
                            <Text className="!text-gray-300" style={{ display: 'block', marginBottom: '8px' }}>
                                输出文件名
                            </Text>
                            <Input
                                placeholder="留空自动生成"
                                value={config.outputFilename}
                                onChange={(e) => setConfig({...config, outputFilename: e.target.value})}
                                style={{ width: '100%' }}
                            />
                        </div>

                        {/* 进度条 */}
                        {merging && (
                            <div style={{ marginBottom: '20px' }}>
                                <Text className="!text-gray-300" style={{ display: 'block', marginBottom: '8px' }}>
                                    合并进度
                                </Text>
                                <Progress percent={progress} status={progress === 100 ? 'success' : 'active'} />
                            </div>
                        )}

                        {/* 操作按钮 */}
                        <Title level={5} className="!text-white" style={{ marginTop: '24px', marginBottom: '16px' }}>
                            操作
                        </Title>
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                            <Button
                                type="primary"
                                icon={<Merge size={16} />}
                                onClick={handleMerge}
                                loading={merging}
                                disabled={files.length === 0}
                                style={{ width: '100%' }}
                            >
                                {merging ? '合并中...' : '开始合并'}
                            </Button>

                            <Button
                                icon={<Download size={16} />}
                                onClick={downloadResult}
                                disabled={!mergeResult}
                                style={{ width: '100%' }}
                            >
                                下载结果
                            </Button>

                            <Divider style={{ margin: '16px 0', borderColor: 'rgba(255,255,255,0.2)' }} />

                            <Button
                                icon={<Code size={16} />}
                                onClick={() => setShowVBAModal(true)}
                                style={{ width: '100%' }}
                            >
                                查看VBA代码
                            </Button>

                            <Button
                                icon={<Download size={16} />}
                                onClick={() => setShowDownloadModal(true)}
                                style={{ width: '100%' }}
                            >
                                下载本地工具
                            </Button>
                        </div>
                    </GlassmorphicCard>
                </Sider>

                <Content style={{ position: 'relative' }}>
                    <GlassmorphicCard style={{ padding: 0, height: '100%' }}>
                        <Tabs defaultActiveKey="upload" style={{ height: '100%' }}>
                            <TabPane tab="文件上传" key="upload" style={{ height: '100%' }}>
                                <div style={{ padding: '24px', height: '100%' }}>
                                    {files.length === 0 ? (
                                        <div style={{
                                            height: '100%',
                                            display: 'flex',
                                            flexDirection: 'column',
                                            justifyContent: 'center',
                                            alignItems: 'center'
                                        }}>
                                            <Dragger
                                                multiple
                                                accept=".xlsx,.xls"
                                                beforeUpload={() => false}
                                                onChange={handleFileUpload}
                                                style={{
                                                    background: 'rgba(255, 255, 255, 0.05)',
                                                    border: '2px dashed rgba(255, 255, 255, 0.3)',
                                                    borderRadius: '12px',
                                                    padding: '48px 24px',
                                                    width: '100%',
                                                    maxWidth: '500px'
                                                }}
                                            >
                                                <p className="ant-upload-drag-icon">
                                                    <FileSpreadsheet size={48} style={{ color: '#22d3ee' }} />
                                                </p>
                                                <p style={{ color: 'white', fontSize: '18px', fontWeight: 600, margin: '16px 0 8px' }}>
                                                    拖拽Excel文件到此处
                                                </p>
                                                <p style={{ color: 'rgba(255, 255, 255, 0.7)', fontSize: '14px', margin: 0 }}>
                                                    支持 .xlsx 和 .xls 格式，可同时选择多个文件
                                                </p>
                                            </Dragger>

                                            <div style={{ marginTop: '32px', textAlign: 'center' }}>
                                                <Title level={4} className="!text-white" style={{ marginBottom: '12px' }}>
                                                    Excel暴力合并工具
                                                </Title>
                                                <Paragraph style={{
                                                    color: 'rgba(255, 255, 255, 0.7)',
                                                    fontSize: '14px',
                                                    maxWidth: '400px',
                                                    lineHeight: 1.6,
                                                    margin: 0
                                                }}>
                                                    智能合并多个Excel文件或单个文件的多个工作表，支持自定义跳过行数和输出格式
                                                </Paragraph>
                                            </div>
                                        </div>
                                    ) : (
                                        <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                                            <div style={{
                                                display: 'flex',
                                                justifyContent: 'space-between',
                                                alignItems: 'center',
                                                marginBottom: '20px'
                                            }}>
                                                <Title level={4} className="!text-white" style={{ margin: 0 }}>
                                                    已选择文件 ({files.length})
                                                </Title>
                                                <Button
                                                    icon={<Plus size={16} />}
                                                    onClick={() => {
                                                        const input = document.createElement('input');
                                                        input.type = 'file';
                                                        input.multiple = true;
                                                        input.accept = '.xlsx,.xls';
                                                        input.onchange = (e: any) => {
                                                            const newFiles = Array.from(e.target.files).map((file: any) => ({
                                                                uid: Date.now() + Math.random(),
                                                                name: file.name,
                                                                size: file.size,
                                                                file: file,
                                                                sheets: []
                                                            }));
                                                            setFiles([...files, ...newFiles]);
                                                        };
                                                        input.click();
                                                    }}
                                                >
                                                    添加文件
                                                </Button>
                                            </div>

                                            <div style={{ flex: 1, overflow: 'auto' }}>
                                                <List
                                                    dataSource={files}
                                                    renderItem={(file) => (
                                                        <List.Item
                                                            style={{
                                                                background: 'rgba(255, 255, 255, 0.05)',
                                                                border: '1px solid rgba(255, 255, 255, 0.1)',
                                                                borderRadius: '8px',
                                                                padding: '16px',
                                                                marginBottom: '12px'
                                                            }}
                                                            actions={[
                                                                <Tooltip title="预览文件">
                                                                    <Button
                                                                        type="text"
                                                                        icon={<Eye size={16} />}
                                                                        style={{ color: '#22d3ee' }}
                                                                        onClick={() => setShowPreview(true)}
                                                                    />
                                                                </Tooltip>,
                                                                <Tooltip title="移除文件">
                                                                    <Button
                                                                        type="text"
                                                                        icon={<X size={16} />}
                                                                        style={{ color: '#ef4444' }}
                                                                        onClick={() => handleRemoveFile(file.uid)}
                                                                    />
                                                                </Tooltip>
                                                            ]}
                                                        >
                                                            <List.Item.Meta
                                                                avatar={<FileSpreadsheet size={24} style={{ color: '#22d3ee' }} />}
                                                                title={
                                                                    <Text style={{ color: 'white', fontWeight: 600 }}>
                                                                        {file.name}
                                                                    </Text>
                                                                }
                                                                description={
                                                                    <Space>
                                                                        <Tag color="blue">{formatFileSize(file.size)}</Tag>
                                                                        {file.sheets && file.sheets.length > 0 && (
                                                                            <Tag color="green">{file.sheets.length} sheets</Tag>
                                                                        )}
                                                                    </Space>
                                                                }
                                                            />
                                                        </List.Item>
                                                    )}
                                                />
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </TabPane>

                            <TabPane tab="合并结果" key="result" style={{ height: '100%' }}>
                                <div style={{ padding: '24px', height: '100%' }}>
                                    {mergeResult ? (
                                        <div>
                                            <Alert
                                                message="合并完成"
                                                description={`成功合并 ${mergeResult.merged_files_count} 个文件，共 ${mergeResult.total_rows} 行数据`}
                                                type="success"
                                                showIcon
                                                style={{ marginBottom: '24px' }}
                                            />

                                            <Card
                                                title="合并结果详情"
                                                style={{
                                                    background: 'rgba(255, 255, 255, 0.05)',
                                                    border: '1px solid rgba(255, 255, 255, 0.1)'
                                                }}
                                                headStyle={{ color: 'white', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}
                                                bodyStyle={{ color: 'white' }}
                                            >
                                                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                                                    <Statistic
                                                        title="输出文件"
                                                        value={mergeResult.output_filename}
                                                        valueStyle={{ color: 'white', fontSize: '16px' }}
                                                    />
                                                    <Statistic
                                                        title="文件大小"
                                                        value={formatFileSize(mergeResult.file_size)}
                                                        valueStyle={{ color: 'white', fontSize: '16px' }}
                                                    />
                                                    <Statistic
                                                        title="处理时间"
                                                        value={`${mergeResult.processing_time.toFixed(2)}s`}
                                                        valueStyle={{ color: 'white', fontSize: '16px' }}
                                                    />
                                                    <Statistic
                                                        title="总行数"
                                                        value={mergeResult.total_rows}
                                                        valueStyle={{ color: 'white', fontSize: '16px' }}
                                                    />
                                                </div>

                                                <div style={{ marginTop: '24px', textAlign: 'center' }}>
                                                    <Button
                                                        type="primary"
                                                        size="large"
                                                        icon={<Download size={20} />}
                                                        onClick={downloadResult}
                                                    >
                                                        下载合并文件
                                                    </Button>
                                                </div>
                                            </Card>
                                        </div>
                                    ) : (
                                        <div style={{
                                            height: '100%',
                                            display: 'flex',
                                            flexDirection: 'column',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            textAlign: 'center'
                                        }}>
                                            <Merge size={64} style={{ color: 'rgba(255, 255, 255, 0.3)', marginBottom: '24px' }} />
                                            <Title level={4} className="!text-white" style={{ marginBottom: '12px' }}>
                                                暂无合并结果
                                            </Title>
                                            <Text style={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                                                上传Excel文件并点击"开始合并"后，结果将显示在这里
                                            </Text>
                                        </div>
                                    )}
                                </div>
                            </TabPane>
                        </Tabs>
                    </GlassmorphicCard>
                </Content>
            </Layout>

            {/* VBA代码模态框 */}
            <Modal
                title="Excel VBA合并工具"
                open={showVBAModal}
                onCancel={() => setShowVBAModal(false)}
                footer={[
                    <Button key="copy" onClick={() => {
                        navigator.clipboard.writeText(vbaCode);
                        message.success('VBA代码已复制到剪贴板');
                    }}>
                        复制代码
                    </Button>,
                    <Button key="download" onClick={() => {
                        const blob = new Blob([vbaCode], { type: 'text/plain' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'ExcelMerger.bas';
                        a.click();
                        URL.revokeObjectURL(url);
                        message.success('VBA文件已下载');
                    }}>
                        下载.bas文件
                    </Button>,
                    <Button key="close" type="primary" onClick={() => setShowVBAModal(false)}>
                        关闭
                    </Button>
                ]}
                width={900}
            >
                <Tabs defaultActiveKey="instructions">
                    <TabPane tab="使用说明" key="instructions">
                        <div style={{ marginBottom: '16px' }}>
                            <Alert
                                message="VBA宏使用指南"
                                description={
                                    <div>
                                        <h4>安装步骤：</h4>
                                        <ol style={{ margin: '8px 0', paddingLeft: '20px' }}>
                                            <li>打开Excel，按 <code>Alt+F11</code> 进入VBA编辑器</li>
                                            <li>在项目资源管理器中右键点击 → 插入 → 模块</li>
                                            <li>将VBA代码粘贴到新建的模块中</li>
                                            <li>保存文件（建议保存为.xlsm格式以保留宏）</li>
                                        </ol>

                                        <h4>功能说明：</h4>
                                        <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                                            <li><strong>MergeExcelFiles</strong>：合并多个Excel文件的第一个工作表</li>
                                            <li><strong>MergeMultipleSheets</strong>：合并当前工作簿的多个工作表</li>
                                            <li>支持自定义跳过头部和尾部行数</li>
                                            <li>自动格式化合并结果（边框、表头样式等）</li>
                                            <li>显示处理进度和完成提示</li>
                                        </ul>

                                        <h4>运行方式：</h4>
                                        <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                                            <li>按 <code>F5</code> 运行选中的宏</li>
                                            <li>或者在Excel中按 <code>Alt+F8</code> 打开宏对话框运行</li>
                                            <li>也可以在开发工具选项卡中点击"宏"按钮</li>
                                        </ul>
                                    </div>
                                }
                                type="info"
                                showIcon
                            />
                        </div>
                    </TabPane>

                    <TabPane tab="VBA代码" key="code">
                        <div style={{ marginBottom: '12px', display: 'flex', gap: '8px' }}>
                            <Tag color="blue">支持.xlsx/.xls格式</Tag>
                            <Tag color="green">自动格式化</Tag>
                            <Tag color="orange">进度显示</Tag>
                            <Tag color="purple">多种合并模式</Tag>
                        </div>
                        <pre style={{
                            background: '#f5f5f5',
                            padding: '16px',
                            borderRadius: '4px',
                            fontSize: '11px',
                            lineHeight: '1.3',
                            overflow: 'auto',
                            maxHeight: '500px',
                            border: '1px solid #d9d9d9'
                        }}>
                            {vbaCode}
                        </pre>
                    </TabPane>

                    <TabPane tab="系统要求" key="requirements">
                        <div>
                            <Alert
                                message="系统要求和注意事项"
                                description={
                                    <div>
                                        <h4>系统要求：</h4>
                                        <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                                            <li>Microsoft Excel 2010 或更高版本</li>
                                            <li>Windows 7 或更高版本</li>
                                            <li>启用宏功能（文件 → 选项 → 信任中心 → 宏设置）</li>
                                        </ul>

                                        <h4>注意事项：</h4>
                                        <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                                            <li>处理大文件时请确保有足够的内存</li>
                                            <li>建议在合并前备份原始文件</li>
                                            <li>合并过程中请勿操作Excel，避免程序中断</li>
                                            <li>如遇到权限问题，请以管理员身份运行Excel</li>
                                        </ul>

                                        <h4>性能建议：</h4>
                                        <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                                            <li>单次合并文件数量建议不超过100个</li>
                                            <li>单个文件大小建议不超过50MB</li>
                                            <li>合并前关闭不必要的Excel工作簿</li>
                                        </ul>
                                    </div>
                                }
                                type="warning"
                                showIcon
                            />
                        </div>
                    </TabPane>
                </Tabs>
            </Modal>

            {/* 本地工具下载模态框 */}
            <Modal
                title="下载本地Excel合并工具"
                open={showDownloadModal}
                onCancel={() => setShowDownloadModal(false)}
                footer={[
                    <Button key="close" onClick={() => setShowDownloadModal(false)}>
                        关闭
                    </Button>
                ]}
                width={700}
            >
                <Tabs defaultActiveKey="standalone">
                    <TabPane tab="独立工具" key="standalone">
                        <div style={{ textAlign: 'center', padding: '20px' }}>
                            <FileSpreadsheet size={64} style={{ color: '#22d3ee', marginBottom: '20px' }} />
                            <Title level={4}>Excel合并独立工具</Title>
                            <Paragraph style={{ marginBottom: '24px' }}>
                                无需安装，双击即可运行的Excel合并工具
                            </Paragraph>

                            <Alert
                                message="功能特性"
                                description={
                                    <ul style={{ textAlign: 'left', margin: '8px 0', paddingLeft: '20px' }}>
                                        <li>图形化界面，操作简单直观</li>
                                        <li>支持拖拽添加Excel文件</li>
                                        <li>可配置合并参数（跳过行数等）</li>
                                        <li>实时显示合并进度</li>
                                        <li>支持.xlsx和.xls格式</li>
                                        <li>自动保存合并结果</li>
                                    </ul>
                                }
                                type="info"
                                style={{ marginBottom: '20px' }}
                            />

                            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                                <Button
                                    type="primary"
                                    size="large"
                                    icon={<Download size={20} />}
                                    disabled
                                    style={{ width: '200px' }}
                                >
                                    下载Windows版本
                                </Button>
                                <Text type="secondary">文件大小：约 15MB | 版本：v1.0.0</Text>
                                <Text type="secondary" style={{ fontSize: '12px' }}>
                                    系统要求：Windows 7 及以上版本
                                </Text>
                            </Space>
                        </div>
                    </TabPane>

                    <TabPane tab="Excel插件" key="plugin">
                        <div style={{ textAlign: 'center', padding: '20px' }}>
                            <Code size={64} style={{ color: '#10b981', marginBottom: '20px' }} />
                            <Title level={4}>Excel插件版本</Title>
                            <Paragraph style={{ marginBottom: '24px' }}>
                                安装到Excel中的插件，集成在Excel工具栏
                            </Paragraph>

                            <Alert
                                message="插件特性"
                                description={
                                    <ul style={{ textAlign: 'left', margin: '8px 0', paddingLeft: '20px' }}>
                                        <li>直接集成在Excel功能区</li>
                                        <li>支持批量处理和自动化</li>
                                        <li>保留原始文件格式和样式</li>
                                        <li>支持自定义合并规则</li>
                                        <li>兼容Excel 2010-2021版本</li>
                                        <li>一键安装，自动配置</li>
                                    </ul>
                                }
                                type="success"
                                style={{ marginBottom: '20px' }}
                            />

                            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                                <Button
                                    type="primary"
                                    size="large"
                                    icon={<Download size={20} />}
                                    disabled
                                    style={{ width: '200px' }}
                                >
                                    下载Excel插件
                                </Button>
                                <Text type="secondary">文件大小：约 8MB | 版本：v1.0.0</Text>
                                <Text type="secondary" style={{ fontSize: '12px' }}>
                                    支持：Excel 2010, 2013, 2016, 2019, 2021, Office 365
                                </Text>
                            </Space>
                        </div>
                    </TabPane>

                    <TabPane tab="Python脚本" key="python">
                        <div style={{ padding: '20px' }}>
                            <div style={{ textAlign: 'center', marginBottom: '20px' }}>
                                <FileText size={64} style={{ color: '#f97316', marginBottom: '20px' }} />
                                <Title level={4}>Python脚本版本</Title>
                                <Paragraph>
                                    适合开发者使用的Python脚本，可自定义和扩展
                                </Paragraph>
                            </div>

                            <Alert
                                message="脚本特性"
                                description={
                                    <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                                        <li>开源代码，可自由修改和扩展</li>
                                        <li>支持命令行批量处理</li>
                                        <li>可集成到自动化流程中</li>
                                        <li>支持更多自定义参数</li>
                                        <li>跨平台支持（Windows/Mac/Linux）</li>
                                    </ul>
                                }
                                type="warning"
                                style={{ marginBottom: '20px' }}
                            />

                            <div style={{ textAlign: 'center' }}>
                                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                                    <Button
                                        type="primary"
                                        size="large"
                                        icon={<Download size={20} />}
                                        onClick={() => {
                                            const pythonScript = `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel合并工具 - Python版本
功能：批量合并Excel文件
作者：IDEALAB团队
版本：1.0.0
"""

import pandas as pd
import os
import sys
from pathlib import Path
import argparse

def merge_excel_files(file_paths, output_path, skip_header=0, skip_footer=0):
    """
    合并多个Excel文件

    Args:
        file_paths: Excel文件路径列表
        output_path: 输出文件路径
        skip_header: 跳过头部行数
        skip_footer: 跳过尾部行数
    """
    merged_data = []

    for i, file_path in enumerate(file_paths):
        print(f"处理文件 {i+1}/{len(file_paths)}: {file_path}")

        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)

            # 应用跳过行数配置
            if skip_header > 0:
                df = df.iloc[skip_header:]
            if skip_footer > 0:
                df = df.iloc[:-skip_footer]

            # 重置索引
            df.reset_index(drop=True, inplace=True)

            merged_data.append(df)

        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            continue

    if merged_data:
        # 合并所有数据
        result = pd.concat(merged_data, ignore_index=True)

        # 保存结果
        result.to_excel(output_path, index=False)
        print(f"合并完成！输出文件：{output_path}")
        print(f"总行数：{len(result)}")
    else:
        print("没有成功处理的文件")

def main():
    parser = argparse.ArgumentParser(description='Excel文件合并工具')
    parser.add_argument('files', nargs='+', help='要合并的Excel文件路径')
    parser.add_argument('-o', '--output', default='merged_excel.xlsx', help='输出文件路径')
    parser.add_argument('--skip-header', type=int, default=0, help='跳过头部行数')
    parser.add_argument('--skip-footer', type=int, default=0, help='跳过尾部行数')

    args = parser.parse_args()

    # 验证文件存在
    valid_files = []
    for file_path in args.files:
        if os.path.exists(file_path):
            valid_files.append(file_path)
        else:
            print(f"文件不存在：{file_path}")

    if not valid_files:
        print("没有找到有效的Excel文件")
        sys.exit(1)

    # 执行合并
    merge_excel_files(
        valid_files,
        args.output,
        args.skip_header,
        args.skip_footer
    )

if __name__ == '__main__':
    main()
`;
                                            const blob = new Blob([pythonScript], { type: 'text/plain' });
                                            const url = URL.createObjectURL(blob);
                                            const a = document.createElement('a');
                                            a.href = url;
                                            a.download = 'excel_merger.py';
                                            a.click();
                                            URL.revokeObjectURL(url);
                                            message.success('Python脚本已下载');
                                        }}
                                        style={{ width: '200px' }}
                                    >
                                        下载Python脚本
                                    </Button>
                                    <Text type="secondary">文件大小：约 3KB | 版本：v1.0.0</Text>
                                    <Text type="secondary" style={{ fontSize: '12px' }}>
                                        需要：Python 3.6+ 和 pandas 库
                                    </Text>
                                </Space>
                            </div>
                        </div>
                    </TabPane>
                </Tabs>
            </Modal>
        </div>
    );
};