from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum


# 枚举类型
class FileFormat(str, Enum):
    """支持的文件格式"""
    PDF = "pdf"
    PNG = "png"
    JPG = "jpg"
    JPEG = "jpeg"
    TIFF = "tiff"
    BMP = "bmp"
    XLSX = "xlsx"
    XLS = "xls"
    CSV = "csv"
    DOCX = "docx"
    DOC = "doc"


class OCRLanguage(str, Enum):
    """OCR支持的语言"""
    CHINESE = "chi_sim"
    ENGLISH = "eng"
    CHINESE_TRADITIONAL = "chi_tra"
    AUTO = "auto"


class WatermarkType(str, Enum):
    """水印类型"""
    TEXT = "text"
    IMAGE = "image"


# OCR相关模型
class OCRRequest(BaseModel):
    """OCR请求模型"""
    file_urls: List[str] = Field(..., description="文件URL列表", min_items=1, max_items=50)
    language: OCRLanguage = Field(default=OCRLanguage.AUTO, description="识别语言")
    output_format: str = Field(default="text", description="输出格式：text/json/table")
    enable_table_detection: bool = Field(default=False, description="是否启用表格检测")
    confidence_threshold: float = Field(default=0.5, description="置信度阈值", ge=0.0, le=1.0)


class OCRResult(BaseModel):
    """单个文件OCR结果"""
    file_url: str = Field(..., description="原始文件URL")
    file_name: str = Field(..., description="文件名")
    recognized_text: str = Field(..., description="识别的文本")
    confidence: float = Field(..., description="平均置信度")
    page_count: int = Field(..., description="页面数量")
    processing_time: float = Field(..., description="处理时间(秒)")
    bounding_boxes: Optional[List[Dict[str, Any]]] = Field(None, description="文字边界框")
    table_data: Optional[List[Dict[str, Any]]] = Field(None, description="表格数据")


class OCRResponse(BaseModel):
    """OCR响应模型"""
    task_id: str = Field(..., description="任务ID")
    total_files: int = Field(..., description="总文件数")
    processed_files: int = Field(..., description="已处理文件数")
    failed_files: int = Field(..., description="失败文件数")
    results: List[OCRResult] = Field(..., description="识别结果列表")
    total_processing_time: float = Field(..., description="总处理时间")
    download_url: Optional[str] = Field(None, description="结果下载链接")


# 水印相关模型
class TextWatermarkConfig(BaseModel):
    """文字水印配置"""
    text: str = Field(..., description="水印文字", min_length=1, max_length=100)
    font_size: int = Field(default=24, description="字体大小", ge=8, le=200)
    font_color: str = Field(default="rgba(128,128,128,0.5)", description="字体颜色")
    position: str = Field(default="center", description="位置：center/top-left/top-right/bottom-left/bottom-right")
    rotation: int = Field(default=0, description="旋转角度", ge=-360, le=360)
    opacity: float = Field(default=0.5, description="透明度", ge=0.0, le=1.0)


class ImageWatermarkConfig(BaseModel):
    """图片水印配置"""
    watermark_url: str = Field(..., description="水印图片URL")
    scale: float = Field(default=0.2, description="缩放比例", ge=0.01, le=1.0)
    position: str = Field(default="center", description="位置")
    opacity: float = Field(default=0.5, description="透明度", ge=0.0, le=1.0)


class WatermarkRequest(BaseModel):
    """水印请求模型"""
    file_urls: List[str] = Field(..., description="文件URL列表", min_items=1, max_items=100)
    watermark_type: WatermarkType = Field(..., description="水印类型")
    text_config: Optional[TextWatermarkConfig] = Field(None, description="文字水印配置")
    image_config: Optional[ImageWatermarkConfig] = Field(None, description="图片水印配置")
    output_format: FileFormat = Field(default=FileFormat.PNG, description="输出格式")
    quality: int = Field(default=95, description="输出质量", ge=1, le=100)


class WatermarkResult(BaseModel):
    """水印处理结果"""
    original_url: str = Field(..., description="原始文件URL")
    watermarked_url: str = Field(..., description="加水印后的文件URL")
    file_name: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小(字节)")
    processing_time: float = Field(..., description="处理时间(秒)")


class WatermarkResponse(BaseModel):
    """水印响应模型"""
    task_id: str = Field(..., description="任务ID")
    total_files: int = Field(..., description="总文件数")
    processed_files: int = Field(..., description="已处理文件数")
    failed_files: int = Field(..., description="失败文件数")
    results: List[WatermarkResult] = Field(..., description="处理结果列表")
    total_processing_time: float = Field(..., description="总处理时间")
    download_url: Optional[str] = Field(None, description="批量下载链接")


# Excel相关模型
class ExcelMergeConfig(BaseModel):
    """Excel合并配置"""
    merge_type: str = Field(..., description="合并类型：vertical/horizontal/by_sheet")
    header_handling: str = Field(default="first_only", description="表头处理：first_only/all/none")
    sheet_naming: str = Field(default="auto", description="工作表命名：auto/original/custom")
    custom_sheet_names: Optional[List[str]] = Field(None, description="自定义工作表名")
    include_source_info: bool = Field(default=True, description="是否包含源文件信息")


class ExcelMergeRequest(BaseModel):
    """Excel合并请求模型"""
    file_urls: List[str] = Field(..., description="Excel文件URL列表", min_items=2, max_items=50)
    config: ExcelMergeConfig = Field(..., description="合并配置")
    output_filename: str = Field(default="merged_excel.xlsx", description="输出文件名")


class ExcelProcessingResult(BaseModel):
    """Excel处理结果"""
    original_filename: str = Field(..., description="原始文件名")
    sheet_count: int = Field(..., description="工作表数量")
    total_rows: int = Field(..., description="总行数")
    total_columns: int = Field(..., description="总列数")
    processing_status: str = Field(..., description="处理状态")
    error_message: Optional[str] = Field(None, description="错误信息")


class ExcelMergeResponse(BaseModel):
    """Excel合并响应模型"""
    task_id: str = Field(..., description="任务ID")
    merged_file_url: str = Field(..., description="合并后文件URL")
    source_files: List[ExcelProcessingResult] = Field(..., description="源文件处理结果")
    merged_stats: Dict[str, Any] = Field(..., description="合并统计信息")
    processing_time: float = Field(..., description="处理时间(秒)")


# 任务状态模型
class TaskStatus(str, Enum):
    """任务状态"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskInfo(BaseModel):
    """任务信息"""
    task_id: str = Field(..., description="任务ID")
    task_type: str = Field(..., description="任务类型：ocr/watermark/excel")
    status: TaskStatus = Field(..., description="任务状态")
    progress: float = Field(..., description="任务进度", ge=0.0, le=100.0)
    created_at: datetime = Field(..., description="创建时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    error_message: Optional[str] = Field(None, description="错误信息")
    result_data: Optional[Dict[str, Any]] = Field(None, description="结果数据")


# 通用响应模型
class FileUploadResponse(BaseModel):
    """文件上传响应"""
    file_id: str = Field(..., description="文件ID")
    file_url: str = Field(..., description="文件访问URL")
    filename: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小")
    content_type: str = Field(..., description="文件类型")
    uploaded_at: datetime = Field(..., description="上传时间")


class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = Field(True, description="操作是否成功")
    message: str = Field("", description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")