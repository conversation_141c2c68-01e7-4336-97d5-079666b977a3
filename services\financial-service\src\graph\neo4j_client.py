from neo4j import AsyncGraphDatabase
from typing import Dict, List, Any, Optional
import logging
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)


class Neo4jClient:
    def __init__(self, uri: str, username: str, password: str):
        self.uri = uri
        self.username = username
        self.password = password
        self.driver: Optional[AsyncGraphDatabase.driver] = None

    async def connect(self):
        """建立Neo4j连接"""
        try:
            self.driver = AsyncGraphDatabase.driver(
                self.uri,
                auth=(self.username, self.password)
            )
            # 测试连接
            await self.verify_connectivity()
            logger.info("✅ Neo4j连接成功")
        except Exception as e:
            logger.error(f"❌ Neo4j连接失败: {e}")
            raise

    async def verify_connectivity(self):
        """验证连接"""
        async with self.driver.session() as session:
            result = await session.run("RETURN 1")
            record = await result.single()
            return record and record[0] == 1

    async def close(self):
        """关闭Neo4j驱动连接"""
        if self.driver:
            await self.driver.close()
            logger.info("Neo4j连接已关闭")

    @asynccontextmanager
    async def session(self):
        """会话上下文管理器"""
        if not self.driver:
            raise ConnectionError("Driver not connected. Call connect() first.")
        
        session = self.driver.session()
        try:
            yield session
        finally:
            if session:
                await session.close()

    async def execute_query(self, query: str, parameters: Dict[str, Any] = None) -> List[Dict]:
        """执行查询"""
        async with self.session() as session:
            result = await session.run(query, parameters or {})
            return [record.data() async for record in result]

    async def execute_write_query(self, query: str, parameters: Dict[str, Any] = None) -> Dict:
        """执行写入查询"""
        async with self.session() as session:
            result = await session.run(query, parameters or {})
            summary = await result.consume()
            return {
                "nodes_created": summary.counters.nodes_created,
                "nodes_deleted": summary.counters.nodes_deleted,
                "relationships_created": summary.counters.relationships_created,
                "relationships_deleted": summary.counters.relationships_deleted,
                "properties_set": summary.counters.properties_set
            }

    async def create_indexes(self):
        """创建数据库索引"""
        async with self.driver.session() as session:
            # Full-text index for company names
            await session.run("""
                CREATE FULLTEXT INDEX company_name_index IF NOT EXISTS 
                FOR (c:Company) ON EACH [c.name]
            """)
            logger.info("已创建或确认company_name_index全文本索引。")

            # Other indexes
            index_queries = [
                "CREATE INDEX company_unified_code_index IF NOT EXISTS FOR (c:Company) ON (c.unified_code)",
                "CREATE INDEX person_id_index IF NOT EXISTS FOR (p:Person) ON (p.id_number)"
            ]
            for query in index_queries:
                await session.run(query)
            
            logger.info("已创建或确认节点和关系索引。")

    async def clear_database(self):
        """清空数据库"""
        await self.execute_write_query("MATCH (n) DETACH DELETE n")
        logger.info("数据库已清空")

    async def get_database_stats(self) -> Dict:
        """获取数据库统计信息"""
        queries = {
            "companies": "MATCH (c:Company) RETURN count(c) as count",
            "persons": "MATCH (p:Person) RETURN count(p) as count",
            "investments": "MATCH ()-[r:INVESTS_IN]->() RETURN count(r) as count",
            "shareholdings": "MATCH ()-[r:HOLDS_SHARES]->() RETURN count(r) as count",
            "pledges": "MATCH ()-[r:PLEDGED_TO]->() RETURN count(r) as count"
        }
        
        stats = {}
        for name, query in queries.items():
            result = await self.execute_query(query)
            stats[name] = result[0]["count"] if result else 0
        
        return stats