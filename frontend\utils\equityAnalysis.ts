import { EquityNode, NodeCentrality, Community, AnalysisStats, NetworkInsight } from '../types/equity';

// 颜色配置
export const NODE_COLORS = {
  parent: '#2563eb',
  subsidiary: '#06b6d4',
  holding: '#10b981',
  partner: '#8b5cf6',
  person: '#f59e0b'
} as const;

export const EDGE_COLORS = {
  control: '#ef4444',
  holding: '#f59e0b',
  partnership: '#8b5cf6'
} as const;

export const COMMUNITY_COLORS = [
  '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
  '#13c2c2', '#eb2f96', '#fa541c', '#a0d911', '#096dd9'
] as const;

// 类型名称映射
export const TYPE_NAMES = {
  parent: '母公司',
  subsidiary: '子公司',
  holding: '控股公司',
  partner: '参股公司',
  person: '个人'
} as const;

// 角色名称映射
export const ROLE_NAMES = {
  key_player: '关键角色',
  bridge: '桥梁节点',
  influencer: '影响者',
  connector: '连接器'
} as const;

// 角色颜色映射
export const ROLE_COLORS = {
  key_player: '#faad14',
  bridge: '#f5222d',
  influencer: '#13c2c2',
  connector: '#52c41a'
} as const;

/**
 * 获取节点颜色
 */
export const getNodeColor = (type: string): string => {
  return NODE_COLORS[type as keyof typeof NODE_COLORS] || '#64748b';
};

/**
 * 获取社群颜色
 */
export const getCommunityColor = (communityId: number): string => {
  return COMMUNITY_COLORS[communityId % COMMUNITY_COLORS.length];
};

/**
 * 获取类型名称
 */
export const getTypeName = (type: string): string => {
  return TYPE_NAMES[type as keyof typeof TYPE_NAMES] || type;
};

/**
 * 获取角色名称
 */
export const getRoleName = (role: string): string => {
  return ROLE_NAMES[role as keyof typeof ROLE_NAMES] || role;
};

/**
 * 获取角色颜色
 */
export const getRoleColor = (role: string): string => {
  return ROLE_COLORS[role as keyof typeof ROLE_COLORS] || '#d9d9d9';
};

/**
 * 将嵌套的股权数据转换为平面数组
 */
export const flattenEquityNodes = (nodes: EquityNode[]): EquityNode[] => {
  const result: EquityNode[] = [];
  
  const traverse = (nodeList: EquityNode[]) => {
    nodeList.forEach(node => {
      // 确保节点有必要的分析属性
      const flatNode: EquityNode = {
        ...node,
        centrality: node.centrality || generateMockCentrality(),
        community: node.community ?? Math.floor(Math.random() * 3),
        importance: node.importance ?? node.percentage,
        internalConnections: node.internalConnections ?? Math.floor(Math.random() * 3),
        externalConnections: node.externalConnections ?? Math.floor(Math.random() * 2)
      };
      
      result.push(flatNode);
      
      if (node.children) {
        traverse(node.children);
      }
    });
  };
  
  traverse(nodes);
  return result;
};

/**
 * 生成模拟中心性数据
 */
const generateMockCentrality = (): NodeCentrality => ({
  betweenness: Math.random() * 0.5,
  closeness: Math.random() * 0.5,
  degree: Math.floor(Math.random() * 5),
  pageRank: Math.random() * 0.1
});

/**
 * 计算分析统计数据
 */
export const calculateAnalysisStats = (data: EquityNode[]): AnalysisStats => {
  let totalNodes = 0;
  let totalEdges = 0;
  let maxDepth = 0;
  let totalEquity = 0;
  let controllingStakes = 0;
  const communities = new Set<number>();

  const traverse = (nodes: EquityNode[], depth = 0) => {
    maxDepth = Math.max(maxDepth, depth);
    nodes.forEach(node => {
      totalNodes++;
      totalEquity += node.percentage;
      if (node.percentage > 50) controllingStakes++;
      if (node.community !== undefined) communities.add(node.community);
      
      if (node.children) {
        totalEdges += node.children.length;
        traverse(node.children, depth + 1);
      }
    });
  };

  traverse(data);

  return {
    totalNodes,
    totalEdges,
    maxDepth,
    totalEquity: Math.round(totalEquity),
    controllingStakes,
    communities: communities.size
  };
};

/**
 * 分析单个社群
 */
export const analyzeCommunity = (id: number, nodes: EquityNode[]): Community => {
  const size = nodes.length;
  const totalEquity = nodes.reduce((sum, node) => sum + node.percentage, 0);
  const avgEquity = totalEquity / size;

  // 计算社群密度
  const totalInternalConnections = nodes.reduce((sum, node) => 
    sum + (node.internalConnections || 0), 0
  );
  const maxPossibleConnections = (size * (size - 1)) / 2;
  const density = maxPossibleConnections > 0 ? totalInternalConnections / maxPossibleConnections : 0;

  // 找出主导类型
  const typeCount = new Map<string, number>();
  nodes.forEach(node => {
    typeCount.set(node.type, (typeCount.get(node.type) || 0) + 1);
  });
  const dominantType = Array.from(typeCount.entries())
    .sort((a, b) => b[1] - a[1])[0]?.[0] || 'unknown';

  // 找出中心节点
  const centralNode = nodes.reduce((prev, current) =>
    ((prev.importance || 0) > (current.importance || 0)) ? prev : current
  );

  // 计算凝聚力
  const totalConnections = nodes.reduce((sum, node) => 
    sum + (node.internalConnections || 0) + (node.externalConnections || 0), 0
  );
  const cohesion = totalConnections > 0 ? totalInternalConnections / totalConnections : 0;

  // 计算影响力
  const influence = nodes.reduce((sum, node) => 
    sum + (node.importance || 0) * ((node.internalConnections || 0) + (node.externalConnections || 0)), 0
  ) / size;

  return {
    id,
    nodes,
    size,
    density,
    totalEquity,
    avgEquity,
    dominantType,
    centralNode,
    cohesion,
    influence
  };
};

/**
 * 生成网络洞察
 */
export const generateNetworkInsights = (nodes: EquityNode[]): NetworkInsight[] => {
  const insights: NetworkInsight[] = [];
  
  // 控制权集中度分析
  const controlNodes = nodes.filter(n => n.percentage > 50);
  if (controlNodes.length > 0) {
    insights.push({
      type: 'control',
      title: '控制权集中度分析',
      description: `发现 ${controlNodes.length} 个具有控制权的节点，控制权相对${controlNodes.length > 3 ? '分散' : '集中'}`,
      level: controlNodes.length > 3 ? 'high' : 'medium',
      nodes: controlNodes.map(n => n.id),
      recommendation: controlNodes.length > 3 ? '建议关注控制权分散带来的治理风险' : '建议关注控制权集中带来的决策风险'
    });
  }

  // 网络脆弱性分析
  const bridgeNodes = nodes.filter(n => n.centrality && n.centrality.betweenness > 0.3);
  if (bridgeNodes.length > 0) {
    insights.push({
      type: 'vulnerability',
      title: '网络脆弱性分析',
      description: `${bridgeNodes.length} 个关键桥梁节点的失效可能导致网络分割`,
      level: 'high',
      nodes: bridgeNodes.map(n => n.id),
      recommendation: '建议建立备用连接路径，降低对关键节点的依赖'
    });
  }

  // 影响力分布分析
  const influencers = nodes.filter(n => n.centrality && n.centrality.closeness > 0.5);
  if (influencers.length > 0) {
    insights.push({
      type: 'influence',
      title: '影响力分布分析',
      description: `${influencers.length} 个节点具有高影响力，能够快速传播信息和影响`,
      level: 'medium',
      nodes: influencers.map(n => n.id),
      recommendation: '可利用高影响力节点进行信息传播和决策推广'
    });
  }

  // 社群结构分析
  const communities = new Set(nodes.map(n => n.community).filter(c => c !== undefined));
  if (communities.size > 1) {
    insights.push({
      type: 'structure',
      title: '社群结构分析',
      description: `网络中存在 ${communities.size} 个不同的社群，具有模块化特征`,
      level: 'medium',
      nodes: [],
      recommendation: '可基于社群结构进行分层管理和差异化策略制定'
    });
  }

  return insights;
};

/**
 * 计算网络统计指标
 */
export const calculateNetworkStats = (nodes: EquityNode[]): any => {
  const totalNodes = nodes.length;
  const totalConnections = nodes.reduce((sum, node) => 
    sum + (node.internalConnections || 0) + (node.externalConnections || 0), 0
  ) / 2; // 除以2因为每条边被计算了两次

  const avgDegree = totalNodes > 0 ? (2 * totalConnections) / totalNodes : 0;
  const maxPossibleEdges = (totalNodes * (totalNodes - 1)) / 2;
  const density = maxPossibleEdges > 0 ? totalConnections / maxPossibleEdges : 0;

  // 计算中心性分布
  const centralityStats = {
    avgBetweenness: 0,
    avgCloseness: 0,
    avgPageRank: 0,
    maxDegree: 0
  };

  if (totalNodes > 0) {
    centralityStats.avgBetweenness = nodes.reduce((sum, node) => 
      sum + (node.centrality?.betweenness || 0), 0
    ) / totalNodes;

    centralityStats.avgCloseness = nodes.reduce((sum, node) => 
      sum + (node.centrality?.closeness || 0), 0
    ) / totalNodes;

    centralityStats.avgPageRank = nodes.reduce((sum, node) => 
      sum + (node.centrality?.pageRank || 0), 0
    ) / totalNodes;

    centralityStats.maxDegree = Math.max(...nodes.map(node => 
      node.centrality?.degree || 0
    ));
  }

  return {
    nodeCount: totalNodes,
    edgeCount: totalConnections,
    avgDegree: Math.round(avgDegree * 100) / 100,
    density: Math.round(density * 1000) / 1000,
    ...centralityStats
  };
};

/**
 * 导出数据为JSON格式
 */
export const exportToJSON = (data: any, filename: string = 'equity_analysis'): void => {
  const jsonString = JSON.stringify(data, null, 2);
  const blob = new Blob([jsonString], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `${filename}_${Date.now()}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
};

/**
 * 导出数据为CSV格式
 */
export const exportToCSV = (nodes: EquityNode[], filename: string = 'equity_nodes'): void => {
  const headers = [
    'ID', '名称', '类型', '持股比例(%)', '层级', '重要性',
    '介数中心性', '接近中心性', '度中心性', 'PageRank', '社群'
  ];
  
  const rows = nodes.map(node => [
    node.id,
    node.name,
    getTypeName(node.type),
    node.percentage,
    node.level,
    node.importance || 0,
    node.centrality?.betweenness || 0,
    node.centrality?.closeness || 0,
    node.centrality?.degree || 0,
    node.centrality?.pageRank || 0,
    node.community || ''
  ]);
  
  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${cell}"`).join(','))
    .join('\n');
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `${filename}_${Date.now()}.csv`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
};

/**
 * 格式化数字显示
 */
export const formatNumber = (num: number, precision: number = 2): number => {
  return Math.round(num * Math.pow(10, precision)) / Math.pow(10, precision);
};

/**
 * 格式化百分比显示
 */
export const formatPercentage = (num: number, precision: number = 1): string => {
  return `${formatNumber(num, precision)}%`;
};

/**
 * 生成唯一ID
 */
export const generateId = (): string => {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};