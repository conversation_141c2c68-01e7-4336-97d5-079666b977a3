const express = require('express');
const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');
const { verifyAccessToken, extractTokenFromHeader } = require('../utils/tokens');

const prisma = new PrismaClient();
const router = express.Router();

// 认证中间件 - 修改为信任网关
const authenticate = (req, res, next) => {
  // 在微服务架构中, 网关负责认证, 并将用户信息通过 headers 传递给下游服务
  // 我们信任由网关添加的 'x-user-id' 头
  const userId = req.headers['x-user-id'];

  if (!userId) {
    // 如果没有这个头, 说明请求可能绕过了网关, 是非法请求
    return res.status(401).json({ success: false, message: 'User ID not found in request headers. Requests must come via the API gateway.' });
  }

  // 将解码后的用户信息附加到请求对象上, 供后续的路由处理器使用
  req.user = { userId: userId };
  next();
};

// GET /profile - 获取用户资料
router.get('/profile', authenticate, async (req, res) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
    });
    
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    const { password, ...userProfile } = user;
    res.json({ success: true, data: { user: userProfile } });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Failed to get user profile' });
  }
});

// POST /change-password - 更改密码
router.post('/change-password', authenticate, async (req, res) => {
  try {
    const { oldPassword, newPassword } = req.body;

    if (!oldPassword || !newPassword || newPassword.length < 6) {
      return res.status(400).json({ success: false, message: 'Invalid input. New password must be at least 6 characters long.' });
    }

    const user = await prisma.user.findUnique({ where: { id: req.user.userId } });

    if (!user || !bcrypt.compareSync(oldPassword, user.password)) {
      return res.status(401).json({ success: false, message: 'Invalid old password.' });
    }

    const hashedNewPassword = bcrypt.hashSync(newPassword, 10);

    await prisma.user.update({
      where: { id: req.user.userId },
      data: {
        password: hashedNewPassword,
        passwordChangeRequired: false,
      },
    });

    res.json({ success: true, message: 'Password changed successfully.' });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Failed to change password.' });
  }
});

module.exports = router;