{"version": 3, "file": "RealtimeCrawlModal-CdWpPaus.js", "sources": ["../../components/RealtimeCrawlModal.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport {\n    Modal,\n    Steps,\n    Progress,\n    Typography,\n    Button,\n    Alert,\n    List,\n    Tag,\n    Space,\n    Spin,\n    Card,\n    Statistic,\n    Row,\n    Col\n} from 'antd';\nimport {\n    CheckCircleOutlined,\n    SyncOutlined,\n    TrophyOutlined\n} from '@ant-design/icons';\n\n\nconst { Text } = Typography;\nconst { Step } = Steps;\n\ninterface LogEntry {\n    timestamp: string;\n    level: 'info' | 'success' | 'warning' | 'error' | 'progress';\n    message: string;\n    phase: string;\n    progress?: number;\n}\n\ninterface TaskStatus {\n    id: string;\n    company_name: string;\n    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';\n    progress: number;\n    total_steps: number;\n    processed_companies: number;\n    created_at: string;\n    started_at?: string;\n    completed_at?: string;\n    error_message?: string;\n    duration?: number;\n}\n\ninterface RealtimeCrawlModalProps {\n    visible: boolean;\n    companyName: string;\n    onClose: () => void;\n    onComplete: (result: any) => void;\n    onCancel: () => void;\n}\n\nexport const RealtimeCrawlModal: React.FC<RealtimeCrawlModalProps> = ({\n    visible,\n    companyName,\n    onClose,\n    onComplete,\n    onCancel\n}) => {\n    const [taskStatus, setTaskStatus] = useState<TaskStatus | null>(null);\n    const [logs, setLogs] = useState<LogEntry[]>([]);\n    const [currentStep, setCurrentStep] = useState(0);\n    const [isStarting, setIsStarting] = useState(false);\n    const [socket, setSocket] = useState<WebSocket | null>(null);\n    const [estimatedDuration, setEstimatedDuration] = useState(0);\n    const [startTime, setStartTime] = useState<Date | null>(null);\n\n    const logsEndRef = useRef<HTMLDivElement>(null);\n    const MAX_LOGS = 100; // 限制日志数量，避免内存溢出\n\n    // 自动滚动到日志底部\n    const scrollToBottom = () => {\n        logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n    };\n\n    useEffect(() => {\n        scrollToBottom();\n    }, [logs]);\n\n    // 启动实时抓取\n    const startRealtimeCrawl = useCallback(async () => {\n        if (!companyName) return;\n\n        setIsStarting(true);\n        setLogs([]);\n        setStartTime(new Date());\n\n        try {\n            const response = await fetch('/api/equity/start-realtime-crawl', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${localStorage.getItem('token')}` // 假设token存储在localStorage\n                },\n                body: JSON.stringify({\n                    company_name: companyName,\n                    depth: 2,\n                    direction: 'both'\n                })\n            });\n\n            // 兼容空响应或非JSON响应，避免 Unexpected end of JSON input\n            const contentType = response.headers.get('content-type') || '';\n            let data: any = null;\n            let rawText = '';\n            try {\n                if (contentType.includes('application/json')) {\n                    data = await response.json();\n                } else {\n                    rawText = await response.text();\n                }\n            } catch (e) {\n                try { rawText = await response.text(); } catch {}\n            }\n\n            if (!response.ok) {\n                const detail = data?.detail || rawText || `启动分析失败 (HTTP ${response.status})`;\n                throw new Error(detail);\n            }\n\n            setEstimatedDuration(data.estimated_duration);\n\n            // 建立 WebSocket 连接（通过 Vite 代理到网关）\n            const taskId = data?.task_id || JSON.parse(rawText || '{}').task_id;\n            if (!taskId) {\n                throw new Error('未能获取到任务ID');\n            }\n            const wsPath = `/api/equity/ws/crawl-progress/${taskId}`;\n            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';\n            const wsUrl = `${protocol}//${window.location.host}${wsPath}`;\n            const ws = new WebSocket(wsUrl);\n\n            setSocket(ws);\n\n            // 处理 WebSocket 事件\n            ws.addEventListener('open', () => {\n                addLog('info', '已连接到实时进度服务');\n            });\n\n            ws.addEventListener('message', (event) => {\n                try {\n                    const msg = JSON.parse(event.data);\n                    switch (msg.type) {\n                        case 'progress_update':\n                            setTaskStatus(prev => prev ? { ...prev, progress: (typeof msg.progress === 'number' ? msg.progress : prev.progress) } : prev);\n                            if (typeof msg.progress === 'number') {\n                                updateCurrentStep(msg.progress);\n                            }\n                            break;\n                        case 'log_message':\n                            addLog(msg.level || 'info', msg.message || '', msg.phase);\n                            break;\n                        case 'status_change':\n                            setTaskStatus(prev => prev ? { ...prev, status: msg.status || prev.status } : prev);\n                            if (msg.status === 'completed') {\n                                addLog('success', `🎉 分析完成！耗时 ${msg.duration || 0} 秒`);\n                                fetchCrawlResult(msg.task_id);\n                            } else if (msg.status === 'failed') {\n                                addLog('error', `❌ 分析失败: ${msg.error || '未知错误'}`);\n                            }\n                            break;\n                        case 'error':\n                            addLog('error', `错误: ${msg.error_message || '未知错误'}`);\n                            break;\n                        default:\n                            break;\n                    }\n                } catch (e) {\n                    console.error('WS消息解析失败:', e);\n                }\n            });\n\n            ws.addEventListener('close', () => {\n                addLog('warning', '与服务器的连接已断开');\n            });\n\n            ws.addEventListener('error', () => {\n                addLog('error', 'WebSocket发生错误');\n            });\n\n            // 初始状态设置\n            setTaskStatus({\n                id: data.task_id,\n                company_name: companyName,\n                status: 'running',\n                progress: 0,\n                total_steps: 0,\n                processed_companies: 0,\n                created_at: new Date().toISOString()\n            });\n\n            addLog('success', `🚀 开始分析 \"${companyName}\" 的股权结构`);\n            addLog('info', `预计需要 ${data.estimated_duration} 秒`);\n\n        } catch (error) {\n            console.error('启动分析失败:', error);\n            addLog('error', `启动失败: ${error instanceof Error ? error.message : '未知错误'}`);\n        } finally {\n            setIsStarting(false);\n        }\n    }, [companyName]);\n\n    // 获取爬取结果\n    const fetchCrawlResult = async (taskId: string) => {\n        try {\n            const response = await fetch(`/api/equity/crawl-result/${taskId}`, {\n                headers: {\n                    'Authorization': `Bearer ${localStorage.getItem('token')}`\n                }\n            });\n\n            if (response.ok) {\n                const result = await response.json();\n                onComplete(result.result);\n            }\n        } catch (error) {\n            console.error('获取结果失败:', error);\n            addLog('error', '获取分析结果失败');\n        }\n    };\n\n    // 添加日志\n    const addLog = (level: LogEntry['level'], message: string, phase?: string) => {\n        const newLog: LogEntry = {\n            timestamp: new Date().toISOString(),\n            level,\n            message,\n            phase: phase || '分析中'\n        };\n\n        setLogs(prev => {\n            const newLogs = [...prev, newLog];\n            return newLogs.slice(-MAX_LOGS); // 保持最新的100条日志\n        });\n    };\n\n    // 更新当前步骤\n    const updateCurrentStep = (progress: number) => {\n        if (progress < 25) setCurrentStep(0);\n        else if (progress < 50) setCurrentStep(1);\n        else if (progress < 75) setCurrentStep(2);\n        else if (progress < 100) setCurrentStep(3);\n        else setCurrentStep(4);\n    };\n\n    // 取消任务\n    const handleCancel = async () => {\n        if (taskStatus?.id) {\n            try {\n                await fetch(`/api/equity/cancel-task/${taskStatus.id}`, {\n                    method: 'POST',\n                    headers: {\n                        'Authorization': `Bearer ${localStorage.getItem('token')}`\n                    }\n                });\n                addLog('warning', '任务已取消');\n            } catch (error) {\n                console.error('取消任务失败:', error);\n            }\n        }\n\n        if (socket) {\n            try { socket.close(); } catch {}\n            setSocket(null);\n        }\n\n        onCancel();\n    };\n\n    // 关闭Modal\n    const handleClose = () => {\n        if (socket) {\n            try { socket.close(); } catch {}\n            setSocket(null);\n        }\n        onClose();\n    };\n\n    // 计算已用时间\n    const getElapsedTime = () => {\n        if (!startTime) return 0;\n        return Math.floor((Date.now() - startTime.getTime()) / 1000);\n    };\n\n    // 获取日志颜色\n    const getLogColor = (level: LogEntry['level']) => {\n        const colors = {\n            'info': 'default',\n            'success': 'success',\n            'warning': 'warning',\n            'error': 'error',\n            'progress': 'processing'\n        };\n        return colors[level] || 'default';\n    };\n\n    // 获取步骤状态\n    const getStepStatus = (stepIndex: number) => {\n        if (!taskStatus) return 'wait';\n\n        if (taskStatus.status === 'failed' && currentStep >= stepIndex) return 'error';\n        if (currentStep > stepIndex) return 'finish';\n        if (currentStep === stepIndex) {\n            return taskStatus.status === 'running' ? 'process' : 'wait';\n        }\n        return 'wait';\n    };\n\n    // 启动时自动开始抓取\n    useEffect(() => {\n        if (visible && !taskStatus) {\n            startRealtimeCrawl();\n        }\n    }, [visible, taskStatus, startRealtimeCrawl]);\n\n    // 清理WebSocket连接\n    useEffect(() => {\n        return () => {\n            if (socket) {\n                try { socket.close(); } catch {}\n            }\n        };\n    }, [socket]);\n\n    return (\n        <Modal\n            title={\n                <Space>\n                    <SyncOutlined spin={taskStatus?.status === 'running'} />\n                    <span>实时股权穿透分析</span>\n                </Space>\n            }\n            open={visible}\n            onCancel={handleClose}\n            width={800}\n            footer={[\n                <Button key=\"cancel\" onClick={handleCancel} disabled={isStarting}>\n                    {taskStatus?.status === 'running' ? '取消任务' : '关闭'}\n                </Button>,\n                taskStatus?.status === 'completed' && (\n                    <Button key=\"close\" type=\"primary\" onClick={handleClose}>\n                        查看结果\n                    </Button>\n                )\n            ]}\n            maskClosable={false}\n        >\n            <div style={{ minHeight: '500px' }}>\n                {/* 公司信息和状态 */}\n                <Card size=\"small\" style={{ marginBottom: 16 }}>\n                    <Row gutter={16}>\n                        <Col span={8}>\n                            <Statistic title=\"目标公司\" value={companyName} />\n                        </Col>\n                        <Col span={8}>\n                            <Statistic\n                                title=\"已用时间\"\n                                value={getElapsedTime()}\n                                suffix=\"秒\"\n                                prefix={<SyncOutlined spin={taskStatus?.status === 'running'} />}\n                            />\n                        </Col>\n                        <Col span={8}>\n                            <Statistic\n                                title=\"预计时间\"\n                                value={estimatedDuration}\n                                suffix=\"秒\"\n                            />\n                        </Col>\n                    </Row>\n                </Card>\n\n                {/* 进度步骤 */}\n                <Steps\n                    current={currentStep}\n                    size=\"small\"\n                    style={{ marginBottom: 24 }}\n                >\n                    <Step\n                        title=\"搜索公司\"\n                        icon={getStepStatus(0) === 'process' ? <SyncOutlined spin /> : undefined}\n                        status={getStepStatus(0)}\n                    />\n                    <Step\n                        title=\"构建队列\"\n                        icon={getStepStatus(1) === 'process' ? <SyncOutlined spin /> : undefined}\n                        status={getStepStatus(1)}\n                    />\n                    <Step\n                        title=\"关系分析\"\n                        icon={getStepStatus(2) === 'process' ? <SyncOutlined spin /> : undefined}\n                        status={getStepStatus(2)}\n                    />\n                    <Step\n                        title=\"数据整理\"\n                        icon={getStepStatus(3) === 'process' ? <SyncOutlined spin /> : undefined}\n                        status={getStepStatus(3)}\n                    />\n                    <Step\n                        title=\"完成\"\n                        icon={taskStatus?.status === 'completed' ? <TrophyOutlined /> : undefined}\n                        status={getStepStatus(4)}\n                    />\n                </Steps>\n\n                {/* 进度条 */}\n                <Progress\n                    percent={taskStatus?.progress || 0}\n                    status={\n                        taskStatus?.status === 'failed' ? 'exception' :\n                        taskStatus?.status === 'completed' ? 'success' :\n                        'active'\n                    }\n                    showInfo={true}\n                    style={{ marginBottom: 16 }}\n                />\n\n                {/* 统计信息 */}\n                {taskStatus && (\n                    <Row gutter={16} style={{ marginBottom: 16 }}>\n                        <Col span={8}>\n                            <Card size=\"small\">\n                                <Statistic\n                                    title=\"已处理企业\"\n                                    value={taskStatus.processed_companies}\n                                    prefix={<CheckCircleOutlined />}\n                                />\n                            </Card>\n                        </Col>\n                        <Col span={8}>\n                            <Card size=\"small\">\n                                <Statistic\n                                    title=\"总步骤\"\n                                    value={taskStatus.total_steps || 0}\n                                />\n                            </Card>\n                        </Col>\n                        <Col span={8}>\n                            <Card size=\"small\">\n                                <Statistic\n                                    title=\"当前状态\"\n                                    value={\n                                        taskStatus.status === 'running' ? '运行中' :\n                                        taskStatus.status === 'completed' ? '已完成' :\n                                        taskStatus.status === 'failed' ? '失败' :\n                                        taskStatus.status === 'pending' ? '等待中' : '未知'\n                                    }\n                                    valueStyle={{\n                                        color: taskStatus.status === 'completed' ? '#3f8600' :\n                                               taskStatus.status === 'failed' ? '#cf1322' : '#1890ff'\n                                    }}\n                                />\n                            </Card>\n                        </Col>\n                    </Row>\n                )}\n\n                {/* 实时日志 */}\n                <Card\n                    title={\n                        <Space>\n                            <SyncOutlined spin={taskStatus?.status === 'running'} />\n                            <span>实时日志</span>\n                        </Space>\n                    }\n                    size=\"small\"\n                >\n                    <div style={{ height: '200px', overflowY: 'auto', backgroundColor: '#fafafa', padding: '8px' }}>\n                        {logs.length === 0 ? (\n                            <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>\n                                <Spin />\n                                <div style={{ marginTop: 8 }}>等待日志信息...</div>\n                            </div>\n                        ) : (\n                            <List\n                                size=\"small\"\n                                dataSource={logs}\n                                renderItem={(log) => (\n                                    <List.Item style={{ padding: '4px 0', borderBottom: 'none' }}>\n                                        <Space size=\"small\" style={{ width: '100%' }}>\n                                            <Text type=\"secondary\" style={{ fontSize: '10px', minWidth: '60px' }}>\n                                                {new Date(log.timestamp).toLocaleTimeString()}\n                                            </Text>\n                                            <Tag color={getLogColor(log.level)}>\n                                                {log.level.toUpperCase()}\n                                            </Tag>\n                                            <Text style={{ fontSize: '12px', flex: 1 }}>\n                                                {log.message}\n                                            </Text>\n                                        </Space>\n                                    </List.Item>\n                                )}\n                            />\n                        )}\n                        <div ref={logsEndRef} />\n                    </div>\n                </Card>\n\n                {/* 错误信息 */}\n                {taskStatus?.error_message && (\n                    <Alert\n                        message=\"分析失败\"\n                        description={taskStatus.error_message}\n                        type=\"error\"\n                        showIcon\n                        style={{ marginTop: 16 }}\n                    />\n                )}\n\n                {/* 完成提示 */}\n                {taskStatus?.status === 'completed' && (\n                    <Alert\n                        message=\"分析完成！\"\n                        description={`股权穿透分析已完成，共耗时 ${taskStatus.duration || 0} 秒。点击\"查看结果\"按钮查看详细的股权关系图谱。`}\n                        type=\"success\"\n                        showIcon\n                        style={{ marginTop: 16 }}\n                    />\n                )}\n            </div>\n        </Modal>\n    );\n};\n\nexport default RealtimeCrawlModal;\n"], "names": ["Text", "Typography", "Step", "Steps", "RealtimeCrawlModal", "visible", "companyName", "onClose", "onComplete", "onCancel", "taskStatus", "setTaskStatus", "useState", "logs", "setLogs", "currentStep", "setCurrentStep", "isStarting", "setIsStarting", "socket", "setSocket", "estimatedDuration", "setEstimatedDuration", "startTime", "setStartTime", "logsEndRef", "useRef", "scrollToBottom", "_a", "useEffect", "startRealtimeCrawl", "useCallback", "response", "contentType", "data", "rawText", "detail", "taskId", "wsPath", "wsUrl", "ws", "addLog", "event", "msg", "prev", "updateCurrentStep", "fetchCrawlResult", "e", "error", "result", "level", "message", "phase", "newLog", "progress", "handleCancel", "handleClose", "getElapsedTime", "getLogColor", "getStepStatus", "stepIndex", "jsx", "Modal", "Space", "SyncOutlined", "<PERSON><PERSON>", "Card", "jsxs", "Row", "Col", "Statistic", "TrophyOutlined", "Progress", "CheckCircleOutlined", "Spin", "List", "log", "Tag", "<PERSON><PERSON>"], "mappings": "oNAwBA,KAAM,CAAE,KAAAA,GAASC,GACX,CAAE,KAAAC,GAASC,EAgCJC,GAAwD,CAAC,CAClE,QAAAC,EACA,YAAAC,EACA,QAAAC,EACA,WAAAC,EACA,SAAAC,CACJ,IAAM,CACF,KAAM,CAACC,EAAYC,CAAa,EAAIC,EAAAA,SAA4B,IAAI,EAC9D,CAACC,EAAMC,CAAO,EAAIF,EAAAA,SAAqB,CAAA,CAAE,EACzC,CAACG,EAAaC,CAAc,EAAIJ,EAAAA,SAAS,CAAC,EAC1C,CAACK,EAAYC,CAAa,EAAIN,EAAAA,SAAS,EAAK,EAC5C,CAACO,EAAQC,CAAS,EAAIR,EAAAA,SAA2B,IAAI,EACrD,CAACS,EAAmBC,CAAoB,EAAIV,EAAAA,SAAS,CAAC,EACtD,CAACW,EAAWC,CAAY,EAAIZ,EAAAA,SAAsB,IAAI,EAEtDa,EAAaC,EAAAA,OAAuB,IAAI,EAIxCC,EAAiB,IAAM,QACzBC,EAAAH,EAAW,UAAX,MAAAG,EAAoB,eAAe,CAAE,SAAU,UACnD,EAEAC,EAAAA,UAAU,IAAM,CACZF,EAAA,CACJ,EAAG,CAACd,CAAI,CAAC,EAGT,MAAMiB,EAAqBC,EAAAA,YAAY,SAAY,CAC/C,GAAKzB,EAEL,CAAAY,EAAc,EAAI,EAClBJ,EAAQ,CAAA,CAAE,EACVU,EAAa,IAAI,IAAM,EAEvB,GAAI,CACA,MAAMQ,EAAW,MAAM,MAAM,mCAAoC,CAC7D,OAAQ,OACR,QAAS,CACL,eAAgB,mBAChB,cAAiB,UAAU,aAAa,QAAQ,OAAO,CAAC,EAAA,EAE5D,KAAM,KAAK,UAAU,CACjB,aAAc1B,EACd,MAAO,EACP,UAAW,MAAA,CACd,CAAA,CACJ,EAGK2B,EAAcD,EAAS,QAAQ,IAAI,cAAc,GAAK,GAC5D,IAAIE,EAAY,KACZC,EAAU,GACd,GAAI,CACIF,EAAY,SAAS,kBAAkB,EACvCC,EAAO,MAAMF,EAAS,KAAA,EAEtBG,EAAU,MAAMH,EAAS,KAAA,CAEjC,MAAY,CACR,GAAI,CAAEG,EAAU,MAAMH,EAAS,KAAA,CAAQ,MAAQ,CAAC,CACpD,CAEA,GAAI,CAACA,EAAS,GAAI,CACd,MAAMI,GAASF,GAAA,YAAAA,EAAM,SAAUC,GAAW,gBAAgBH,EAAS,MAAM,IACzE,MAAM,IAAI,MAAMI,CAAM,CAC1B,CAEAd,EAAqBY,EAAK,kBAAkB,EAG5C,MAAMG,GAASH,GAAA,YAAAA,EAAM,UAAW,KAAK,MAAMC,GAAW,IAAI,EAAE,QAC5D,GAAI,CAACE,EACD,MAAM,IAAI,MAAM,WAAW,EAE/B,MAAMC,EAAS,iCAAiCD,CAAM,GAEhDE,GAAQ,GADG,OAAO,SAAS,WAAa,SAAW,OAAS,KACzC,KAAK,OAAO,SAAS,IAAI,GAAGD,CAAM,GACrDE,EAAK,IAAI,UAAUD,EAAK,EAE9BnB,EAAUoB,CAAE,EAGZA,EAAG,iBAAiB,OAAQ,IAAM,CAC9BC,EAAO,OAAQ,YAAY,CAC/B,CAAC,EAEDD,EAAG,iBAAiB,UAAYE,GAAU,CACtC,GAAI,CACA,MAAMC,EAAM,KAAK,MAAMD,EAAM,IAAI,EACjC,OAAQC,EAAI,KAAA,CACR,IAAK,kBACDhC,EAAciC,GAAQA,GAAO,CAAE,GAAGA,EAAM,SAAW,OAAOD,EAAI,UAAa,SAAWA,EAAI,SAAWC,EAAK,QAAA,CAAkB,EACxH,OAAOD,EAAI,UAAa,UACxBE,EAAkBF,EAAI,QAAQ,EAElC,MACJ,IAAK,cACDF,EAAOE,EAAI,OAAS,OAAQA,EAAI,SAAW,GAAIA,EAAI,KAAK,EACxD,MACJ,IAAK,gBACDhC,EAAciC,GAAQA,GAAO,CAAE,GAAGA,EAAM,OAAQD,EAAI,QAAUC,EAAK,MAAA,CAAe,EAC9ED,EAAI,SAAW,aACfF,EAAO,UAAW,cAAcE,EAAI,UAAY,CAAC,IAAI,EACrDG,EAAiBH,EAAI,OAAO,GACrBA,EAAI,SAAW,UACtBF,EAAO,QAAS,WAAWE,EAAI,OAAS,MAAM,EAAE,EAEpD,MACJ,IAAK,QACDF,EAAO,QAAS,OAAOE,EAAI,eAAiB,MAAM,EAAE,EACpD,MACJ,QACI,KAAA,CAEZ,OAASI,EAAG,CACR,QAAQ,MAAM,YAAaA,CAAC,CAChC,CACJ,CAAC,EAEDP,EAAG,iBAAiB,QAAS,IAAM,CAC/BC,EAAO,UAAW,YAAY,CAClC,CAAC,EAEDD,EAAG,iBAAiB,QAAS,IAAM,CAC/BC,EAAO,QAAS,eAAe,CACnC,CAAC,EAGD9B,EAAc,CACV,GAAIuB,EAAK,QACT,aAAc5B,EACd,OAAQ,UACR,SAAU,EACV,YAAa,EACb,oBAAqB,EACrB,WAAY,IAAI,KAAA,EAAO,YAAA,CAAY,CACtC,EAEDmC,EAAO,UAAW,YAAYnC,CAAW,SAAS,EAClDmC,EAAO,OAAQ,QAAQP,EAAK,kBAAkB,IAAI,CAEtD,OAASc,EAAO,CACZ,QAAQ,MAAM,UAAWA,CAAK,EAC9BP,EAAO,QAAS,SAASO,aAAiB,MAAQA,EAAM,QAAU,MAAM,EAAE,CAC9E,QAAA,CACI9B,EAAc,EAAK,CACvB,EACJ,EAAG,CAACZ,CAAW,CAAC,EAGVwC,EAAmB,MAAOT,GAAmB,CAC/C,GAAI,CACA,MAAML,EAAW,MAAM,MAAM,4BAA4BK,CAAM,GAAI,CAC/D,QAAS,CACL,cAAiB,UAAU,aAAa,QAAQ,OAAO,CAAC,EAAA,CAC5D,CACH,EAED,GAAIL,EAAS,GAAI,CACb,MAAMiB,EAAS,MAAMjB,EAAS,KAAA,EAC9BxB,EAAWyC,EAAO,MAAM,CAC5B,CACJ,OAASD,EAAO,CACZ,QAAQ,MAAM,UAAWA,CAAK,EAC9BP,EAAO,QAAS,UAAU,CAC9B,CACJ,EAGMA,EAAS,CAACS,EAA0BC,EAAiBC,IAAmB,CAC1E,MAAMC,EAAmB,CACrB,UAAW,IAAI,KAAA,EAAO,YAAA,EACtB,MAAAH,EACA,QAAAC,EACA,MAAOC,GAAS,KAAA,EAGpBtC,EAAQ8B,GACY,CAAC,GAAGA,EAAMS,CAAM,EACjB,MAAM,IAAS,CACjC,CACL,EAGMR,EAAqBS,GAAqB,CACxCA,EAAW,GAAItC,EAAe,CAAC,EAC1BsC,EAAW,GAAItC,EAAe,CAAC,EAC/BsC,EAAW,GAAItC,EAAe,CAAC,EAC/BsC,EAAW,IAAKtC,EAAe,CAAC,IACrB,CAAC,CACzB,EAGMuC,EAAe,SAAY,CAC7B,GAAI7C,GAAA,MAAAA,EAAY,GACZ,GAAI,CACA,MAAM,MAAM,2BAA2BA,EAAW,EAAE,GAAI,CACpD,OAAQ,OACR,QAAS,CACL,cAAiB,UAAU,aAAa,QAAQ,OAAO,CAAC,EAAA,CAC5D,CACH,EACD+B,EAAO,UAAW,OAAO,CAC7B,OAASO,EAAO,CACZ,QAAQ,MAAM,UAAWA,CAAK,CAClC,CAGJ,GAAI7B,EAAQ,CACR,GAAI,CAAEA,EAAO,MAAA,CAAS,MAAQ,CAAC,CAC/BC,EAAU,IAAI,CAClB,CAEAX,EAAA,CACJ,EAGM+C,EAAc,IAAM,CACtB,GAAIrC,EAAQ,CACR,GAAI,CAAEA,EAAO,MAAA,CAAS,MAAQ,CAAC,CAC/BC,EAAU,IAAI,CAClB,CACAb,EAAA,CACJ,EAGMkD,EAAiB,IACdlC,EACE,KAAK,OAAO,KAAK,IAAA,EAAQA,EAAU,QAAA,GAAa,GAAI,EADpC,EAKrBmC,EAAeR,IACF,CACX,KAAQ,UACR,QAAW,UACX,QAAW,UACX,MAAS,QACT,SAAY,YAAA,GAEFA,CAAK,GAAK,UAItBS,EAAiBC,GACdlD,EAEDA,EAAW,SAAW,UAAYK,GAAe6C,EAAkB,QACnE7C,EAAc6C,EAAkB,SAChC7C,IAAgB6C,GACTlD,EAAW,SAAW,UAAY,UAEtC,OAPiB,OAW5BmB,OAAAA,EAAAA,UAAU,IAAM,CACRxB,GAAW,CAACK,GACZoB,EAAA,CAER,EAAG,CAACzB,EAASK,EAAYoB,CAAkB,CAAC,EAG5CD,EAAAA,UAAU,IACC,IAAM,CACT,GAAIV,EACA,GAAI,CAAEA,EAAO,MAAA,CAAS,MAAQ,CAAC,CAEvC,EACD,CAACA,CAAM,CAAC,EAGP0C,EAAAA,IAACC,GAAA,CACG,aACKC,EAAA,CACG,SAAA,CAAAF,EAAAA,IAACG,EAAA,CAAa,MAAMtD,GAAA,YAAAA,EAAY,UAAW,UAAW,EACtDmD,EAAAA,IAAC,QAAK,SAAA,UAAA,CAAQ,CAAA,EAClB,EAEJ,KAAMxD,EACN,SAAUmD,EACV,MAAO,IACP,OAAQ,CACJK,EAAAA,IAACI,EAAA,CAAoB,QAASV,EAAc,SAAUtC,EACjD,UAAAP,GAAA,YAAAA,EAAY,UAAW,UAAY,OAAS,IAAA,EADrC,QAEZ,GACAA,GAAA,YAAAA,EAAY,UAAW,aACnBmD,EAAAA,IAACI,EAAA,CAAmB,KAAK,UAAU,QAAST,EAAa,SAAA,MAAA,EAA7C,OAEZ,CAAA,EAGR,aAAc,GAEd,gBAAC,MAAA,CAAI,MAAO,CAAE,UAAW,SAErB,SAAA,CAAAK,EAAAA,IAACK,EAAA,CAAK,KAAK,QAAQ,MAAO,CAAE,aAAc,EAAA,EACtC,SAAAC,EAAAA,KAACC,EAAA,CAAI,OAAQ,GACT,SAAA,CAAAP,EAAAA,IAACQ,EAAA,CAAI,KAAM,EACP,SAAAR,EAAAA,IAACS,GAAU,MAAM,OAAO,MAAOhE,CAAA,CAAa,CAAA,CAChD,EACAuD,EAAAA,IAACQ,EAAA,CAAI,KAAM,EACP,SAAAR,EAAAA,IAACS,EAAA,CACG,MAAM,OACN,MAAOb,EAAA,EACP,OAAO,IACP,OAAQI,EAAAA,IAACG,EAAA,CAAa,MAAMtD,GAAA,YAAAA,EAAY,UAAW,SAAA,CAAW,CAAA,CAAA,EAEtE,EACAmD,EAAAA,IAACQ,EAAA,CAAI,KAAM,EACP,SAAAR,EAAAA,IAACS,EAAA,CACG,MAAM,OACN,MAAOjD,EACP,OAAO,GAAA,CAAA,CACX,CACJ,CAAA,CAAA,CACJ,CAAA,CACJ,EAGA8C,EAAAA,KAAChE,EAAA,CACG,QAASY,EACT,KAAK,QACL,MAAO,CAAE,aAAc,EAAA,EAEvB,SAAA,CAAA8C,EAAAA,IAAC3D,EAAA,CACG,MAAM,OACN,KAAMyD,EAAc,CAAC,IAAM,UAAYE,EAAAA,IAACG,EAAA,CAAa,KAAI,EAAA,CAAC,EAAK,OAC/D,OAAQL,EAAc,CAAC,CAAA,CAAA,EAE3BE,EAAAA,IAAC3D,EAAA,CACG,MAAM,OACN,KAAMyD,EAAc,CAAC,IAAM,UAAYE,EAAAA,IAACG,EAAA,CAAa,KAAI,EAAA,CAAC,EAAK,OAC/D,OAAQL,EAAc,CAAC,CAAA,CAAA,EAE3BE,EAAAA,IAAC3D,EAAA,CACG,MAAM,OACN,KAAMyD,EAAc,CAAC,IAAM,UAAYE,EAAAA,IAACG,EAAA,CAAa,KAAI,EAAA,CAAC,EAAK,OAC/D,OAAQL,EAAc,CAAC,CAAA,CAAA,EAE3BE,EAAAA,IAAC3D,EAAA,CACG,MAAM,OACN,KAAMyD,EAAc,CAAC,IAAM,UAAYE,EAAAA,IAACG,EAAA,CAAa,KAAI,EAAA,CAAC,EAAK,OAC/D,OAAQL,EAAc,CAAC,CAAA,CAAA,EAE3BE,EAAAA,IAAC3D,EAAA,CACG,MAAM,KACN,MAAMQ,GAAA,YAAAA,EAAY,UAAW,YAAcmD,EAAAA,IAACU,KAAe,EAAK,OAChE,OAAQZ,EAAc,CAAC,CAAA,CAAA,CAC3B,CAAA,CAAA,EAIJE,EAAAA,IAACW,GAAA,CACG,SAAS9D,GAAA,YAAAA,EAAY,WAAY,EACjC,QACIA,GAAA,YAAAA,EAAY,UAAW,SAAW,aAClCA,GAAA,YAAAA,EAAY,UAAW,YAAc,UACrC,SAEJ,SAAU,GACV,MAAO,CAAE,aAAc,EAAA,CAAG,CAAA,EAI7BA,UACI0D,EAAA,CAAI,OAAQ,GAAI,MAAO,CAAE,aAAc,EAAA,EACpC,SAAA,CAAAP,EAAAA,IAACQ,GAAI,KAAM,EACP,SAAAR,MAACK,EAAA,CAAK,KAAK,QACP,SAAAL,EAAAA,IAACS,EAAA,CACG,MAAM,QACN,MAAO5D,EAAW,oBAClB,aAAS+D,GAAA,CAAA,CAAoB,CAAA,CAAA,EAErC,CAAA,CACJ,QACCJ,EAAA,CAAI,KAAM,EACP,SAAAR,MAACK,EAAA,CAAK,KAAK,QACP,SAAAL,EAAAA,IAACS,EAAA,CACG,MAAM,MACN,MAAO5D,EAAW,aAAe,CAAA,CAAA,EAEzC,CAAA,CACJ,QACC2D,EAAA,CAAI,KAAM,EACP,SAAAR,MAACK,EAAA,CAAK,KAAK,QACP,SAAAL,EAAAA,IAACS,EAAA,CACG,MAAM,OACN,MACI5D,EAAW,SAAW,UAAY,MAClCA,EAAW,SAAW,YAAc,MACpCA,EAAW,SAAW,SAAW,KACjCA,EAAW,SAAW,UAAY,MAAQ,KAE9C,WAAY,CACR,MAAOA,EAAW,SAAW,YAAc,UACpCA,EAAW,SAAW,SAAW,UAAY,SAAA,CACxD,CAAA,EAER,CAAA,CACJ,CAAA,EACJ,EAIJmD,EAAAA,IAACK,EAAA,CACG,aACKH,EAAA,CACG,SAAA,CAAAF,EAAAA,IAACG,EAAA,CAAa,MAAMtD,GAAA,YAAAA,EAAY,UAAW,UAAW,EACtDmD,EAAAA,IAAC,QAAK,SAAA,MAAA,CAAI,CAAA,EACd,EAEJ,KAAK,QAEL,SAAAM,EAAAA,KAAC,MAAA,CAAI,MAAO,CAAE,OAAQ,QAAS,UAAW,OAAQ,gBAAiB,UAAW,QAAS,KAAA,EAClF,SAAA,CAAAtD,EAAK,SAAW,EACbsD,EAAAA,KAAC,MAAA,CAAI,MAAO,CAAE,UAAW,SAAU,MAAO,OAAQ,QAAS,MAAA,EACvD,SAAA,CAAAN,EAAAA,IAACa,GAAA,EAAK,QACL,MAAA,CAAI,MAAO,CAAE,UAAW,CAAA,EAAK,SAAA,WAAA,CAAS,CAAA,CAAA,CAC3C,EAEAb,EAAAA,IAACc,EAAA,CACG,KAAK,QACL,WAAY9D,EACZ,WAAa+D,GACTf,EAAAA,IAACc,EAAK,KAAL,CAAU,MAAO,CAAE,QAAS,QAAS,aAAc,QAChD,gBAACZ,EAAA,CAAM,KAAK,QAAQ,MAAO,CAAE,MAAO,MAAA,EAChC,SAAA,CAAAF,EAAAA,IAAC7D,GAAK,KAAK,YAAY,MAAO,CAAE,SAAU,OAAQ,SAAU,MAAA,EACvD,aAAI,KAAK4E,EAAI,SAAS,EAAE,qBAC7B,EACAf,EAAAA,IAACgB,GAAA,CAAI,MAAOnB,EAAYkB,EAAI,KAAK,EAC5B,SAAAA,EAAI,MAAM,YAAA,CAAY,CAC3B,EACAf,EAAAA,IAAC7D,EAAA,CAAK,MAAO,CAAE,SAAU,OAAQ,KAAM,CAAA,EAClC,SAAA4E,EAAI,OAAA,CACT,CAAA,CAAA,CACJ,CAAA,CACJ,CAAA,CAAA,EAIZf,EAAAA,IAAC,MAAA,CAAI,IAAKpC,CAAA,CAAY,CAAA,CAAA,CAC1B,CAAA,CAAA,GAIHf,GAAA,YAAAA,EAAY,gBACTmD,EAAAA,IAACiB,EAAA,CACG,QAAQ,OACR,YAAapE,EAAW,cACxB,KAAK,QACL,SAAQ,GACR,MAAO,CAAE,UAAW,EAAA,CAAG,CAAA,GAK9BA,GAAA,YAAAA,EAAY,UAAW,aACpBmD,EAAAA,IAACiB,EAAA,CACG,QAAQ,QACR,YAAa,iBAAiBpE,EAAW,UAAY,CAAC,4BACtD,KAAK,UACL,SAAQ,GACR,MAAO,CAAE,UAAW,EAAA,CAAG,CAAA,CAC3B,CAAA,CAER,CAAA,CAAA,CAGZ"}