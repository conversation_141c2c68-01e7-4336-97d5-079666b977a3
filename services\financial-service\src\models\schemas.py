from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, date
from decimal import Decimal


# 基础实体模型
class Company(BaseModel):
    """公司实体模型"""
    code: str = Field(..., description="公司代码/统一社会信用代码")
    name: str = Field(..., description="公司名称")
    legal_representative: Optional[str] = Field(None, description="法定代表人")
    registered_capital: Optional[Decimal] = Field(None, description="注册资本")
    establishment_date: Optional[date] = Field(None, description="成立日期")
    company_type: Optional[str] = Field(None, description="公司类型")
    business_scope: Optional[str] = Field(None, description="经营范围")
    address: Optional[str] = Field(None, description="注册地址")
    status: Optional[str] = Field(None, description="经营状态")


class Person(BaseModel):
    """个人实体模型"""
    id: str = Field(..., description="个人ID")
    name: str = Field(..., description="姓名")
    id_number: Optional[str] = Field(None, description="身份证号")
    nationality: Optional[str] = Field(None, description="国籍")


# 股权关系模型
class ShareholdingRelation(BaseModel):
    """股权关系模型"""
    shareholder_id: str = Field(..., description="股东ID")
    shareholder_name: str = Field(..., description="股东名称")
    shareholder_type: str = Field(..., description="股东类型：company/person")
    company_id: str = Field(..., description="被投资公司ID")
    company_name: str = Field(..., description="被投资公司名称")
    shareholding_ratio: Decimal = Field(..., description="持股比例", ge=0, le=100)
    investment_amount: Optional[Decimal] = Field(None, description="投资金额")
    investment_date: Optional[date] = Field(None, description="投资日期")


# 股权穿透请求和响应
class EquityPenetrationRequest(BaseModel):
    """股权穿透分析请求"""
    company_id: str = Field(..., description="公司名称")
    max_depth: int = Field(5, gt=0, le=15, description="查询深度")


class EquityNode(BaseModel):
    """股权节点"""
    id: str = Field(..., description="节点ID")
    name: str = Field(..., description="节点名称")
    type: str = Field(..., description="节点类型：company/person")
    level: int = Field(..., description="层级")
    total_ratio: Decimal = Field(..., description="累计持股比例")
    direct_ratio: Optional[Decimal] = Field(None, description="直接持股比例")


class EquityEdge(BaseModel):
    """股权边"""
    source: str = Field(..., description="源节点ID")
    target: str = Field(..., description="目标节点ID")
    ratio: Decimal = Field(..., description="持股比例")
    amount: Optional[Decimal] = Field(None, description="投资金额")


class EquityPenetrationResponse(BaseModel):
    """股权穿透分析响应"""
    company_name: str
    graph_data: Dict[str, Any]
    analysis_depth: int
    analysis_time: datetime


# 股权质押模型
class PledgeRecord(BaseModel):
    """股权质押记录"""
    pledge_id: str = Field(..., description="质押记录ID")
    company_code: str = Field(..., description="公司代码")
    company_name: str = Field(..., description="公司名称")
    pledger: str = Field(..., description="出质人")
    pledgee: str = Field(..., description="质权人")
    pledged_shares: Decimal = Field(..., description="质押股份数量")
    pledged_ratio: Decimal = Field(..., description="质押股份比例")
    pledge_date: date = Field(..., description="质押日期")
    release_date: Optional[date] = Field(None, description="解质日期")
    pledge_purpose: Optional[str] = Field(None, description="质押用途")
    status: str = Field(..., description="质押状态：active/released/expired")


class PledgeAnalysisRequest(BaseModel):
    """股权质押分析请求"""
    company_code: str = Field(..., description="公司统一社会信用代码")
    include_subsidiaries: bool = Field(default=True, description="是否包含子公司")
    date_range: Optional[Dict[str, date]] = Field(None, description="日期范围")


class PledgeAnalysisResponse(BaseModel):
    """股权质押分析响应"""
    company: Company = Field(..., description="公司信息")
    pledge_records: List[PledgeRecord] = Field(..., description="质押记录")
    total_pledged_ratio: Decimal = Field(..., description="总质押比例")
    active_pledges_count: int = Field(..., description="有效质押数量")
    risk_assessment: Dict[str, Any] = Field(..., description="风险评估")
    trend_analysis: Dict[str, Any] = Field(..., description="趋势分析")


# 债券分析模型
class BondInfo(BaseModel):
    """债券基本信息"""
    bond_code: str = Field(..., description="债券代码")
    bond_name: str = Field(..., description="债券名称")
    issuer: str = Field(..., description="发行人")
    bond_type: str = Field(..., description="债券类型")
    issue_date: date = Field(..., description="发行日期")
    maturity_date: date = Field(..., description="到期日期")
    face_value: Decimal = Field(..., description="面值")
    issue_size: Decimal = Field(..., description="发行规模")
    coupon_rate: Decimal = Field(..., description="票面利率")
    rating: Optional[str] = Field(None, description="信用评级")


class BondPriceData(BaseModel):
    """债券价格数据"""
    trade_date: date = Field(..., description="日期")
    open_price: Decimal = Field(..., description="开盘价")
    close_price: Decimal = Field(..., description="收盘价")
    high_price: Decimal = Field(..., description="最高价")
    low_price: Decimal = Field(..., description="最低价")
    volume: Decimal = Field(..., description="成交量")
    yield_rate: Optional[Decimal] = Field(None, description="到期收益率")


class BondAnalysisRequest(BaseModel):
    """债券分析请求"""
    bond_codes: List[str] = Field(..., description="债券代码列表", min_items=1)
    analysis_period: int = Field(default=30, description="分析周期(天)", ge=1, le=365)
    include_comparison: bool = Field(default=True, description="是否包含比较分析")


class BondAnalysisResponse(BaseModel):
    """债券分析响应"""
    bonds: List[BondInfo] = Field(..., description="债券信息列表")
    price_data: Dict[str, List[BondPriceData]] = Field(..., description="价格数据")
    performance_metrics: Dict[str, Any] = Field(..., description="绩效指标")
    risk_metrics: Dict[str, Any] = Field(..., description="风险指标")
    market_analysis: Dict[str, Any] = Field(..., description="市场分析")
    recommendations: List[str] = Field(..., description="投资建议")


# 通用响应模型
class AnalysisResponse(BaseModel):
    """通用分析响应"""
    success: bool = Field(True, description="分析是否成功")
    message: str = Field("", description="响应消息")
    data: Dict[str, Any] = Field(..., description="分析结果数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="分析时间戳")
    processing_time: Optional[float] = Field(None, description="处理时间(秒)")