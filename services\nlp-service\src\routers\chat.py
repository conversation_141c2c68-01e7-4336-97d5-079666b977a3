from fastapi import APIRouter, HTTPException, Depends
import logging
import time
import uuid
from typing import Dict, Any

from ..models.schemas import ChatRequest, ChatResponse, BaseResponse
from ..services.ai_service import AIService

router = APIRouter()
logger = logging.getLogger(__name__)


async def get_ai_service() -> AIService:
    """依赖注入：获取AI服务实例"""
    from main import app
    return app.state.ai_service


@router.post("/message", response_model=ChatResponse)
async def chat_message(
    request: ChatRequest,
    ai_service: AIService = Depends(get_ai_service)
):
    """处理聊天消息"""
    start_time = time.time()
    
    try:
        logger.info(f"Processing chat message: {request.message[:50]}...")
        
        # 生成会话ID（如果没有提供）
        if not request.conversation_id:
            request.conversation_id = str(uuid.uuid4())
        
        # 调用AI服务生成回复
        response_text, tokens_used = await ai_service.generate_chat_response(
            message=request.message,
            conversation_id=request.conversation_id,
            user_id=request.user_id,
            context=request.context
        )
        
        processing_time = time.time() - start_time
        
        logger.info(f"Chat response generated in {processing_time:.2f}s")
        
        return ChatResponse(
            response=response_text,
            conversation_id=request.conversation_id,
            confidence=0.9,  # 可以根据实际模型输出计算
            tokens_used=tokens_used,
            response_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"Chat message error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process chat message: {str(e)}"
        )


@router.get("/conversation/{conversation_id}")
async def get_conversation_history(
    conversation_id: str,
    ai_service: AIService = Depends(get_ai_service)
):
    """获取会话历史"""
    try:
        history = await ai_service.get_conversation_history(conversation_id)
        
        return {
            "success": True,
            "conversation_id": conversation_id,
            "messages": history,
            "message_count": len(history)
        }
        
    except Exception as e:
        logger.error(f"Get conversation history error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get conversation history: {str(e)}"
        )


@router.delete("/conversation/{conversation_id}")
async def clear_conversation(
    conversation_id: str,
    ai_service: AIService = Depends(get_ai_service)
):
    """清除会话历史"""
    try:
        await ai_service.clear_conversation(conversation_id)
        
        return BaseResponse(
            success=True,
            message=f"Conversation {conversation_id} cleared successfully"
        )
        
    except Exception as e:
        logger.error(f"Clear conversation error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear conversation: {str(e)}"
        )


@router.get("/conversations/user/{user_id}")
async def get_user_conversations(
    user_id: str,
    ai_service: AIService = Depends(get_ai_service)
):
    """获取用户的所有会话"""
    try:
        conversations = await ai_service.get_user_conversations(user_id)
        
        return {
            "success": True,
            "user_id": user_id,
            "conversations": conversations,
            "total_count": len(conversations)
        }
        
    except Exception as e:
        logger.error(f"Get user conversations error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get user conversations: {str(e)}"
        )


@router.post("/system-prompt")
async def update_system_prompt(
    prompt_data: Dict[str, Any],
    ai_service: AIService = Depends(get_ai_service)
):
    """更新系统提示词"""
    try:
        system_prompt = prompt_data.get("system_prompt")
        if not system_prompt:
            raise HTTPException(
                status_code=400,
                detail="system_prompt is required"
            )
        
        await ai_service.update_system_prompt(system_prompt)
        
        return BaseResponse(
            success=True,
            message="System prompt updated successfully"
        )
        
    except Exception as e:
        logger.error(f"Update system prompt error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update system prompt: {str(e)}"
        )