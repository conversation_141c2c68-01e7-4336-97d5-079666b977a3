from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
import uvicorn
import logging
from contextlib import asynccontextmanager

from src.routers import chat, sentiment
from src.config.settings import settings
from src.services.ai_service import AIService


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("🤖 Initializing NLP Service...")
    
    # 初始化AI服务
    ai_service = AIService()
    await ai_service.initialize()
    app.state.ai_service = ai_service
    
    logger.info("✅ NLP Service initialized successfully")
    
    yield
    
    # 关闭时清理
    logger.info("🛑 Shutting down NLP Service...")


# 创建FastAPI应用
app = FastAPI(
    title="IDEALAB NLP Service",
    description="AI-powered NLP service for chatbot and sentiment analysis",
    version="1.0.0",
    lifespan=lifespan
)

# 中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "service": "nlp-service",
        "status": "healthy",
        "version": "1.0.0",
        "models": {
            "chatbot": "initialized",
            "sentiment": "initialized"
        }
    }


@app.get("/")
async def root():
    """根端点"""
    return {
        "message": "IDEALAB NLP Service",
        "version": "1.0.0",
        "endpoints": {
            "chat": "/api/chat/*",
            "sentiment": "/api/sentiment/*",
            "health": "/health"
        }
    }


# 注册路由
app.include_router(chat.router, prefix="/api/chat", tags=["Chatbot"])
app.include_router(sentiment.router, prefix="/api/sentiment", tags=["Sentiment Analysis"])


# 错误处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"Global exception: {exc}")
    return HTTPException(
        status_code=500,
        detail="Internal NLP Service Error"
    )


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )