import hashlib
import pandas as pd
import psycopg2
from sqlalchemy import create_engine
from datetime import datetime
import akshare as ak
import numpy as np

# 数据库配置
DB_CONFIG = {
    "dbname": "idealab",
    "user": "postgres",
    "password": "123456",
    "host": "localhost",
    "port": "5432"
}

# 表结构定义
COMPANY_TABLE = "pledge_distribution_company"
BANK_TABLE = "pledge_distribution_bank"
UPDATE_LOG_TABLE = "data_update_log"

# 初始化数据库连接
def get_db_connection():
    return psycopg2.connect(**DB_CONFIG)

def get_sqlalchemy_engine():
    return create_engine(
        f"postgresql+psycopg2://{DB_CONFIG['user']}:{DB_CONFIG['password']}@" +
        f"{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['dbname']}"
    )

# 创建数据库表结构
def create_tables():
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # 创建证券公司质押分布表
    cursor.execute(f"""
    CREATE TABLE IF NOT EXISTS {COMPANY_TABLE} (
        id SERIAL PRIMARY KEY,
        serial_number INTEGER NOT NULL,
        pledge_agency TEXT NOT NULL,
        pledge_company_count INTEGER,
        pledge_count INTEGER,
        pledge_quantity BIGINT,
        below_warning_ratio FLOAT,
        between_warning_and_close_ratio FLOAT,
        above_close_ratio FLOAT,
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """)
    
    # 创建银行质押分布表
    cursor.execute(f"""
    CREATE TABLE IF NOT EXISTS {BANK_TABLE} (
        id SERIAL PRIMARY KEY,
        serial_number INTEGER NOT NULL,
        pledge_agency TEXT NOT NULL,
        pledge_company_count INTEGER,
        pledge_count INTEGER,
        pledge_quantity BIGINT,
        below_warning_ratio FLOAT,
        between_warning_and_close_ratio FLOAT,
        above_close_ratio FLOAT,
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """)
    
    # 创建数据更新日志表
    cursor.execute(f"""
    CREATE TABLE IF NOT EXISTS {UPDATE_LOG_TABLE} (
        id SERIAL PRIMARY KEY,
        data_type TEXT NOT NULL CHECK (data_type IN ('company', 'bank')),
        last_update_time TIMESTAMP NOT NULL,
        data_hash TEXT NOT NULL,
        record_count INTEGER NOT NULL
    )
    """)
    
    conn.commit()
    cursor.close()
    conn.close()

# 计算数据哈希值用于变更检测
def calculate_data_hash(df):
    """计算DataFrame的哈希值用于变更检测"""
    # 使用整个DataFrame的字符串表示计算哈希
    return hashlib.md5(df.to_string().encode()).hexdigest()

# 获取最新数据哈希值
def get_last_hash(data_type):
    conn = get_db_connection()
    cursor = conn.cursor()
    
    cursor.execute(f"""
    SELECT data_hash 
    FROM {UPDATE_LOG_TABLE} 
    WHERE data_type = %s 
    ORDER BY last_update_time DESC 
    LIMIT 1
    """, (data_type,))
    
    result = cursor.fetchone()
    cursor.close()
    conn.close()
    
    return result[0] if result else None

# 记录数据更新日志
def log_data_update(data_type, data_hash, record_count):
    conn = get_db_connection()
    cursor = conn.cursor()
    
    cursor.execute(f"""
    INSERT INTO {UPDATE_LOG_TABLE} 
    (data_type, last_update_time, data_hash, record_count)
    VALUES (%s, %s, %s, %s)
    """, (data_type, datetime.now(), data_hash, record_count))
    
    conn.commit()
    cursor.close()
    conn.close()

# 处理证券公司质押数据
def process_company_data():
    print("获取证券公司质押分布数据...")
    df = ak.stock_gpzy_distribute_statistics_company_em()
    
    # 数据清洗
    df = df.rename(columns={
        "质押机构": "pledge_agency",
        "质押公司数量": "pledge_company_count",
        "质押笔数": "pledge_count",
        "质押数量": "pledge_quantity",
        "未达预警线比例": "below_warning_ratio",
        "达到预警线未达平仓线比例": "between_warning_and_close_ratio",
        "达到平仓线比例": "above_close_ratio"
    })
    
    # 转换质押数量为整数
    df["pledge_quantity"] = df["pledge_quantity"].astype("int64")
    
    # 计算当前数据哈希
    current_hash = calculate_data_hash(df)
    last_hash = get_last_hash("company")
    
    if current_hash == last_hash:
        print("证券公司质押数据无变化，跳过更新")
        return False
    
    print("检测到证券公司质押数据变更，更新数据库...")
    
    # 清空旧数据
    engine = get_sqlalchemy_engine()
    with engine.connect() as conn:
        conn.execute(f"TRUNCATE TABLE {COMPANY_TABLE} CONTINUE IDENTITY")
    
    # 写入新数据
    df.to_sql(
        COMPANY_TABLE,
        engine,
        if_exists="append",
        index=False
    )
    
    # 记录更新日志
    log_data_update("company", current_hash, len(df))
    
    print(f"证券公司质押数据更新完成，共 {len(df)} 条记录")
    return True

# 处理银行质押数据
def process_bank_data():
    print("获取银行质押分布数据...")
    df = ak.stock_gpzy_distribute_statistics_bank_em()
    
    # 数据清洗
    df = df.rename(columns={
        "质押机构": "pledge_agency",
        "质押公司数量": "pledge_company_count",
        "质押笔数": "pledge_count",
        "质押数量": "pledge_quantity",
        "未达预警线比例": "below_warning_ratio",
        "达到预警线未达平仓线比例": "between_warning_and_close_ratio",
        "达到平仓线比例": "above_close_ratio"
    })
    
    # 转换质押数量为整数
    df["pledge_quantity"] = df["pledge_quantity"].astype("int64")
    
    # 计算当前数据哈希
    current_hash = calculate_data_hash(df)
    last_hash = get_last_hash("bank")
    
    if current_hash == last_hash:
        print("银行质押数据无变化，跳过更新")
        return False
    
    print("检测到银行质押数据变更，更新数据库...")
    
    # 清空旧数据
    engine = get_sqlalchemy_engine()
    with engine.connect() as conn:
        conn.execute(f"TRUNCATE TABLE {BANK_TABLE} CONTINUE IDENTITY")
    
    # 写入新数据
    df.to_sql(
        BANK_TABLE,
        engine,
        if_exists="append",
        index=False
    )
    
    # 记录更新日志
    log_data_update("bank", current_hash, len(df))
    
    print(f"银行质押数据更新完成，共 {len(df)} 条记录")
    return True

# 主函数
def main():
    print("="*50)
    print(f"股权质押数据更新开始 @ {datetime.now()}")
    print("="*50)
    
    # 确保表结构存在
    create_tables()
    
    # 处理两类数据
    company_updated = process_company_data()
    bank_updated = process_bank_data()
    
    if not company_updated and not bank_updated:
        print("所有数据均无变化，无需更新")
    
    print("="*50)
    print("数据更新流程完成")
    print("="*50)

if __name__ == "__main__":
    main()