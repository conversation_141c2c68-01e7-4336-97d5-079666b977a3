import React, { useState, useEffect, useCallback } from 'react';
import {
    Modal,
    Button,
    Space
} from 'antd';
import {
    SyncOutlined
} from '@ant-design/icons';
import { api } from '@/services/api';
import ProgressManager, { LogEntry, TaskStatus, ProgressStep } from './ProgressManager';

interface RealtimeCrawlModalProps {
    visible: boolean;
    companyName: string;
    onClose: () => void;
    onComplete: (result: any) => void;
    onCancel: () => void;
}

export const RealtimeCrawlModal: React.FC<RealtimeCrawlModalProps> = ({
    visible,
    companyName,
    onClose,
    onComplete,
    onCancel
}) => {
    const [taskStatus, setTaskStatus] = useState<TaskStatus | null>(null);
    const [logs, setLogs] = useState<LogEntry[]>([]);
    const [currentStep, setCurrentStep] = useState(0);
    const [isStarting, setIsStarting] = useState(false);
    const [socket, setSocket] = useState<WebSocket | null>(null);
    const [estimatedDuration, setEstimatedDuration] = useState(0);
    const [startTime, setStartTime] = useState<Date | null>(null);

    const MAX_LOGS = 100; // 限制日志数量，避免内存溢出

    // 定义进度步骤
    const progressSteps: ProgressStep[] = [
        { title: "搜索公司", description: "查找目标公司信息" },
        { title: "构建队列", description: "建立分析任务队列" },
        { title: "关系分析", description: "分析股权关系结构" },
        { title: "数据整理", description: "整理和验证数据" },
        { title: "完成", description: "分析完成" }
    ];

    // 启动实时抓取
    const startRealtimeCrawl = useCallback(async () => {
        if (!companyName) return;

        setIsStarting(true);
        setLogs([]);
        setStartTime(new Date());

        try {
            // 用统一 api 客户端，自动携带 token
            const data: any = await api.post('/equity/start-realtime-crawl', {
                company_name: companyName,
                depth: 2,
                direction: 'both'
            });

            setEstimatedDuration(data?.estimated_duration || 0);

            const taskId = data?.task_id;
            if (!taskId) {
                throw new Error('未能获取到任务ID');
            }
            // 使用 Server-Sent Events 替代 WebSocket
            const ssePath = `/api/equity/sse/crawl-progress/${taskId}`;
            const apiBase = (import.meta as any).env?.VITE_API_URL || '';

            let sseUrl: string;
            if (apiBase) {
                sseUrl = `${apiBase}${ssePath.replace('/api', '')}`;
            } else {
                sseUrl = `${window.location.origin}${ssePath}`;
            }

            console.log('Connecting to SSE:', sseUrl);
            const eventSource = new EventSource(sseUrl);
            setSocket(eventSource as any); // 复用socket状态

            // 处理 SSE 事件
            eventSource.addEventListener('open', () => {
                addLog('info', '已连接到实时进度服务');
            });

            eventSource.addEventListener('message', (event) => {
                try {
                    const msg = JSON.parse(event.data);
                    switch (msg.type) {
                        case 'connection_established':
                            console.log('SSE连接已确认:', msg.message);
                            break;
                        case 'progress_update':
                            setTaskStatus(prev => prev ? {
                                ...prev,
                                progress: (typeof msg.progress === 'number' ? msg.progress : prev.progress),
                                processed_items: msg.processed_companies || prev.processed_items
                            } : prev);
                            if (typeof msg.progress === 'number') {
                                updateCurrentStep(msg.progress);
                            }
                            break;
                        case 'log_message':
                            addLog(msg.level || 'info', msg.message || '', msg.phase);
                            break;
                        case 'status_change':
                            setTaskStatus(prev => prev ? { ...prev, status: msg.status || prev.status } : prev);
                            if (msg.status === 'completed') {
                                addLog('success', `🎉 分析完成！耗时 ${msg.duration || 0} 秒`);
                                fetchCrawlResult(msg.task_id);
                            } else if (msg.status === 'failed') {
                                addLog('error', `❌ 分析失败: ${msg.error || '未知错误'}`);
                            }
                            break;
                        case 'error':
                            addLog('error', `错误: ${msg.error_message || '未知错误'}`);
                            break;
                        default:
                            console.log('未知消息类型:', msg.type, msg);
                            break;
                    }
                } catch (e) {
                    console.error('SSE消息解析失败:', e);
                }
            });

            eventSource.addEventListener('error', () => {
                addLog('error', 'SSE连接发生错误');
                addLog('warning', '与服务器的连接已断开');
            });

            // 初始状态设置
            setTaskStatus({
                id: data.task_id,
                name: companyName,
                status: 'running',
                progress: 0,
                total_steps: 0,
                processed_items: 0,
                created_at: new Date().toISOString()
            });

            addLog('success', `🚀 开始分析 "${companyName}" 的股权结构`);
            addLog('info', `预计需要 ${data.estimated_duration} 秒`);

        } catch (error) {
            console.error('启动分析失败:', error);
            addLog('error', `启动失败: ${error instanceof Error ? error.message : '未知错误'}`);
        } finally {
            setIsStarting(false);
        }
    }, [companyName]);

    // 获取爬取结果
    const fetchCrawlResult = async (taskId: string) => {
        try {
            const response = await fetch(`/api/equity/crawl-result/${taskId}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });

            if (response.ok) {
                const result = await response.json();
                onComplete(result.result);
            }
        } catch (error) {
            console.error('获取结果失败:', error);
            addLog('error', '获取分析结果失败');
        }
    };

    // 添加日志
    const addLog = (level: LogEntry['level'], message: string, phase?: string) => {
        const newLog: LogEntry = {
            timestamp: new Date().toISOString(),
            level,
            message,
            phase: phase || '分析中'
        };

        setLogs(prev => {
            const newLogs = [...prev, newLog];
            return newLogs.slice(-MAX_LOGS); // 保持最新的100条日志
        });
    };

    // 更新当前步骤
    const updateCurrentStep = (progress: number) => {
        if (progress < 25) setCurrentStep(0);
        else if (progress < 50) setCurrentStep(1);
        else if (progress < 75) setCurrentStep(2);
        else if (progress < 100) setCurrentStep(3);
        else setCurrentStep(4);
    };

    // 取消任务
    const handleCancel = async () => {
        if (taskStatus?.id) {
            try {
                await api.post(`/equity/cancel-task/${taskStatus.id}`);
                addLog('warning', '任务已取消');
            } catch (error) {
                console.error('取消任务失败:', error);
            }
        }

        if (socket) {
            try {
                if ('close' in socket) socket.close();
                else if ('terminate' in socket) (socket as any).terminate();
            } catch {}
            setSocket(null);
        }

        onCancel();
    };

    // 关闭Modal
    const handleClose = () => {
        if (socket) {
            try {
                if ('close' in socket) socket.close();
                else if ('terminate' in socket) (socket as any).terminate();
            } catch {}
            setSocket(null);
        }
        onClose();
    };



    // 启动时自动开始抓取
    useEffect(() => {
        if (visible && !taskStatus) {
            startRealtimeCrawl();
        }
    }, [visible, taskStatus, startRealtimeCrawl]);

    // 清理连接
    useEffect(() => {
        return () => {
            if (socket) {
                try {
                    if ('close' in socket) socket.close();
                    else if ('terminate' in socket) (socket as any).terminate();
                } catch {}
            }
        };
    }, [socket]);

    return (
        <Modal
            title={
                <Space style={{ color: 'rgba(255, 255, 255, 0.95)' }}>
                    <SyncOutlined
                        spin={taskStatus?.status === 'running'}
                        style={{ color: '#2563eb' }}
                    />
                    <span style={{
                        fontSize: '18px',
                        fontWeight: '600',
                        background: 'linear-gradient(135deg, #2563eb 0%, #0891b2 100%)',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent'
                    }}>
                        实时股权穿透分析
                    </span>
                </Space>
            }
            open={visible}
            onCancel={handleClose}
            width={800}
            footer={[
                <Button
                    key="cancel"
                    onClick={handleCancel}
                    disabled={isStarting}
                    style={{
                        backgroundColor: 'rgba(255, 255, 255, 0.05)',
                        borderColor: 'rgba(255, 255, 255, 0.15)',
                        color: 'rgba(255, 255, 255, 0.95)',
                        borderRadius: '8px',
                        height: '36px'
                    }}
                >
                    {taskStatus?.status === 'running' ? '取消任务' : '关闭'}
                </Button>,
                taskStatus?.status === 'completed' && (
                    <Button
                        key="close"
                        type="primary"
                        onClick={handleClose}
                        style={{
                            background: 'linear-gradient(135deg, #2563eb 0%, #0891b2 100%)',
                            borderColor: '#2563eb',
                            borderRadius: '8px',
                            height: '36px',
                            fontWeight: '500',
                            boxShadow: '0 4px 12px rgba(37, 99, 235, 0.3)'
                        }}
                    >
                        查看结果
                    </Button>
                )
            ]}
            maskClosable={false}
            styles={{
                content: {
                    backgroundColor: 'rgba(15, 23, 42, 0.95)',
                    backdropFilter: 'blur(20px)',
                    border: '1px solid rgba(255, 255, 255, 0.15)',
                    borderRadius: '12px',
                    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
                },
                header: {
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: '12px 12px 0 0'
                },
                body: {
                    padding: '20px',
                    backgroundColor: 'transparent'
                }
            }}
        >
            <div style={{ minHeight: '350px' }}>
                <ProgressManager
                    taskStatus={taskStatus}
                    logs={logs}
                    steps={progressSteps}
                    currentStep={currentStep}
                    startTime={startTime}
                    estimatedDuration={estimatedDuration}
                    showStatistics={false}  // 简化：不显示详细统计
                    compact={true}          // 使用紧凑模式
                    maxLogs={MAX_LOGS}
                />
            </div>
        </Modal>
    );
};

export default RealtimeCrawlModal;
