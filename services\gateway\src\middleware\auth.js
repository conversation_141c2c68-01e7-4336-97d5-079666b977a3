const jwt = require('jsonwebtoken');
// const { createClient } = require('redis');
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [new winston.transports.Console()]
});

/**
 * 从请求头中提取token
 */
function extractTokenFromHeader(req) {
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  return null;
}

// let redisClient;

// const initializeRedis = async () => {
//   redisClient = createClient({
//     url: process.env.REDIS_URL || 'redis://localhost:6379'
//   });

//   redisClient.on('error', (err) => console.error('Redis connection error:', err));
//   await redisClient.connect();
//   console.log('✅ Connected to Redis');
// };

// const isTokenBlacklisted = async (token) => {
//   if (!redisClient || !redisClient.isOpen) return false;
//   const result = await redisClient.get(`blacklist:${token}`);
//   return result !== null;
// };

// JWT验证中间件
const authenticateToken = async (req, res, next) => {
  try {
    const token = extractTokenFromHeader(req);
    if (!token) {
      return res.status(401).json({ success: false, message: 'Access token required' });
    }

    // if (await isTokenBlacklisted(token)) {
    //   return res.status(401).json({ success: false, message: 'Token has been invalidated' });
    // }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    logger.error('Token verification error:', error);
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token has expired'
      });
    }
    
    return res.status(403).json({
      success: false,
      message: 'Invalid token'
    });
  }
};

// 权限验证中间件
const authorizePermission = (requiredPermissions) => {
  return (req, res, next) => {
    const userPermissions = req.user?.permissions || [];
    
    // 检查用户是否有所需权限
    const hasPermission = requiredPermissions.some(permission => 
      userPermissions.includes(permission)
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions'
      });
    }
    
    next();
  };
};

// 角色验证中间件
const authorizeRole = (allowedRoles) => {
  return (req, res, next) => {
    const userRole = req.user?.role;
    
    if (!allowedRoles.includes(userRole)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient role privileges'
      });
    }
    
    next();
  };
};

// 可选认证中间件（不强制要求token）
const optionalAuth = async (req, res, next) => {
  try {
    const token = extractTokenFromHeader(req);

    if (token) {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      req.user = decoded;
    }
    
    next();
  } catch (error) {
    // 忽略认证错误，继续处理请求
    next();
  }
};

module.exports = {
  authenticateToken,
  authorizePermission,
  authorizeRole,
  optionalAuth,
  // initializeRedis
};