from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import networkx as nx
import logging
import re
from ..services.graph_service import GraphService
from ..models.schemas import (
    EquityPenetrationRequest, EquityPenetrationResponse,
    PledgeAnalysisRequest, PledgeAnalysisResponse,
    Company, Person, ShareholdingRelation
)

logger = logging.getLogger(__name__)


class EquityService:
    def __init__(self, graph_service: GraphService):
        self.graph_service = graph_service

    def _normalize_capital(self, capital_str: Optional[str]) -> float:
        """将注册资本字符串转换为可比较的数值"""
        if not capital_str or not isinstance(capital_str, str):
            return 0.0

        # 提取数字部分
        num_part = re.search(r'(\d+\.?\d*)', capital_str)
        if not num_part:
            return 0.0
        
        num = float(num_part.group(1))

        # 根据单位进行换算
        if '亿' in capital_str:
            num *= *********
        elif '万' in capital_str:
            num *= 10000
        
        # 可以在此添加更多货币转换逻辑，暂时忽略
        
        return num

    async def get_equity_penetration_graph(self, company_name: str, up_depth: int = 2, down_depth: int = 2) -> Dict:
        """
        获取股权穿透图谱, 使用新的扁平化数据结构.
        初始加载上下各两层.
        """
        try:
            # Step 1: Fetch flat data from the new graph service method
            graph_data = await self.graph_service.get_bidirectional_equity_graph(
                company_name, up_depth, down_depth
            )

            if not graph_data["nodes"]:
                return {"nodes": [], "edges": []}

            # Step 2: Process nodes to add calculated size
            for node in graph_data["nodes"]:
                capital_str = node.get("properties", {}).get("注册资本")
                node["node_size"] = self._normalize_capital(capital_str)

            return graph_data
            
        except Exception as e:
            logger.error(f"构建股权穿透图(扁平化)失败: {e}")
            raise

    async def get_expanded_graph_data(self, node_id: str, direction: str) -> Dict:
        """获取扩展节点的图数据"""
        try:
            graph_data = await self.graph_service.get_node_expansions(node_id, direction, 1)

            if not graph_data["nodes"]:
                return {"nodes": [], "edges": []}

            for node in graph_data["nodes"]:
                capital_str = node.get("properties", {}).get("注册资本")
                node["node_size"] = self._normalize_capital(capital_str)

            return graph_data

        except Exception as e:
            logger.error(f"扩展节点失败: {e}")
            raise


    async def analyze_equity_penetration(self, request: EquityPenetrationRequest) -> EquityPenetrationResponse:
        """股权穿透分析"""
        try:
            # 获取股权穿透路径
            paths = await self.graph_service.get_equity_penetration(
                request.company_id, 
                request.max_depth
            )
            
            # 构建结果
            penetration_paths = []
            ultimate_controllers = []
            
            for path_data in paths:
                entities = path_data["entities"]
                ratios = path_data["ratios"]
                total_ratio = path_data["total_ratio"]
                depth = path_data["depth"]
                
                # 构建路径
                path = []
                for i, entity in enumerate(entities):
                    if i == 0:
                        # 目标公司
                        path.append({
                            "entity": entity,
                            "shareholding_ratio": 1.0,
                            "level": 0
                        })
                    else:
                        # 股东
                        path.append({
                            "entity": entity,
                            "shareholding_ratio": ratios[i-1],
                            "level": i
                        })
                
                penetration_paths.append({
                    "path": path,
                    "total_ratio": total_ratio,
                    "depth": depth
                })
                
                # 记录最终控制人
                if depth > 0:
                    ultimate_controller = entities[-1]
                    ultimate_controller["control_ratio"] = total_ratio
                    ultimate_controllers.append(ultimate_controller)
            
            # 去重并排序最终控制人
            unique_controllers = []
            seen = set()
            for controller in ultimate_controllers:
                key = controller["id"]
                if key not in seen:
                    seen.add(key)
                    unique_controllers.append(controller)
            
            unique_controllers.sort(key=lambda x: x["control_ratio"], reverse=True)
            
            return EquityPenetrationResponse(
                company_id=request.company_id,
                penetration_paths=penetration_paths,
                ultimate_controllers=unique_controllers[:request.max_results] if request.max_results else unique_controllers,
                analysis_depth=request.max_depth,
                total_paths=len(penetration_paths),
                analysis_time=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"股权穿透分析失败: {e}")
            raise

    async def analyze_equity_pledge(self, request: PledgeAnalysisRequest) -> PledgeAnalysisResponse:
        """股权质押分析"""
        try:
            # 获取质押信息
            pledge_query = """
            MATCH (company:Company {unified_code: $company_id})
            OPTIONAL MATCH (shareholder)-[h:HOLDS_SHARES]->(company)
            OPTIONAL MATCH (shareholder)-[p:PLEDGED_TO]->(pledgee)
            RETURN company {.*} as company,
                   shareholder {
                       .*, 
                       id: CASE 
                           WHEN 'Company' IN labels(shareholder) THEN shareholder.unified_code 
                           ELSE shareholder.id_number 
                       END,
                       type: CASE 
                           WHEN 'Company' IN labels(shareholder) THEN 'company' 
                           ELSE 'person' 
                       END
                   } as shareholder,
                   h {.*} as shareholding,
                   p {.*} as pledge,
                   pledgee {
                       .*, 
                       id: CASE 
                           WHEN 'Company' IN labels(pledgee) THEN pledgee.unified_code 
                           ELSE pledgee.id_number 
                       END,
                       type: CASE 
                           WHEN 'Company' IN labels(pledgee) THEN 'company' 
                           ELSE 'person' 
                       END
                   } as pledgee
            """
            
            results = await self.graph_service.neo4j_client.execute_query(
                pledge_query, {"company_id": request.company_id}
            )
            
            if not results:
                raise ValueError(f"Company {request.company_id} not found")
            
            company_info = results[0]["company"]
            pledge_records = []
            pledge_statistics = {
                "total_pledged_shares": 0.0,
                "total_pledged_amount": 0.0,
                "pledge_ratio": 0.0,
                "pledgee_count": 0
            }
            
            pledgees = set()
            
            for result in results:
                if result["pledge"] and result["pledgee"]:
                    pledge_record = {
                        "shareholder": result["shareholder"],
                        "shareholding": result["shareholding"],
                        "pledge": result["pledge"],
                        "pledgee": result["pledgee"]
                    }
                    pledge_records.append(pledge_record)
                    
                    # 统计信息
                    pledged_ratio = result["pledge"].get("pledged_ratio", 0.0)
                    shareholding_ratio = result["shareholding"].get("shareholding_ratio", 0.0)
                    
                    pledge_statistics["total_pledged_shares"] += pledged_ratio * shareholding_ratio
                    pledge_statistics["total_pledged_amount"] += result["pledge"].get("pledged_amount", 0.0)
                    pledgees.add(result["pledgee"]["id"])
            
            pledge_statistics["pledgee_count"] = len(pledgees)
            pledge_statistics["pledge_ratio"] = pledge_statistics["total_pledged_shares"]
            
            # 计算风险等级
            risk_level = self._calculate_pledge_risk(pledge_statistics["pledge_ratio"])
            
            return PledgeAnalysisResponse(
                company=company_info,
                pledge_records=pledge_records,
                total_pledged_ratio=pledge_statistics["pledge_ratio"],
                active_pledges_count=pledge_statistics["pledgee_count"],
                risk_assessment={"level": risk_level},
                trend_analysis={}
            )
            
        except Exception as e:
            logger.error(f"股权质押分析失败: {e}")
            raise

    def _calculate_pledge_risk(self, pledge_ratio: float) -> str:
        """计算质押风险等级"""
        if pledge_ratio >= 0.8:
            return "高风险"
        elif pledge_ratio >= 0.5:
            return "中风险"
        elif pledge_ratio >= 0.2:
            return "低风险"
        else:
            return "无风险"

    async def get_company_equity_structure(self, company_id: str) -> Dict:
        """获取公司股权结构"""
        try:
            # 获取直接股东
            shareholders = await self.graph_service.get_company_shareholders(company_id)
            
            # 构建股权结构图
            equity_structure = {
                "company_id": company_id,
                "direct_shareholders": [],
                "ownership_concentration": 0.0,
                "largest_shareholder_ratio": 0.0,
                "top5_shareholders_ratio": 0.0
            }
            
            total_ratio = 0.0
            ratios = []
            
            for shareholder_data in shareholders:
                shareholder = shareholder_data["shareholder"]
                shareholding = shareholder_data["shareholding"]
                
                ratio = shareholding.get("shareholding_ratio", 0.0)
                ratios.append(ratio)
                total_ratio += ratio
                
                equity_structure["direct_shareholders"].append({
                    "shareholder": shareholder,
                    "shareholding": shareholding
                })
            
            # 计算持股集中度
            if ratios:
                ratios.sort(reverse=True)
                equity_structure["largest_shareholder_ratio"] = ratios[0]
                equity_structure["top5_shareholders_ratio"] = sum(ratios[:5])
                
                # 计算HHI指数（赫芬达尔指数）
                hhi = sum(ratio ** 2 for ratio in ratios)
                equity_structure["ownership_concentration"] = hhi
            
            return equity_structure
            
        except Exception as e:
            logger.error(f"获取股权结构失败: {e}")
            raise

    async def find_related_parties(self, company_id: str, max_depth: int = 3) -> List[Dict]:
        """查找关联方"""
        try:
            # 通过股权关系查找关联方
            query = """
            MATCH path = (c:Company {unified_code: $company_id})-[:HOLDS_SHARES|INVESTS_IN*1..$max_depth]-(related:Company)
            WHERE related.unified_code <> $company_id
            WITH related, min(length(path)) as distance
            RETURN related {.*, distance: distance} as company
            ORDER BY distance ASC, related.name ASC
            """
            
            results = await self.graph_service.neo4j_client.execute_query(
                query, {"company_id": company_id, "max_depth": max_depth}
            )
            
            related_parties = []
            for result in results:
                company = result["company"]
                
                # 分析关联关系类型
                relationship_types = await self._analyze_relationship_type(company_id, company["unified_code"])
                
                related_parties.append({
                    "company": company,
                    "relationship_types": relationship_types,
                    "distance": company["distance"]
                })
            
            return related_parties
            
        except Exception as e:
            logger.error(f"查找关联方失败: {e}")
            raise

    async def _analyze_relationship_type(self, company1_id: str, company2_id: str) -> List[str]:
        """分析关联关系类型"""
        query = """
        MATCH (c1:Company {unified_code: $company1_id}), (c2:Company {unified_code: $company2_id})
        OPTIONAL MATCH (c1)-[r1:HOLDS_SHARES]->(c2)
        OPTIONAL MATCH (c2)-[r2:HOLDS_SHARES]->(c1)
        OPTIONAL MATCH (c1)-[r3:INVESTS_IN]->(c2)
        OPTIONAL MATCH (c2)-[r4:INVESTS_IN]->(c1)
        OPTIONAL MATCH (c1)<-[:HOLDS_SHARES]-(common)-[:HOLDS_SHARES]->(c2)
        RETURN r1 IS NOT NULL as c1_holds_c2,
               r2 IS NOT NULL as c2_holds_c1,
               r3 IS NOT NULL as c1_invests_c2,
               r4 IS NOT NULL as c2_invests_c1,
               count(DISTINCT common) as common_shareholders
        """
        
        result = await self.graph_service.neo4j_client.execute_query(
            query, {"company1_id": company1_id, "company2_id": company2_id}
        )
        
        if not result:
            return []
        
        data = result[0]
        relationship_types = []
        
        if data["c1_holds_c2"]:
            relationship_types.append("直接持股")
        if data["c2_holds_c1"]:
            relationship_types.append("被持股")
        if data["c1_invests_c2"]:
            relationship_types.append("直接投资")
        if data["c2_invests_c1"]:
            relationship_types.append("被投资")
        if data["common_shareholders"] > 0:
            relationship_types.append("共同股东")
        
        return relationship_types

    async def calculate_control_power(self, company_id: str) -> Dict:
        """计算控制力分析"""
        try:
            # 获取控制链
            control_chains = await self.graph_service.get_control_chain(company_id)
            
            # 计算控制力指标
            control_analysis = {
                "company_id": company_id,
                "is_controlled": len(control_chains) > 0,
                "control_chains": control_chains,
                "control_depth": 0,
                "ultimate_controller": None,
                "control_stability": 0.0
            }
            
            if control_chains:
                # 找到最短控制链
                shortest_chain = min(control_chains, key=lambda x: x["depth"])
                control_analysis["control_depth"] = shortest_chain["depth"]
                control_analysis["ultimate_controller"] = shortest_chain["entities"][-1]
                
                # 计算控制稳定性（基于控制链数量和深度）
                avg_depth = sum(chain["depth"] for chain in control_chains) / len(control_chains)
                stability = 1.0 / (1.0 + avg_depth) * min(1.0, len(control_chains) / 3.0)
                control_analysis["control_stability"] = round(stability, 2)
            
            return control_analysis
            
        except Exception as e:
            logger.error(f"控制力分析失败: {e}")
            raise