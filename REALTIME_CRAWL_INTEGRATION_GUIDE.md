# 实时股权穿透功能 - 集成部署指南

## 🎯 功能概述

本指南介绍如何将实时股权穿透分析功能集成到IDEALAB项目中。该功能实现了：

- ⚡ **实时数据抓取**：当数据库中无股权数据时，自动启动实时分析
- 📊 **进度可视化**：WebSocket实时推送分析进度和日志
- 🔄 **智能缓存**：避免重复抓取，提升用户体验
- 🛡️ **日志过滤**：用户友好的进度展示，隐藏技术细节
- 🚀 **无Redis依赖**：基于内存的轻量级任务管理

## 📁 新增文件结构

```
services/financial-service/src/
├── crawlers/                          # 新增爬虫模块
│   ├── __init__.py
│   ├── tianyancha_scraper.py         # 天眼查爬虫（异步版）
│   ├── tree_crawler.py               # BFS股权树爬取器
│   ├── integrated_crawler.py         # 集成爬虫接口
│   ├── crawler_manager.py            # 内存任务管理器
│   └── progress_logger.py            # 进度日志管理
├── services/
│   ├── websocket_service.py          # WebSocket服务
│   └── realtime_crawl_service.py     # 实时爬取服务
├── routers/
│   └── realtime_equity.py            # 新增API路由
└── models/
    └── crawl_schemas.py              # 爬虫数据模型

frontend/
└── components/
    └── RealtimeCrawlModal.tsx        # 实时分析Modal组件
```

## 🚀 部署步骤

### 第一步：安装依赖

#### Python后端依赖
```bash
cd services/financial-service
pip install selenium webdriver-manager beautifulsoup4 requests pandas
```

#### Node.js前端依赖
```bash
cd frontend
npm install socket.io-client@^4.7.4
```

### 第二步：配置环境

#### 1. Selenium ChromeDriver配置
创建或更新 `services/financial-service/.env`：
```env
# Selenium配置
SELENIUM_HEADLESS=true
CHROME_DRIVER_AUTO_INSTALL=true
CRAWL_DELAY_MIN=2
CRAWL_DELAY_MAX=5

# 并发控制
MAX_CONCURRENT_CRAWL_TASKS=3
CRAWL_TASK_TIMEOUT=1800  # 30分钟

# WebSocket配置
WEBSOCKET_PING_INTERVAL=30
```

#### 2. Chrome浏览器设置（生产环境）
生产环境需要安装Chrome浏览器：
```bash
# Ubuntu/Debian
wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add -
echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google.list
apt-get update && apt-get install -y google-chrome-stable

# 或使用Docker镜像
FROM selenium/standalone-chrome:latest
```

### 第三步：更新financial-service主应用

确保在 `services/financial-service/main.py` 中正确注册了新路由：
```python
from src.routers import equity, bond, analytics, realtime_equity

# 注册路由
app.include_router(realtime_equity.router, tags=["Realtime Equity Analysis"])
```

### 第四步：前端集成

#### 1. 更新股权穿透工具
`frontend/pages/tools/EquityPenetrationTool.tsx` 已自动集成实时分析功能。

#### 2. 确保API路由正确
更新 `frontend/services/api.ts`，添加实时分析API：
```typescript
// 实时股权分析API
export const realtimeEquityApi = {
    startCrawl: (data: any) => api.post('/api/equity/start-realtime-crawl', data),
    getTaskStatus: (taskId: string) => api.get(`/api/equity/task-status/${taskId}`),
    getCrawlResult: (taskId: string) => api.get(`/api/equity/crawl-result/${taskId}`),
    cancelTask: (taskId: string) => api.post(`/api/equity/cancel-task/${taskId}`)
};
```

### 第五步：Docker配置更新

#### 1. 更新 financial-service Dockerfile
```dockerfile
FROM python:3.9-slim

# 安装Chrome和依赖
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    curl \
    && wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

CMD ["python", "main.py"]
```

#### 2. 更新 docker-compose.yml
```yaml
financial-service:
  build: ./services/financial-service
  environment:
    - SELENIUM_HEADLESS=true
    - DISPLAY=:99  # 虚拟显示器
  volumes:
    - /dev/shm:/dev/shm  # 共享内存，提高Chrome性能
  deploy:
    resources:
      limits:
        memory: 2G  # 增加内存限制
```

## 🔧 配置选项

### 任务管理配置
在 `crawler_manager.py` 中可调整：
```python
class CrawlTaskManager:
    def __init__(self):
        self.max_concurrent_tasks = 3      # 最大并发任务数
        self.task_timeout = 30 * 60       # 任务超时时间（秒）
        self.cleanup_interval = 3600      # 清理间隔（秒）
        self.task_retention_hours = 24    # 任务保留时间（小时）
```

### 爬虫行为配置
在 `tianyancha_scraper.py` 中可调整：
```python
class TianyanchaScraperAsync:
    def __init__(self):
        self.request_delay = (2, 5)       # 请求延迟范围
        self.max_retries = 3              # 最大重试次数
        self.timeout = 30                 # 请求超时时间
```

### 日志过滤配置
在 `progress_logger.py` 中可调整：
```python
class ProgressLogger:
    def __init__(self):
        self.max_logs = 100               # 最大日志条数
        self.log_levels = ['info', 'success', 'warning', 'error']
        self.sensitive_patterns = [       # 需要过滤的敏感信息
            r'File "[^"]*"',
            r'https?://[^\s]+',
            r'line \d+'
        ]
```

## 🛠️ 故障排除

### 常见问题

#### 1. Chrome浏览器启动失败
```bash
# 检查Chrome是否正确安装
google-chrome --version

# 检查权限
chmod +x /usr/bin/google-chrome
```

#### 2. WebSocket连接失败
检查防火墙和代理设置：
```bash
# 确保端口开放
netstat -tlnp | grep 8003
```

#### 3. 任务超时或卡住
增加资源限制和超时设置：
```python
# 在crawler_manager.py中
self.task_timeout = 45 * 60  # 增加到45分钟
```

#### 4. 内存不足
监控内存使用：
```bash
# 监控内存使用
docker stats financial-service

# 增加swap空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

## 📊 性能优化

### 1. 并发控制
```python
# 根据服务器配置调整并发数
MAX_CONCURRENT_TASKS = min(cpu_count(), 5)
```

### 2. 缓存策略
```python
# 智能缓存检查
async def should_use_cache(company_name: str) -> bool:
    # 检查数据新鲜度
    last_update = await get_last_update_time(company_name)
    return (datetime.now() - last_update).days < 7
```

### 3. 资源清理
```python
# 定期清理过期任务和日志
async def cleanup_expired_data():
    # 清理24小时前的任务
    # 清理大于100条的日志
    # 释放未使用的Chrome进程
```

## 🚦 监控和日志

### 1. 应用日志
```python
import logging

# 配置日志级别
logging.getLogger("crawler_manager").setLevel(logging.INFO)
logging.getLogger("tianyancha_scraper").setLevel(logging.WARNING)
logging.getLogger("websocket_manager").setLevel(logging.INFO)
```

### 2. 性能监控
```python
# 添加性能指标
from prometheus_client import Counter, Histogram, Gauge

crawl_tasks_total = Counter('crawl_tasks_total', 'Total crawl tasks')
crawl_duration = Histogram('crawl_duration_seconds', 'Crawl duration')
active_connections = Gauge('websocket_connections', 'Active WebSocket connections')
```

## 🔐 安全考虑

### 1. 反爬虫策略
- 随机化请求延迟
- 轮换User-Agent
- 使用代理池（可选）
- 智能频率控制

### 2. 数据安全
- 敏感信息日志过滤
- 数据传输加密
- 访问权限控制
- 数据保留策略

## 📈 扩展建议

### 1. 分布式爬虫
使用Celery + Redis实现真正的分布式任务队列：
```python
# 升级到Celery
from celery import Celery

app = Celery('crawler', broker='redis://localhost:6379')

@app.task
def crawl_company_task(company_name):
    # 分布式任务执行
    pass
```

### 2. 智能调度
```python
# 添加智能调度算法
class SmartScheduler:
    def prioritize_tasks(self, tasks):
        # 根据企业重要性、用户优先级等排序
        return sorted(tasks, key=self.priority_score)
```

### 3. 数据质量评估
```python
# 添加数据质量检查
class DataQualityChecker:
    def validate_equity_data(self, data):
        # 检查数据完整性和一致性
        return self.quality_score(data)
```

## 🎉 功能特性

✅ **已实现功能**
- 实时股权数据抓取
- WebSocket进度推送
- 智能任务管理
- 用户友好的日志过滤
- 错误处理和重试机制
- 响应式前端界面

🚧 **待扩展功能**
- 分布式任务调度
- 高级数据质量检查
- 用户行为分析
- 缓存优化策略
- 监控和告警系统

---

## 🏃‍♂️ 快速启动

1. **安装依赖**: `pip install selenium webdriver-manager beautifulsoup4`
2. **启动服务**: `docker-compose up financial-service`
3. **测试功能**: 在股权穿透页面搜索一个新公司
4. **查看日志**: 观察实时分析过程和进度推送

成功集成后，用户在股权穿透页面搜索未收录的公司时，将看到实时分析确认对话框，点击确认后进入详细的进度展示界面，完成后自动显示分析结果。

🎯 **整个实现无需Redis依赖，基于内存管理，轻量高效！**