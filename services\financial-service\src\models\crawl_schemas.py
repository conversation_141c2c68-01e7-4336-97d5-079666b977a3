#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫相关的数据模型
"""
from pydantic import BaseModel, Field
from typing import Optional, Dict, List, Any
from datetime import datetime
from enum import Enum

class CrawlDirection(str, Enum):
    """爬取方向"""
    UP = "up"          # 向上（股东）
    DOWN = "down"      # 向下（投资）
    BOTH = "both"      # 双向

class TaskStatus(str, Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class RealtimeCrawlRequest(BaseModel):
    """实时爬取请求"""
    company_name: str = Field(..., description="公司名称", min_length=1, max_length=200)
    depth: int = Field(default=2, description="穿透深度", ge=1, le=5)
    direction: CrawlDirection = Field(default=CrawlDirection.BOTH, description="爬取方向")
    force_refresh: bool = Field(default=False, description="是否强制刷新缓存")

class TaskStatusResponse(BaseModel):
    """任务状态响应"""
    id: str = Field(..., description="任务ID")
    company_name: str = Field(..., description="公司名称")
    status: TaskStatus = Field(..., description="任务状态")
    progress: int = Field(..., description="进度百分比", ge=0, le=100)
    total_steps: int = Field(default=0, description="总步数")
    processed_companies: int = Field(default=0, description="已处理公司数")
    created_at: str = Field(..., description="创建时间")
    started_at: Optional[str] = Field(None, description="开始时间")
    completed_at: Optional[str] = Field(None, description="完成时间")
    error_message: Optional[str] = Field(None, description="错误信息")
    duration: Optional[float] = Field(None, description="持续时间（秒）")

class StartCrawlResponse(BaseModel):
    """开始爬取响应"""
    task_id: str = Field(..., description="任务ID")
    message: str = Field(..., description="响应消息")
    websocket_url: str = Field(..., description="WebSocket连接URL")
    estimated_duration: int = Field(..., description="预估持续时间（秒）")

class CrawlResultNode(BaseModel):
    """爬取结果节点"""
    id: str = Field(..., description="节点ID")
    name: str = Field(..., description="节点名称")
    label: str = Field(..., description="显示标签")
    type: str = Field(..., description="节点类型")
    properties: Dict[str, Any] = Field(default_factory=dict, description="节点属性")

class CrawlResultEdge(BaseModel):
    """爬取结果边"""
    source: str = Field(..., description="源节点ID")
    target: str = Field(..., description="目标节点ID")
    type: str = Field(..., description="关系类型")
    properties: Dict[str, Any] = Field(default_factory=dict, description="关系属性")

class CrawlResult(BaseModel):
    """爬取结果"""
    nodes: List[CrawlResultNode] = Field(..., description="节点列表")
    edges: List[CrawlResultEdge] = Field(..., description="边列表")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")

class CrawlCompleteResponse(BaseModel):
    """爬取完成响应"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="最终状态")
    result: Optional[CrawlResult] = Field(None, description="爬取结果")
    error_message: Optional[str] = Field(None, description="错误信息")
    duration: float = Field(..., description="总耗时（秒）")
    stats: Dict[str, Any] = Field(default_factory=dict, description="统计信息")

class WebSocketMessage(BaseModel):
    """WebSocket消息基础模型"""
    type: str = Field(..., description="消息类型")
    timestamp: str = Field(..., description="时间戳")

class ProgressUpdateMessage(WebSocketMessage):
    """进度更新消息"""
    type: str = Field(default="progress_update", description="消息类型")
    task_id: str = Field(..., description="任务ID")
    progress: int = Field(..., description="进度百分比")
    current_step: int = Field(..., description="当前步骤")
    total_steps: int = Field(..., description="总步骤")
    processed_companies: int = Field(default=0, description="已处理公司数")

class LogMessage(WebSocketMessage):
    """日志消息"""
    type: str = Field(default="log_message", description="消息类型")
    task_id: str = Field(..., description="任务ID")
    level: str = Field(..., description="日志级别")
    message: str = Field(..., description="日志内容")
    phase: str = Field(..., description="当前阶段")

class StatusChangeMessage(WebSocketMessage):
    """状态变化消息"""
    type: str = Field(default="status_change", description="消息类型")
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="新状态")
    additional_data: Optional[Dict[str, Any]] = Field(None, description="附加数据")

class ErrorMessage(WebSocketMessage):
    """错误消息"""
    type: str = Field(default="error", description="消息类型")
    task_id: str = Field(..., description="任务ID")
    error_message: str = Field(..., description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")

class TaskListResponse(BaseModel):
    """任务列表响应"""
    tasks: List[TaskStatusResponse] = Field(..., description="任务列表")
    total: int = Field(..., description="总数")

class CrawlStats(BaseModel):
    """爬取统计信息"""
    total_tasks: int = Field(..., description="总任务数")
    running_tasks: int = Field(..., description="运行中任务数")
    completed_tasks: int = Field(..., description="已完成任务数")
    failed_tasks: int = Field(..., description="失败任务数")
    average_duration: float = Field(..., description="平均耗时")

class SystemStatus(BaseModel):
    """系统状态"""
    crawl_stats: CrawlStats = Field(..., description="爬取统计")
    websocket_connections: int = Field(..., description="WebSocket连接数")
    server_time: str = Field(..., description="服务器时间")
    version: str = Field(..., description="版本信息")