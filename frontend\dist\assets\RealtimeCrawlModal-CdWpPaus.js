import{r as i,j as e}from"./react-vendor-DnpxqCDv.js";import{M as se,d as j,R as O,b as h,S as x,H as u,J as M,K as te,P as re,N as oe,r as ne,O as P,i as b,T as ie,o as ae,Q as A,B as q}from"./antd-DnRyuF5C.js";const{Text:J}=ie,{Step:w}=M,ue=({visible:v,companyName:p,onClose:W,onComplete:k,onCancel:H})=>{const[s,_]=i.useState(null),[$,E]=i.useState([]),[y,g]=i.useState(0),[U,I]=i.useState(!1),[d,C]=i.useState(null),[K,Q]=i.useState(0),[z,V]=i.useState(null),L=i.useRef(null),Y=()=>{var t;(t=L.current)==null||t.scrollIntoView({behavior:"smooth"})};i.useEffect(()=>{Y()},[$]);const B=i.useCallback(async()=>{if(p){I(!0),E([]),V(new Date);try{const t=await fetch("/api/equity/start-realtime-crawl",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({company_name:p,depth:2,direction:"both"})}),l=t.headers.get("content-type")||"";let o=null,f="";try{l.includes("application/json")?o=await t.json():f=await t.text()}catch{try{f=await t.text()}catch{}}if(!t.ok){const S=(o==null?void 0:o.detail)||f||`启动分析失败 (HTTP ${t.status})`;throw new Error(S)}Q(o.estimated_duration);const T=(o==null?void 0:o.task_id)||JSON.parse(f||"{}").task_id;if(!T)throw new Error("未能获取到任务ID");const D=`/api/equity/ws/crawl-progress/${T}`,ee=`${window.location.protocol==="https:"?"wss:":"ws:"}//${window.location.host}${D}`,m=new WebSocket(ee);C(m),m.addEventListener("open",()=>{n("info","已连接到实时进度服务")}),m.addEventListener("message",S=>{try{const r=JSON.parse(S.data);switch(r.type){case"progress_update":_(c=>c&&{...c,progress:typeof r.progress=="number"?r.progress:c.progress}),typeof r.progress=="number"&&G(r.progress);break;case"log_message":n(r.level||"info",r.message||"",r.phase);break;case"status_change":_(c=>c&&{...c,status:r.status||c.status}),r.status==="completed"?(n("success",`🎉 分析完成！耗时 ${r.duration||0} 秒`),F(r.task_id)):r.status==="failed"&&n("error",`❌ 分析失败: ${r.error||"未知错误"}`);break;case"error":n("error",`错误: ${r.error_message||"未知错误"}`);break;default:break}}catch(r){console.error("WS消息解析失败:",r)}}),m.addEventListener("close",()=>{n("warning","与服务器的连接已断开")}),m.addEventListener("error",()=>{n("error","WebSocket发生错误")}),_({id:o.task_id,company_name:p,status:"running",progress:0,total_steps:0,processed_companies:0,created_at:new Date().toISOString()}),n("success",`🚀 开始分析 "${p}" 的股权结构`),n("info",`预计需要 ${o.estimated_duration} 秒`)}catch(t){console.error("启动分析失败:",t),n("error",`启动失败: ${t instanceof Error?t.message:"未知错误"}`)}finally{I(!1)}}},[p]),F=async t=>{try{const l=await fetch(`/api/equity/crawl-result/${t}`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(l.ok){const o=await l.json();k(o.result)}}catch(l){console.error("获取结果失败:",l),n("error","获取分析结果失败")}},n=(t,l,o)=>{const f={timestamp:new Date().toISOString(),level:t,message:l,phase:o||"分析中"};E(T=>[...T,f].slice(-100))},G=t=>{t<25?g(0):t<50?g(1):t<75?g(2):t<100?g(3):g(4)},X=async()=>{if(s!=null&&s.id)try{await fetch(`/api/equity/cancel-task/${s.id}`,{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}}),n("warning","任务已取消")}catch(t){console.error("取消任务失败:",t)}if(d){try{d.close()}catch{}C(null)}H()},R=()=>{if(d){try{d.close()}catch{}C(null)}W()},Z=()=>z?Math.floor((Date.now()-z.getTime())/1e3):0,N=t=>({info:"default",success:"success",warning:"warning",error:"error",progress:"processing"})[t]||"default",a=t=>s?s.status==="failed"&&y>=t?"error":y>t?"finish":y===t&&s.status==="running"?"process":"wait":"wait";return i.useEffect(()=>{v&&!s&&B()},[v,s,B]),i.useEffect(()=>()=>{if(d)try{d.close()}catch{}},[d]),e.jsx(se,{title:e.jsxs(b,{children:[e.jsx(u,{spin:(s==null?void 0:s.status)==="running"}),e.jsx("span",{children:"实时股权穿透分析"})]}),open:v,onCancel:R,width:800,footer:[e.jsx(q,{onClick:X,disabled:U,children:(s==null?void 0:s.status)==="running"?"取消任务":"关闭"},"cancel"),(s==null?void 0:s.status)==="completed"&&e.jsx(q,{type:"primary",onClick:R,children:"查看结果"},"close")],maskClosable:!1,children:e.jsxs("div",{style:{minHeight:"500px"},children:[e.jsx(j,{size:"small",style:{marginBottom:16},children:e.jsxs(O,{gutter:16,children:[e.jsx(h,{span:8,children:e.jsx(x,{title:"目标公司",value:p})}),e.jsx(h,{span:8,children:e.jsx(x,{title:"已用时间",value:Z(),suffix:"秒",prefix:e.jsx(u,{spin:(s==null?void 0:s.status)==="running"})})}),e.jsx(h,{span:8,children:e.jsx(x,{title:"预计时间",value:K,suffix:"秒"})})]})}),e.jsxs(M,{current:y,size:"small",style:{marginBottom:24},children:[e.jsx(w,{title:"搜索公司",icon:a(0)==="process"?e.jsx(u,{spin:!0}):void 0,status:a(0)}),e.jsx(w,{title:"构建队列",icon:a(1)==="process"?e.jsx(u,{spin:!0}):void 0,status:a(1)}),e.jsx(w,{title:"关系分析",icon:a(2)==="process"?e.jsx(u,{spin:!0}):void 0,status:a(2)}),e.jsx(w,{title:"数据整理",icon:a(3)==="process"?e.jsx(u,{spin:!0}):void 0,status:a(3)}),e.jsx(w,{title:"完成",icon:(s==null?void 0:s.status)==="completed"?e.jsx(te,{}):void 0,status:a(4)})]}),e.jsx(re,{percent:(s==null?void 0:s.progress)||0,status:(s==null?void 0:s.status)==="failed"?"exception":(s==null?void 0:s.status)==="completed"?"success":"active",showInfo:!0,style:{marginBottom:16}}),s&&e.jsxs(O,{gutter:16,style:{marginBottom:16},children:[e.jsx(h,{span:8,children:e.jsx(j,{size:"small",children:e.jsx(x,{title:"已处理企业",value:s.processed_companies,prefix:e.jsx(oe,{})})})}),e.jsx(h,{span:8,children:e.jsx(j,{size:"small",children:e.jsx(x,{title:"总步骤",value:s.total_steps||0})})}),e.jsx(h,{span:8,children:e.jsx(j,{size:"small",children:e.jsx(x,{title:"当前状态",value:s.status==="running"?"运行中":s.status==="completed"?"已完成":s.status==="failed"?"失败":s.status==="pending"?"等待中":"未知",valueStyle:{color:s.status==="completed"?"#3f8600":s.status==="failed"?"#cf1322":"#1890ff"}})})})]}),e.jsx(j,{title:e.jsxs(b,{children:[e.jsx(u,{spin:(s==null?void 0:s.status)==="running"}),e.jsx("span",{children:"实时日志"})]}),size:"small",children:e.jsxs("div",{style:{height:"200px",overflowY:"auto",backgroundColor:"#fafafa",padding:"8px"},children:[$.length===0?e.jsxs("div",{style:{textAlign:"center",color:"#999",padding:"20px"},children:[e.jsx(ne,{}),e.jsx("div",{style:{marginTop:8},children:"等待日志信息..."})]}):e.jsx(P,{size:"small",dataSource:$,renderItem:t=>e.jsx(P.Item,{style:{padding:"4px 0",borderBottom:"none"},children:e.jsxs(b,{size:"small",style:{width:"100%"},children:[e.jsx(J,{type:"secondary",style:{fontSize:"10px",minWidth:"60px"},children:new Date(t.timestamp).toLocaleTimeString()}),e.jsx(ae,{color:N(t.level),children:t.level.toUpperCase()}),e.jsx(J,{style:{fontSize:"12px",flex:1},children:t.message})]})})}),e.jsx("div",{ref:L})]})}),(s==null?void 0:s.error_message)&&e.jsx(A,{message:"分析失败",description:s.error_message,type:"error",showIcon:!0,style:{marginTop:16}}),(s==null?void 0:s.status)==="completed"&&e.jsx(A,{message:"分析完成！",description:`股权穿透分析已完成，共耗时 ${s.duration||0} 秒。点击"查看结果"按钮查看详细的股权关系图谱。`,type:"success",showIcon:!0,style:{marginTop:16}})]})})};export{ue as RealtimeCrawlModal,ue as default};
//# sourceMappingURL=RealtimeCrawlModal-CdWpPaus.js.map
