# NLP Service Dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Poetry
RUN pip install poetry

# 复制依赖文件
COPY pyproject.toml poetry.lock ./

# 配置Poetry并安装依赖
RUN poetry config virtualenvs.create false \
    && poetry install --no-dev

# 复制源码
COPY . .

# 创建模型目录
RUN mkdir -p models

# 暴露端口
EXPOSE 8002

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:8002/health || exit 1

# 启动服务
CMD ["python", "main.py"]