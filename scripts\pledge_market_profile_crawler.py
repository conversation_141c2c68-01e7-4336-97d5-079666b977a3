#!/usr/bin/env python3
"""
股权质押市场概况数据爬取脚本
功能：
1. 从AKShare获取股权质押市场概况历史数据
2. 智能检测数据变动，只在有变化时更新数据库
3. 记录更新时间和数据版本

接口：stock_gpzy_profile_em

作者：IDEALAB团队
创建时间：2024-01-15
"""

import os
import sys
import logging
import pandas as pd
import psycopg2
from psycopg2.extras import execute_values
import akshare as ak
import time
import hashlib
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)


class PledgeMarketProfileCrawler:
    """股权质押市场概况数据爬取器"""
    
    def __init__(self):
        """初始化"""
        self.connection = None
        self.cursor = None
        self.db_config = self._get_db_config()
    
    def _get_db_config(self):
        """获取数据库配置"""
        config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 5432)),
            'database': os.getenv('DB_NAME', 'idealab'),
            'user': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', '123456')
        }
        
        if not all([config['host'], config['database'], config['user'], config['password']]):
            raise ValueError("数据库配置不完整，请检查环境变量")
        
        return config
    
    def connect_db(self):
        """连接数据库"""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_db(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("数据库连接已关闭")
    
    def create_tables(self):
        """创建数据表"""
        try:
            # 创建市场概况表
            market_table_sql = """
            CREATE TABLE IF NOT EXISTS pledge_market_profile (
                id SERIAL PRIMARY KEY,
                trading_date DATE NOT NULL,
                total_pledge_ratio DECIMAL(10, 4),
                pledge_company_count INTEGER,
                pledge_transaction_count INTEGER,
                total_pledge_shares BIGINT,
                total_pledge_market_value BIGINT,
                hs300_index DECIMAL(10, 4),
                index_change_ratio DECIMAL(10, 4),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """

            # 创建数据版本控制表
            version_table_sql = """
            CREATE TABLE IF NOT EXISTS pledge_market_version (
                id SERIAL PRIMARY KEY,
                table_name VARCHAR(50) NOT NULL,
                data_hash VARCHAR(64) NOT NULL,
                record_count INTEGER,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(table_name)
            );
            """

            self.cursor.execute(market_table_sql)
            self.cursor.execute(version_table_sql)

            # 创建索引
            index_sql = """
            CREATE INDEX IF NOT EXISTS idx_market_trading_date ON pledge_market_profile(trading_date);
            CREATE INDEX IF NOT EXISTS idx_market_updated ON pledge_market_profile(updated_at);
            """

            self.cursor.execute(index_sql)
            self.connection.commit()
            logger.info("数据表创建/检查完成")

        except Exception as e:
            logger.error(f"创建数据表失败: {e}")
            self.connection.rollback()
            raise

    def fetch_data(self):
        """获取市场概况数据"""
        try:
            logger.info("开始获取股权质押市场概况数据...")
            start_time = time.time()

            df = ak.stock_gpzy_profile_em()

            end_time = time.time()
            logger.info(f"数据获取完成，耗时: {end_time - start_time:.2f}秒，获取 {len(df)} 条记录")

            return df
        except Exception as e:
            logger.error(f"获取数据失败: {e}")
            return None

    def clean_data(self, df):
        """清洗数据"""
        logger.info("开始数据清洗...")

        # 重命名列
        column_mapping = {
            '交易日期': 'trading_date',
            'A股质押总比例': 'total_pledge_ratio',
            '质押公司数量': 'pledge_company_count',
            '质押笔数': 'pledge_transaction_count',
            '质押总股数': 'total_pledge_shares',
            '质押总市值': 'total_pledge_market_value',
            '沪深300指数': 'hs300_index',
            '涨跌幅': 'index_change_ratio'
        }

        df = df.rename(columns=column_mapping)

        try:
            # 日期字段处理
            df['trading_date'] = pd.to_datetime(df['trading_date'], errors='coerce')

            # 数值字段处理
            numeric_cols = [
                'total_pledge_ratio', 'pledge_company_count', 'pledge_transaction_count',
                'total_pledge_shares', 'total_pledge_market_value', 'hs300_index', 'index_change_ratio'
            ]

            for col in numeric_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            # 移除日期为空的记录
            initial_count = len(df)
            df = df.dropna(subset=['trading_date'])
            final_count = len(df)

            if initial_count != final_count:
                logger.warning(f"移除了 {initial_count - final_count} 条无效记录")

            logger.info(f"数据清洗完成，有效记录: {final_count} 条")

        except Exception as e:
            logger.error(f"数据清洗失败: {e}")
            raise

        return df

    def calculate_data_hash(self, df):
        """计算数据哈希值用于变动检测"""
        try:
            data_str = df.to_string(index=False)
            return hashlib.md5(data_str.encode('utf-8')).hexdigest()
        except Exception as e:
            logger.error(f"计算数据哈希失败: {e}")
            return None

    def get_last_data_hash(self):
        """获取上次数据的哈希值"""
        try:
            self.cursor.execute(
                "SELECT data_hash, record_count, last_updated FROM pledge_market_version WHERE table_name = %s",
                ('pledge_market_profile',)
            )
            result = self.cursor.fetchone()
            return result if result else (None, 0, None)
        except Exception as e:
            logger.error(f"获取数据版本信息失败: {e}")
            return None, 0, None

    def update_data_version(self, data_hash, record_count):
        """更新数据版本信息"""
        try:
            self.cursor.execute("""
                INSERT INTO pledge_market_version (table_name, data_hash, record_count, last_updated)
                VALUES (%s, %s, %s, %s)
                ON CONFLICT (table_name)
                DO UPDATE SET
                    data_hash = EXCLUDED.data_hash,
                    record_count = EXCLUDED.record_count,
                    last_updated = EXCLUDED.last_updated
            """, ('pledge_market_profile', data_hash, record_count, datetime.now()))

        except Exception as e:
            logger.error(f"更新数据版本信息失败: {e}")
            raise

    def insert_data(self, df):
        """插入数据到数据库"""
        try:
            logger.info("开始插入数据...")

            # 清空现有数据
            self.cursor.execute("TRUNCATE TABLE pledge_market_profile RESTART IDENTITY")

            insert_sql = """
            INSERT INTO pledge_market_profile (
                trading_date, total_pledge_ratio, pledge_company_count,
                pledge_transaction_count, total_pledge_shares, total_pledge_market_value,
                hs300_index, index_change_ratio
            ) VALUES %s
            """

            # 准备数据
            data_tuples = []
            for _, row in df.iterrows():
                data_tuple = (
                    row['trading_date'].date() if pd.notna(row['trading_date']) else None,
                    float(row['total_pledge_ratio']) if pd.notna(row['total_pledge_ratio']) else None,
                    int(row['pledge_company_count']) if pd.notna(row['pledge_company_count']) else None,
                    int(row['pledge_transaction_count']) if pd.notna(row['pledge_transaction_count']) else None,
                    int(row['total_pledge_shares']) if pd.notna(row['total_pledge_shares']) else None,
                    int(row['total_pledge_market_value']) if pd.notna(row['total_pledge_market_value']) else None,
                    float(row['hs300_index']) if pd.notna(row['hs300_index']) else None,
                    float(row['index_change_ratio']) if pd.notna(row['index_change_ratio']) else None
                )
                data_tuples.append(data_tuple)

            # 批量插入
            execute_values(self.cursor, insert_sql, data_tuples, page_size=1000)

            logger.info(f"成功插入 {len(data_tuples)} 条记录")
            return True

        except Exception as e:
            logger.error(f"数据插入失败: {e}")
            return False
    
    def fetch_data(self):
        """获取市场概况数据"""
        try:
            logger.info("开始获取股权质押市场概况数据...")
            start_time = time.time()
            
            df = ak.stock_gpzy_profile_em()
            
            end_time = time.time()
            logger.info(f"数据获取完成，耗时: {end_time - start_time:.2f}秒，获取 {len(df)} 条记录")
            
            return df
        except Exception as e:
            logger.error(f"获取数据失败: {e}")
            return None
    
    def clean_data(self, df):
        """清洗数据"""
        logger.info("开始数据清洗...")
        
        # 重命名列
        column_mapping = {
            '交易日期': 'trading_date',
            'A股质押总比例': 'total_pledge_ratio',
            '质押公司数量': 'pledge_company_count',
            '质押笔数': 'pledge_transaction_count',
            '质押总股数': 'total_pledge_shares',
            '质押总市值': 'total_pledge_market_value',
            '沪深300指数': 'hs300_index',
            '涨跌幅': 'index_change_ratio'
        }
        
        df = df.rename(columns=column_mapping)
        
        try:
            # 日期字段处理
            df['trading_date'] = pd.to_datetime(df['trading_date'], errors='coerce')
            
            # 数值字段处理
            numeric_cols = [
                'total_pledge_ratio', 'pledge_company_count', 'pledge_transaction_count',
                'total_pledge_shares', 'total_pledge_market_value', 'hs300_index', 'index_change_ratio'
            ]
            
            for col in numeric_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 移除日期为空的记录
            initial_count = len(df)
            df = df.dropna(subset=['trading_date'])
            final_count = len(df)
            
            if initial_count != final_count:
                logger.warning(f"移除了 {initial_count - final_count} 条无效记录")
            
            logger.info(f"数据清洗完成，有效记录: {final_count} 条")
            
        except Exception as e:
            logger.error(f"数据清洗失败: {e}")
            raise
        
        return df
    
    def calculate_data_hash(self, df):
        """计算数据哈希值用于变动检测"""
        try:
            data_str = df.to_string(index=False)
            return hashlib.md5(data_str.encode('utf-8')).hexdigest()
        except Exception as e:
            logger.error(f"计算数据哈希失败: {e}")
            return None
    
    def get_last_data_hash(self):
        """获取上次数据的哈希值"""
        try:
            self.cursor.execute(
                "SELECT data_hash, record_count, last_updated FROM pledge_market_version WHERE table_name = %s",
                ('pledge_market_profile',)
            )
            result = self.cursor.fetchone()
            return result if result else (None, 0, None)
        except Exception as e:
            logger.error(f"获取数据版本信息失败: {e}")
            return None, 0, None
    
    def update_data_version(self, data_hash, record_count):
        """更新数据版本信息"""
        try:
            self.cursor.execute("""
                INSERT INTO pledge_market_version (table_name, data_hash, record_count, last_updated)
                VALUES (%s, %s, %s, %s)
                ON CONFLICT (table_name)
                DO UPDATE SET
                    data_hash = EXCLUDED.data_hash,
                    record_count = EXCLUDED.record_count,
                    last_updated = EXCLUDED.last_updated
            """, ('pledge_market_profile', data_hash, record_count, datetime.now()))
            
        except Exception as e:
            logger.error(f"更新数据版本信息失败: {e}")
            raise
    
    def insert_data(self, df):
        """插入数据到数据库"""
        try:
            logger.info("开始插入数据...")
            
            # 清空现有数据
            self.cursor.execute("TRUNCATE TABLE pledge_market_profile RESTART IDENTITY")
            
            insert_sql = """
            INSERT INTO pledge_market_profile (
                trading_date, total_pledge_ratio, pledge_company_count,
                pledge_transaction_count, total_pledge_shares, total_pledge_market_value,
                hs300_index, index_change_ratio
            ) VALUES %s
            """
            
            # 准备数据
            data_tuples = []
            for _, row in df.iterrows():
                data_tuple = (
                    row['trading_date'].date() if pd.notna(row['trading_date']) else None,
                    float(row['total_pledge_ratio']) if pd.notna(row['total_pledge_ratio']) else None,
                    int(row['pledge_company_count']) if pd.notna(row['pledge_company_count']) else None,
                    int(row['pledge_transaction_count']) if pd.notna(row['pledge_transaction_count']) else None,
                    int(row['total_pledge_shares']) if pd.notna(row['total_pledge_shares']) else None,
                    int(row['total_pledge_market_value']) if pd.notna(row['total_pledge_market_value']) else None,
                    float(row['hs300_index']) if pd.notna(row['hs300_index']) else None,
                    float(row['index_change_ratio']) if pd.notna(row['index_change_ratio']) else None
                )
                data_tuples.append(data_tuple)
            
            # 批量插入
            execute_values(self.cursor, insert_sql, data_tuples, page_size=1000)
            
            logger.info(f"成功插入 {len(data_tuples)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"数据插入失败: {e}")
            return False
    
    def get_stats(self):
        """获取统计信息"""
        try:
            self.cursor.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    MIN(trading_date) as earliest_date,
                    MAX(trading_date) as latest_date,
                    AVG(total_pledge_ratio) as avg_pledge_ratio,
                    MAX(total_pledge_ratio) as max_pledge_ratio,
                    MAX(updated_at) as last_updated
                FROM pledge_market_profile
            """)
            result = self.cursor.fetchone()
            
            if result:
                logger.info("=== 数据库统计信息 ===")
                logger.info(f"总记录数: {result[0]}")
                logger.info(f"日期范围: {result[1]} 至 {result[2]}")
                logger.info(f"平均质押比例: {result[3]:.4f}%" if result[3] else "平均质押比例: N/A")
                logger.info(f"最高质押比例: {result[4]:.4f}%" if result[4] else "最高质押比例: N/A")
                logger.info(f"最后更新: {result[5]}")
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
    
    def run(self):
        """执行完整流程"""
        start_time = time.time()
        
        try:
            logger.info("=== 股权质押市场概况数据爬取开始 ===")
            
            # 1. 连接数据库
            if not self.connect_db():
                return False
            
            # 2. 创建表
            self.create_tables()
            
            # 3. 获取数据
            df = self.fetch_data()
            if df is None or df.empty:
                logger.error("未获取到有效数据")
                return False
            
            # 4. 清洗数据
            df_clean = self.clean_data(df)
            
            # 5. 检查数据是否有变动
            current_hash = self.calculate_data_hash(df_clean)
            if not current_hash:
                logger.error("计算数据哈希失败")
                return False
            
            last_hash, last_count, last_updated = self.get_last_data_hash()
            
            if last_hash == current_hash:
                logger.info("市场概况数据无变动，跳过更新")
                logger.info(f"  当前记录数: {len(df_clean)}")
                logger.info(f"  上次更新: {last_updated}")
                return True
            
            logger.info("市场概况数据有变动，开始更新")
            logger.info(f"  新记录数: {len(df_clean)}")
            logger.info(f"  旧记录数: {last_count}")
            logger.info(f"  上次更新: {last_updated}")
            
            # 6. 插入数据
            success = self.insert_data(df_clean)
            
            if success:
                # 更新版本信息
                self.update_data_version(current_hash, len(df_clean))
                self.connection.commit()
                
                # 7. 统计信息
                self.get_stats()
                
                end_time = time.time()
                logger.info(f"=== 执行完成，总耗时: {end_time - start_time:.2f}秒 ===")
                return True
            else:
                self.connection.rollback()
                logger.error("数据更新失败")
                return False
            
        except Exception as e:
            logger.error(f"执行失败: {e}")
            self.connection.rollback()
            return False
        finally:
            self.close_db()


def main():
    """主函数"""
    try:
        crawler = PledgeMarketProfileCrawler()
        success = crawler.run()
        
        if success:
            logger.info("股权质押市场概况数据更新成功")
            sys.exit(0)
        else:
            logger.error("股权质押市场概况数据更新失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"程序执行异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
