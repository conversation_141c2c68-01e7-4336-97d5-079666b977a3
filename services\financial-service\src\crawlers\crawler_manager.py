#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存任务管理器
- 无Redis的轻量级任务调度
- 支持并发控制和任务状态管理
- 提供WebSocket进度推送
"""
import asyncio
import threading
import uuid
import queue
from typing import Dict, List, Optional, Callable, Any
from enum import Enum
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor
import logging

from .progress_logger import ProgressLogger, LogLevel

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class CrawlTask:
    """爬取任务数据结构"""
    id: str
    company_name: str
    user_id: str
    depth: int = 2
    direction: str = "both"
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress: int = 0
    total_steps: int = 0
    processed_companies: int = 0
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    websocket_callbacks: List[Callable] = field(default_factory=list)

class CrawlTaskManager:
    """内存任务管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, 'initialized'):
            return

        self.tasks: Dict[str, CrawlTask] = {}
        self.running_tasks: Dict[str, threading.Thread] = {}
        self.executor = ThreadPoolExecutor(max_workers=3)  # 限制并发数
        self.logger = logging.getLogger("crawler_manager")

        # 为每个任务创建消息队列，用于SSE端点获取实时消息
        self.task_message_queues: Dict[str, queue.Queue] = {}

        self.initialized = True

        # 启动清理线程
        self._start_cleanup_thread()
    
    def create_task(self, company_name: str, user_id: str, depth: int = 2, 
                   direction: str = "both") -> str:
        """创建新的爬取任务"""
        task_id = str(uuid.uuid4())
        
        # 检查是否已有相同公司的运行中任务
        existing_task = self._find_running_task_for_company(company_name)
        if existing_task:
            self.logger.info(f"Found existing task {existing_task.id} for company {company_name}")
            return existing_task.id
        
        task = CrawlTask(
            id=task_id,
            company_name=company_name,
            user_id=user_id,
            depth=depth,
            direction=direction
        )
        
        self.tasks[task_id] = task
        # 为任务创建消息队列
        self.task_message_queues[task_id] = queue.Queue()
        self.logger.info(f"Created task {task_id} for company {company_name}")
        return task_id
    
    def start_task(self, task_id: str, crawler_func: Callable) -> bool:
        """启动任务执行"""
        if task_id not in self.tasks:
            return False
            
        task = self.tasks[task_id]
        if task.status != TaskStatus.PENDING:
            return False
        
        # 检查并发限制
        if len(self.running_tasks) >= 3:
            self.logger.warning(f"Max concurrent tasks reached, task {task_id} queued")
            return False
        
        # 更新任务状态
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.now()
        
        # 创建进度日志记录器
        progress_logger = ProgressLogger(
            task_id=task_id,
            callback=self._progress_callback
        )
        
        # 在线程池中执行任务
        thread = threading.Thread(
            target=self._execute_task,
            args=(task, crawler_func, progress_logger)
        )
        thread.start()
        
        self.running_tasks[task_id] = thread
        self.logger.info(f"Started task {task_id}")
        return True
    
    def _execute_task(self, task: CrawlTask, crawler_func: Callable,
                     progress_logger: ProgressLogger):
        """执行爬取任务"""
        try:
            self.logger.info(f"Executing task {task.id} for {task.company_name}")

            # 执行爬取
            result = crawler_func(
                company_name=task.company_name,
                depth=task.depth,
                direction=task.direction,
                progress_logger=progress_logger
            )
            
            # 执行爬取
            result = crawler_func(
                company_name=task.company_name,
                depth=task.depth,
                direction=task.direction,
                progress_logger=progress_logger
            )
            
            # 任务成功完成
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.result = result
            task.progress = 100
            
            self.logger.info(f"Task {task.id} completed successfully")
            
            # 通知完成
            duration = self._calculate_duration(task)
            self._notify_websockets(task.id, {
                "type": "status_change",
                "task_id": task.id,
                "status": "completed",
                "duration": duration,
                "result": result
            })
            
        except Exception as e:
            # 任务失败
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            task.error_message = str(e)
            
            self.logger.error(f"Task {task.id} failed: {e}")
            
            # 通知失败
            self._notify_websockets(task.id, {
                "type": "status_change",
                "task_id": task.id,
                "status": "failed",
                "error": str(e)
            })
        
        finally:
            # 清理运行中的任务记录
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if task_id not in self.tasks:
            return None
            
        task = self.tasks[task_id]
        # 确保可JSON序列化
        status_value = task.status
        try:
            # 如果是枚举，取其值，否则转为字符串
            if hasattr(status_value, 'value'):
                status_value = status_value.value
            elif not isinstance(status_value, (str, int, float, bool)) and status_value is not None:
                status_value = str(status_value)
        except Exception:
            status_value = str(status_value)

        return {
            "id": task.id,
            "company_name": task.company_name,
            "status": status_value,
            "progress": task.progress,
            "total_steps": task.total_steps,
            "processed_companies": task.processed_companies,
            "created_at": task.created_at.isoformat(),
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "error_message": task.error_message,
            "duration": self._calculate_duration(task)
        }
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id not in self.tasks:
            return False
            
        task = self.tasks[task_id]
        if task.status not in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            return False
        
        task.status = TaskStatus.CANCELLED
        task.completed_at = datetime.now()
        
        # 如果任务正在运行，尝试停止线程（注意：Python线程不能强制停止）
        if task_id in self.running_tasks:
            self.logger.warning(f"Task {task_id} marked as cancelled, but thread may continue")
        
        self.logger.info(f"Task {task_id} cancelled")
        return True
    
    def register_websocket_callback(self, task_id: str, callback: Callable):
        """注册WebSocket回调"""
        if task_id in self.tasks:
            self.tasks[task_id].websocket_callbacks.append(callback)
    
    def unregister_websocket_callback(self, task_id: str, callback: Callable):
        """注销WebSocket回调"""
        if task_id in self.tasks:
            try:
                self.tasks[task_id].websocket_callbacks.remove(callback)
            except ValueError:
                pass
    
    def _progress_callback(self, progress_data: Dict[str, Any]):
        """进度回调函数"""
        try:
            task_id = progress_data.get('task_id')
            if not task_id or task_id not in self.tasks:
                return

            # 更新任务进度
            task = self.tasks[task_id]
            if 'progress' in progress_data:
                task.progress = progress_data['progress']
            if 'processed_companies' in progress_data:
                task.processed_companies = progress_data['processed_companies']

            # 将消息放入队列供SSE端点使用
            if task_id in self.task_message_queues:
                try:
                    self.task_message_queues[task_id].put_nowait(progress_data)
                except queue.Full:
                    # 如果队列满了，移除最旧的消息
                    try:
                        self.task_message_queues[task_id].get_nowait()
                        self.task_message_queues[task_id].put_nowait(progress_data)
                    except queue.Empty:
                        pass

            # 简单记录进度日志
            self.logger.info(f"Progress update for task {task_id}: {progress_data}")

        except Exception as e:
            self.logger.error(f"Progress callback failed: {e}")

    def get_task_messages(self, task_id: str, timeout: float = 1.0) -> Optional[Dict[str, Any]]:
        """获取任务的下一个消息（阻塞等待）"""
        if task_id not in self.task_message_queues:
            return None

        try:
            return self.task_message_queues[task_id].get(timeout=timeout)
        except queue.Empty:
            return None

    def get_task_messages_nowait(self, task_id: str) -> List[Dict[str, Any]]:
        """获取任务的所有待处理消息（非阻塞）"""
        if task_id not in self.task_message_queues:
            return []

        messages = []
        while True:
            try:
                message = self.task_message_queues[task_id].get_nowait()
                messages.append(message)
            except queue.Empty:
                break
        return messages

    def _notify_websockets(self, task_id: str, data: Dict[str, Any]):
        """通知WebSocket客户端"""
        # 简化：只记录日志，不发送WebSocket消息
        self.logger.info(f"WebSocket notification for task {task_id}: {data}")
    
    def _find_running_task_for_company(self, company_name: str) -> Optional[CrawlTask]:
        """查找公司是否有正在运行的任务"""
        for task in self.tasks.values():
            if (task.company_name == company_name and 
                task.status == TaskStatus.RUNNING):
                return task
        return None
    
    def _calculate_duration(self, task: CrawlTask) -> Optional[float]:
        """计算任务持续时间"""
        if not task.started_at:
            return None
        
        end_time = task.completed_at if task.completed_at else datetime.now()
        duration = (end_time - task.started_at).total_seconds()
        return round(duration, 2)
    
    def _start_cleanup_thread(self):
        """启动清理线程，定期清理过期任务"""
        def cleanup():
            while True:
                try:
                    current_time = datetime.now()
                    expired_tasks = []
                    
                    for task_id, task in self.tasks.items():
                        # 清理24小时前完成的任务
                        if (task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] and
                            task.completed_at and 
                            current_time - task.completed_at > timedelta(hours=24)):
                            expired_tasks.append(task_id)
                    
                    # 删除过期任务
                    for task_id in expired_tasks:
                        del self.tasks[task_id]
                        # 清理对应的消息队列
                        if task_id in self.task_message_queues:
                            del self.task_message_queues[task_id]
                        self.logger.info(f"Cleaned up expired task {task_id}")
                    
                except Exception as e:
                    self.logger.error(f"Cleanup thread error: {e}")
                
                # 每小时清理一次
                threading.Event().wait(3600)
        
        cleanup_thread = threading.Thread(target=cleanup, daemon=True)
        cleanup_thread.start()
    
    def get_running_tasks_count(self) -> int:
        """获取正在运行的任务数量"""
        return len(self.running_tasks)
    
    def get_all_tasks_for_user(self, user_id: str) -> List[Dict[str, Any]]:
        """获取用户的所有任务"""
        user_tasks = []
        for task in self.tasks.values():
            if task.user_id == user_id:
                user_tasks.append(self.get_task_status(task.id))
        
        # 按创建时间降序排序
        user_tasks.sort(key=lambda x: x['created_at'], reverse=True)
        return user_tasks

# 全局单例实例
task_manager = CrawlTaskManager()