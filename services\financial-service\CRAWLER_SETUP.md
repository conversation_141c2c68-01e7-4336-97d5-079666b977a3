# 股权穿透爬虫设置指南

## 问题说明

天眼查网站有严格的反爬虫机制，直接使用自动化工具访问会返回419错误（请求过于频繁）。为了绕过这个限制，我们需要使用"连接到现有浏览器"的策略。

## 推荐设置方法

### 方法1：连接到现有浏览器（推荐）

1. **启动Chrome浏览器（调试模式）**
   ```bash
   # Windows
   "C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="C:\temp\chrome_debug"
   
   # macOS
   /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --remote-debugging-port=9222 --user-data-dir="/tmp/chrome_debug"
   
   # Linux
   google-chrome --remote-debugging-port=9222 --user-data-dir="/tmp/chrome_debug"
   ```

2. **手动登录天眼查**
   - 在打开的浏览器中访问 https://www.tianyancha.com
   - 手动完成登录（输入账号密码、验证码等）
   - 确保能正常访问企业信息页面

3. **启动股权穿透服务**
   - 重启金融服务
   - 爬虫会自动连接到现有浏览器会话
   - 利用已登录状态绕过反爬虫限制

### 方法2：新浏览器实例（备用）

如果无法连接到现有浏览器，系统会自动启动新的浏览器实例：
- 使用反爬虫伪装技术
- 可能需要手动处理验证码
- 成功率相对较低

## 验证设置

启动服务后，查看日志输出：

```
✅ 成功连接到现有浏览器会话  # 推荐状态
```

或

```
⚠️ 无法连接到现有浏览器，启动新浏览器实例  # 备用状态
✅ 新浏览器实例启动成功
💡 提示：手动打开Chrome浏览器并访问天眼查登录，然后重启服务以获得更好效果
```

## 常见问题

### Q: 为什么会出现"未找到公司"错误？
A: 这通常是因为天眼查的反爬虫机制阻止了请求。请按照上述方法设置浏览器连接。

### Q: Chrome调试端口被占用怎么办？
A: 关闭所有Chrome进程，然后重新启动调试模式的Chrome。

### Q: 连接现有浏览器失败怎么办？
A: 检查Chrome是否以调试模式启动，端口9222是否可用。

## 技术原理

1. **现有浏览器连接**：通过Chrome的远程调试协议连接到用户手动打开的浏览器
2. **会话复用**：利用用户已登录的会话状态
3. **反爬虫绕过**：避免自动化检测，使用真实的用户会话

## 注意事项

- 确保Chrome浏览器版本与ChromeDriver兼容
- 保持浏览器会话活跃，避免登录过期
- 不要在调试模式的浏览器中进行其他敏感操作
