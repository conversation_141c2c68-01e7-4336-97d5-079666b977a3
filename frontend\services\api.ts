import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosHeaders } from 'axios';
import useAuthStore from '@/store/auth';

// API基础配置
const BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:16576/api';

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    if (!config.headers) {
      config.headers = new AxiosHeaders();
    }
    // 直接从 zustand store 获取 token
    const token = useAuthStore.getState().token;
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    
    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = Date.now().toString();
    
    console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('[API Request Error]', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log(`[API Response] ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('[API Response Error]', error);
    
    // 处理401错误（未认证）
    if (error.response?.status === 401) {
      // 通过调用 store 中的 logout 方法来处理认证失败,
      // 这样可以集中管理认证状态, 并且 logout 函数内部会处理页面跳转。
      // 检查以避免在登录页面因401错误而无限循环。
      if (window.location.pathname !== '/login') {
        useAuthStore.getState().logout();
      }
    }
    
    // 处理403错误（权限不足）
    if (error.response?.status === 403) {
      console.error('权限不足');
      // 可以显示权限不足的提示
    }
    
    // 处理500错误（服务器错误）
    if (error.response?.status >= 500) {
      console.error('服务器错误，请稍后重试');
    }
    
    return Promise.reject(error);
  }
);

// 通用API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  requestId?: string;
}

// 通用API请求方法
export const api = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.get(url, config).then(response => response.data),
    
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.post(url, data, config).then(response => response.data),
    
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.put(url, data, config).then(response => response.data),
    
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.delete(url, config).then(response => response.data),
    
  upload: <T = any>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
    }).then(response => response.data),
};

// 健康检查
export const healthCheck = async (): Promise<boolean> => {
  try {
    const response = await api.get('/health');
    return response.success !== false;
  } catch (error) {
    console.error('健康检查失败:', error);
    return false;
  }
};

export default apiClient;