#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股权数据持久化服务
- 负责将抓取的股权数据保存到Neo4j数据库
- 提供缓存检查功能，避免重复抓取
- 参考refer_code中的graph_manager.py实现
"""
import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

from ..graph.neo4j_client import Neo4jClient

logger = logging.getLogger(__name__)


class EquityPersistenceService:
    """股权数据持久化服务"""
    
    def __init__(self, neo4j_client: Neo4jClient):
        self.neo4j_client = neo4j_client
        
    async def check_company_cache(self, tianyancha_id: int, days: int = 1) -> Tuple[bool, List[Dict], List[Dict]]:
        """
        检查公司是否存在且在指定天数内更新过
        返回: (是否存在, 股东列表, 投资列表)
        """
        try:
            threshold_date = datetime.utcnow() - timedelta(days=days)
            
            query = """
            MATCH (c:Company {tianyancha_id: $tianyancha_id})
            WHERE c.updated_at IS NOT NULL AND c.updated_at >= datetime($threshold_date)
            
            // 获取上游股东 (公司或个人)
            OPTIONAL MATCH (shareholder)-[:HOLDS_SHARE]->(c)
            WITH c, collect(DISTINCT shareholder) AS shareholders
            
            // 获取下游投资 (仅公司)
            OPTIONAL MATCH (c)-[:HOLDS_SHARE]->(investment:Company)
            WITH c, shareholders, collect(DISTINCT investment) AS investments
            
            RETURN c AS company_node, shareholders, investments
            """
            
            result = await self.neo4j_client.execute_query(
                query, 
                {
                    'tianyancha_id': tianyancha_id,
                    'threshold_date': threshold_date.isoformat()
                }
            )
            
            if result and result[0].get('company_node'):
                return True, result[0].get('shareholders', []), result[0].get('investments', [])
            
            return False, [], []
            
        except Exception as e:
            logger.error(f"Cache check failed for company {tianyancha_id}: {e}")
            return False, [], []
    
    async def save_company_and_relationships(self, scraped_data: Dict[str, Any]) -> bool:
        """
        保存公司及其股权关系数据到Neo4j
        参考refer_code中的实现
        """
        try:
            # 1. 提取并处理公司基本信息
            basic_info = scraped_data.get('basic_info', {})
            source_url = scraped_data.get('source_url', '')
            
            # 从URL提取公司ID
            match = re.search(r'company/(\d+)', source_url)
            if not match:
                logger.warning(f"URL格式不正确，无法提取公司ID: {source_url}")
                return False
            
            company_t_id = int(match.group(1))
            
            # 2. 创建或更新公司节点
            await self._create_or_update_company(company_t_id, basic_info, source_url)
            
            # 3. 处理股东关系
            shareholders_info = scraped_data.get('shareholders_info', [])
            if shareholders_info:
                await self._process_shareholders(company_t_id, shareholders_info)
            
            # 4. 处理对外投资关系
            investments_info = scraped_data.get('investments_info', [])
            if investments_info:
                await self._process_investments(company_t_id, investments_info)
            
            logger.info(f"💾 数据已成功写入数据库: {basic_info.get('企业名称', '未知公司')}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 写入数据库时发生错误: {e}")
            return False
    
    async def _create_or_update_company(self, tianyancha_id: int, basic_info: Dict, source_url: str):
        """创建或更新公司节点"""
        query = """
        MERGE (c:Company {tianyancha_id: $tianyancha_id})
        ON CREATE SET 
            c += $props, 
            c.created_at = datetime(),
            c.updated_at = datetime()
        ON MATCH SET 
            c += $props, 
            c.updated_at = datetime()
        RETURN c
        """
        
        # 准备属性字典，同时支持中英文字段
        props_to_set = {
            'name': basic_info.get('企业名称'),
            '企业名称': basic_info.get('企业名称'),
            'legal_representative': basic_info.get('法定代表人'),
            '法定代表人': basic_info.get('法定代表人'),
            'registered_capital': basic_info.get('注册资本'),
            '注册资本': basic_info.get('注册资本'),
            'establishment_date': basic_info.get('成立日期'),
            '成立日期': basic_info.get('成立日期'),
            'business_status': basic_info.get('经营状态'),
            '经营状态': basic_info.get('经营状态'),
            'credit_code': basic_info.get('统一社会信用代码'),
            '统一社会信用代码': basic_info.get('统一社会信用代码'),
            'company_type': basic_info.get('企业类型'),
            '企业类型': basic_info.get('企业类型'),
            'industry': basic_info.get('行业'),
            '行业': basic_info.get('行业'),
            'address': basic_info.get('注册地址'),
            '注册地址': basic_info.get('注册地址'),
            'tags': basic_info.get('标签', ''),
            '标签': basic_info.get('标签', ''),
            'source_url': source_url
        }
        
        # 添加其他所有信息，避免数据丢失
        for key, value in basic_info.items():
            if key not in props_to_set and value:
                props_to_set[key] = value
        
        await self.neo4j_client.execute_write_query(
            query, 
            {
                'tianyancha_id': tianyancha_id,
                'props': props_to_set
            }
        )
        
        # 检查企业类型，如果是合伙企业，添加额外标签
        company_type = props_to_set.get('企业类型', '')
        if '合伙' in company_type:
            partnership_query = """
            MATCH (c:Company {tianyancha_id: $tianyancha_id})
            SET c:Partnership
            """
            await self.neo4j_client.execute_write_query(
                partnership_query, 
                {'tianyancha_id': tianyancha_id}
            )
    
    async def _process_shareholders(self, company_id: int, shareholders_info: List[Dict]):
        """处理股东关系"""
        for shareholder in shareholders_info:
            try:
                shareholder_name = shareholder.get('股东名称', '').strip()
                if not shareholder_name:
                    continue
                
                # 判断是公司还是个人
                is_company = self._is_company_name(shareholder_name)
                
                if is_company:
                    # 创建公司股东节点和关系
                    await self._create_company_shareholder_relationship(
                        company_id, shareholder_name, shareholder
                    )
                else:
                    # 创建个人股东节点和关系
                    await self._create_person_shareholder_relationship(
                        company_id, shareholder_name, shareholder
                    )
                    
            except Exception as e:
                logger.error(f"处理股东 {shareholder.get('股东名称', 'Unknown')} 时出错: {e}")
    
    async def _process_investments(self, company_id: int, investments_info: List[Dict]):
        """处理对外投资关系"""
        for investment in investments_info:
            try:
                investment_name = investment.get('被投资公司名称', '').strip()
                if not investment_name:
                    continue
                
                await self._create_investment_relationship(company_id, investment_name, investment)
                
            except Exception as e:
                logger.error(f"处理投资 {investment.get('被投资公司名称', 'Unknown')} 时出错: {e}")
    
    def _is_company_name(self, name: str) -> bool:
        """判断是否为公司名称"""
        company_suffixes = [
            '有限公司', '股份有限公司', '集团', '企业', '公司', '合伙企业',
            '有限责任公司', '股份公司', '实业', '投资', '控股', '发展',
            'Co.', 'Ltd', 'Inc', 'Corp', 'LLC', 'LP'
        ]
        return any(suffix in name for suffix in company_suffixes)
    
    async def _create_company_shareholder_relationship(self, company_id: int, shareholder_name: str, shareholder_data: Dict):
        """创建公司股东关系"""
        query = """
        MATCH (target:Company {tianyancha_id: $company_id})
        MERGE (shareholder:Company {name: $shareholder_name})
        ON CREATE SET 
            shareholder.企业名称 = $shareholder_name,
            shareholder.created_at = datetime()
        MERGE (shareholder)-[r:HOLDS_SHARE]->(target)
        SET r.percentage = $percentage,
            r.amount = $amount,
            r.updated_at = datetime()
        """
        
        await self.neo4j_client.execute_write_query(query, {
            'company_id': company_id,
            'shareholder_name': shareholder_name,
            'percentage': shareholder_data.get('持股比例', ''),
            'amount': shareholder_data.get('认缴出资额', '')
        })
    
    async def _create_person_shareholder_relationship(self, company_id: int, shareholder_name: str, shareholder_data: Dict):
        """创建个人股东关系"""
        query = """
        MATCH (target:Company {tianyancha_id: $company_id})
        MERGE (shareholder:Person {name: $shareholder_name})
        ON CREATE SET 
            shareholder.created_at = datetime()
        MERGE (shareholder)-[r:HOLDS_SHARE]->(target)
        SET r.percentage = $percentage,
            r.amount = $amount,
            r.updated_at = datetime()
        """
        
        await self.neo4j_client.execute_write_query(query, {
            'company_id': company_id,
            'shareholder_name': shareholder_name,
            'percentage': shareholder_data.get('持股比例', ''),
            'amount': shareholder_data.get('认缴出资额', '')
        })
    
    async def _create_investment_relationship(self, company_id: int, investment_name: str, investment_data: Dict):
        """创建投资关系"""
        query = """
        MATCH (investor:Company {tianyancha_id: $company_id})
        MERGE (investment:Company {name: $investment_name})
        ON CREATE SET 
            investment.企业名称 = $investment_name,
            investment.created_at = datetime()
        MERGE (investor)-[r:HOLDS_SHARE]->(investment)
        SET r.percentage = $percentage,
            r.amount = $amount,
            r.updated_at = datetime()
        """
        
        await self.neo4j_client.execute_write_query(query, {
            'company_id': company_id,
            'investment_name': investment_name,
            'percentage': investment_data.get('持股比例', ''),
            'amount': investment_data.get('投资金额', '')
        })
