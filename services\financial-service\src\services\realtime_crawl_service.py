#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时爬取服务
- 整合任务管理和爬虫执行
- 提供高级API接口
"""
import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from ..crawlers.crawler_manager import task_manager
from ..crawlers.integrated_crawler import crawl_company_equity_task
from ..crawlers.progress_logger import ProgressLogger
from ..services.websocket_service import websocket_manager
from ..models.crawl_schemas import (
    RealtimeCrawlRequest, StartCrawlResponse, TaskStatusResponse,
    CrawlCompleteResponse, TaskStatus, CrawlResult
)

class RealtimeCrawlService:
    """实时爬取服务"""
    
    def __init__(self):
        self.logger = logging.getLogger("realtime_crawl_service")
    
    async def start_crawl(self, request: RealtimeCrawlRequest, user_id: str) -> StartCrawlResponse:
        """启动实时爬取任务"""
        try:
            # 创建任务
            task_id = task_manager.create_task(
                company_name=request.company_name,
                user_id=user_id,
                depth=request.depth,
                direction=request.direction  # 字符串枚举直接使用，不需要.value
            )
            
            # 创建进度日志记录器，集成WebSocket推送
            progress_logger = ProgressLogger(
                task_id=task_id,
                callback=self._create_progress_callback(task_id)
            )
            
            # 启动任务
            success = task_manager.start_task(
                task_id=task_id,
                crawler_func=self._create_crawler_wrapper(progress_logger)
            )
            
            if not success:
                # 可能是达到并发限制或任务已在运行
                existing_task = task_manager.get_task_status(task_id)
                if existing_task and existing_task['status'] == 'running':
                    return StartCrawlResponse(
                        task_id=task_id,
                        message=f"公司 '{request.company_name}' 已有正在进行的分析任务",
                        websocket_url=f"/ws/crawl-progress/{task_id}",
                        estimated_duration=self._estimate_duration(request.depth, request.direction)
                    )
                else:
                    raise Exception("无法启动任务，可能服务器繁忙，请稍后重试")
            
            self.logger.info(f"Started crawl task {task_id} for company {request.company_name}")
            
            return StartCrawlResponse(
                task_id=task_id,
                message=f"已开始分析 '{request.company_name}' 的股权结构",
                websocket_url=f"/ws/crawl-progress/{task_id}",
                estimated_duration=self._estimate_duration(request.depth, request.direction)
            )
            
        except Exception as e:
            self.logger.error(f"Failed to start crawl for {request.company_name}: {e}")
            raise Exception(f"启动分析失败: {str(e)}")
    
    async def get_task_status(self, task_id: str) -> Optional[TaskStatusResponse]:
        """获取任务状态"""
        task_status = task_manager.get_task_status(task_id)
        if not task_status:
            return None
        
        return TaskStatusResponse(**task_status)
    
    async def cancel_task(self, task_id: str, user_id: str) -> bool:
        """取消任务"""
        # 验证用户权限
        task_status = task_manager.get_task_status(task_id)
        if not task_status:
            return False
        
        # 简单权限检查（实际应用中需要更严格的权限验证）
        success = task_manager.cancel_task(task_id)
        
        if success:
            # 通知WebSocket客户端
            await websocket_manager.send_task_status_change(
                task_id, TaskStatus.CANCELLED,
                {"cancelled_by": user_id}
            )
            self.logger.info(f"Task {task_id} cancelled by user {user_id}")
        
        return success
    
    async def get_user_tasks(self, user_id: str) -> list:
        """获取用户的所有任务"""
        tasks = task_manager.get_all_tasks_for_user(user_id)
        return [TaskStatusResponse(**task) for task in tasks]
    
    async def get_crawl_result(self, task_id: str) -> Optional[CrawlCompleteResponse]:
        """获取爬取结果"""
        task_status = task_manager.get_task_status(task_id)
        if not task_status:
            return None
        
        if task_status['status'] != TaskStatus.COMPLETED:
            # 安全地转换状态
            status_value = task_status['status']
            if isinstance(status_value, str):
                try:
                    status_enum = TaskStatus(status_value)
                except ValueError:
                    status_enum = TaskStatus.FAILED
            else:
                status_enum = status_value

            return CrawlCompleteResponse(
                task_id=task_id,
                status=status_enum,
                result=None,
                error_message=task_status.get('error_message'),
                duration=task_status.get('duration', 0),
                stats={}
            )
        
        # 获取实际的爬取结果
        task = task_manager.tasks.get(task_id)
        if not task or not task.result:
            return CrawlCompleteResponse(
                task_id=task_id,
                status=TaskStatus.COMPLETED,
                result=None,
                error_message="结果数据不可用",
                duration=task_status.get('duration', 0),
                stats={}
            )
        
        # 转换结果格式
        result_data = task.result
        crawl_result = CrawlResult(
            nodes=result_data.get('nodes', []),
            edges=result_data.get('edges', []),
            metadata=result_data.get('metadata', {})
        )
        
        return CrawlCompleteResponse(
            task_id=task_id,
            status=TaskStatus.COMPLETED,
            result=crawl_result,
            error_message=None,
            duration=task_status.get('duration', 0),
            stats={
                'total_nodes': len(crawl_result.nodes),
                'total_edges': len(crawl_result.edges),
                'processed_companies': task_status.get('processed_companies', 0)
            }
        )
    
    def _create_progress_callback(self, task_id: str):
        """创建进度回调函数"""
        def progress_callback(log_entry: Dict[str, Any]):
            try:
                # 确保log_entry包含task_id
                log_entry['task_id'] = task_id

                # 使用任务管理器的进度回调（它会处理WebSocket通知）
                task_manager._progress_callback(log_entry)
            except Exception as e:
                self.logger.error(f"Progress callback error for task {task_id}: {e}")

        return progress_callback
    
    def _create_crawler_wrapper(self, progress_logger: ProgressLogger):
        """创建爬虫包装函数"""
        def crawler_wrapper(company_name: str, depth: int, direction: str, **kwargs):
            """包装爬虫函数以支持同步调用"""
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                return loop.run_until_complete(
                    crawl_company_equity_task(
                        company_name=company_name,
                        depth=depth,
                        direction=direction,
                        progress_logger=progress_logger
                    )
                )
            finally:
                loop.close()
        
        return crawler_wrapper
    
    def _estimate_duration(self, depth: int, direction: str) -> int:
        """估算任务持续时间（秒）"""
        base_time = 30  # 基础时间30秒
        
        # 根据深度增加时间
        depth_multiplier = depth * 20
        
        # 根据方向调整时间
        direction_multiplier = {
            'up': 1.0,
            'down': 1.2,
            'both': 1.8
        }.get(direction, 1.0)
        
        estimated = int(base_time + depth_multiplier * direction_multiplier)
        return min(estimated, 300)  # 最多5分钟

# 全局服务实例
realtime_crawl_service = RealtimeCrawlService()