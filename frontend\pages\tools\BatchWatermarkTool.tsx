import React, { useState } from 'react';
import { Layout, Typography, Button, Statistic, message } from 'antd';
import { Tag, Image, Upload, Settings } from 'lucide-react';
import { GlassmorphicCard } from '@/components/GlassmorphicCard';

const { Sider, Content } = Layout;
const { Title, Text, Paragraph } = Typography;

export const BatchWatermarkTool: React.FC = () => {
    const [loading, setLoading] = useState(false);

    const handleProcess = () => {
        setLoading(true);
        message.info('批量加水印功能正在开发中...');
        setTimeout(() => setLoading(false), 2000);
    };

    return (
        <div style={{ width: '100%', height: '100%', minHeight: '600px', position: 'relative' }}>
            <Layout style={{ background: 'transparent', padding: '16px', height: '100%', minHeight: '600px' }}>
                <Sider width={280} style={{ background: 'transparent', marginRight: '16px' }}>
                    <GlassmorphicCard title={<Title level={5} className="!text-white">控制面板</Title>}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
                            <Statistic 
                                title={<Text className="!text-gray-300">处理文件</Text>} 
                                value={0} 
                                prefix={<Image size={16} />} 
                                valueStyle={{ color: '#fff', fontSize: '18px' }} 
                            />
                            <Statistic 
                                title={<Text className="!text-gray-300">完成数量</Text>} 
                                value={0} 
                                prefix={<Tag size={16} />} 
                                valueStyle={{ color: '#fff', fontSize: '18px' }} 
                            />
                        </div>
                        
                        <Title level={5} className="!text-white" style={{ marginTop: '24px' }}>快速操作</Title>
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                            <Button 
                                type="primary" 
                                icon={<Upload size={16} />} 
                                onClick={handleProcess}
                                loading={loading}
                                style={{ width: '100%' }}
                            >
                                开始处理
                            </Button>
                            <Button 
                                icon={<Settings size={16} />} 
                                style={{ width: '100%' }} 
                                disabled
                            >
                                水印设置
                            </Button>
                        </div>
                    </GlassmorphicCard>
                </Sider>
                
                <Content style={{ position: 'relative' }}>
                    <GlassmorphicCard style={{ padding: 0, height: '100%' }}>
                        <div style={{ 
                            padding: '48px', 
                            height: '100%', 
                            display: 'flex', 
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            textAlign: 'center'
                        }}>
                            <Tag size={64} style={{ color: '#22d3ee', marginBottom: '24px' }} />
                            <Title level={3} className="!text-white" style={{ marginBottom: '16px' }}>
                                批量加水印工具
                            </Title>
                            <Paragraph style={{ 
                                color: 'rgba(255, 255, 255, 0.7)', 
                                fontSize: '16px',
                                maxWidth: '400px',
                                lineHeight: 1.6
                            }}>
                                为图片和文档批量添加水印，保护您的知识产权。
                                该功能正在开发中，敬请期待。
                            </Paragraph>
                        </div>
                    </GlassmorphicCard>
                </Content>
            </Layout>
        </div>
    );
};