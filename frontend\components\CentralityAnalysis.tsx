import React, { useState, useEffect } from 'react';
import { Card, Table, Progress, Badge, Tabs, Select, Typography, Tag } from 'antd';
import { TrendingUp, Network, Target, Star, Share2, Info } from 'lucide-react';

// const { TabPane } = Tabs; // Removed: TabPane is deprecated, using items prop instead
const { Title, Text } = Typography;
const { Option } = Select;

interface NodeCentrality {
  id: string;
  name: string;
  type: string;
  percentage: number;
  level: number;
  centrality: {
    betweenness: number;
    closeness: number;
    degree: number;
    pageRank: number;
    eigenvector?: number;
  };
  community?: number;
  importance: number;
}

interface CentralityAnalysisProps {
  nodes: NodeCentrality[];
  onNodeSelect?: (nodeId: string) => void;
  className?: string;
}

export const CentralityAnalysis: React.FC<CentralityAnalysisProps> = ({
  nodes,
  onNodeSelect,
  className = ''
}) => {
  const [sortBy, setSortBy] = useState<'betweenness' | 'closeness' | 'degree' | 'pageRank'>('betweenness');
  const [analysisResults, setAnalysisResults] = useState<any>({});

  // 计算分析结果
  useEffect(() => {
    if (!nodes.length) return;

    const results: any = {
      keyPlayers: [] as any[],
      bridges: [] as any[],
      influencers: [] as any[],
      connectors: [] as any[],
      communities: new Map(),
      insights: [] as any[]
    };

    // 按不同指标排序获取关键节点
    const sortedByBetweenness = [...nodes].sort((a, b) => 
      b.centrality.betweenness - a.centrality.betweenness
    );
    const sortedByCloseness = [...nodes].sort((a, b) => 
      b.centrality.closeness - a.centrality.closeness
    );
    const sortedByDegree = [...nodes].sort((a, b) => 
      b.centrality.degree - a.centrality.degree
    );
    const sortedByPageRank = [...nodes].sort((a, b) => 
      b.centrality.pageRank - a.centrality.pageRank
    );

    // 识别关键角色
    results.keyPlayers = sortedByPageRank.slice(0, 5).map(node => ({
      ...node,
      role: 'key_player',
      score: node.centrality.pageRank
    }));

    // 识别桥梁节点（高介数中心性）
    results.bridges = sortedByBetweenness.slice(0, 3).map(node => ({
      ...node,
      role: 'bridge',
      score: node.centrality.betweenness
    }));

    // 识别影响者（高接近中心性）
    results.influencers = sortedByCloseness.slice(0, 3).map(node => ({
      ...node,
      role: 'influencer',
      score: node.centrality.closeness
    }));

    // 识别连接器（高度中心性）
    results.connectors = sortedByDegree.slice(0, 3).map(node => ({
      ...node,
      role: 'connector',
      score: node.centrality.degree
    }));

    // 社群分析
    const communityMap = new Map();
    nodes.forEach(node => {
      if (node.community !== undefined) {
        if (!communityMap.has(node.community)) {
          communityMap.set(node.community, []);
        }
        communityMap.get(node.community).push(node);
      }
    });
    results.communities = communityMap;

    // 生成洞察
    const insights = [];
    
    // 控制权集中度分析
    const controlNodes = nodes.filter(n => n.percentage > 50);
    if (controlNodes.length > 0) {
      insights.push({
        type: 'control',
        title: '控制权集中度',
        description: `发现 ${controlNodes.length} 个具有控制权的节点`,
        level: controlNodes.length > 3 ? 'high' : 'medium',
        nodes: controlNodes.map(n => n.id)
      });
    }

    // 网络脆弱性分析
    const bridgeNodes = (results.bridges as any[]).filter((b: any) => b.score > 0.1);
    if (bridgeNodes.length > 0) {
      insights.push({
        type: 'vulnerability',
        title: '网络脆弱性',
        description: `${bridgeNodes.length} 个关键桥梁节点的失效可能导致网络分割`,
        level: 'high',
        nodes: bridgeNodes.map((n: any) => n.id)
      });
    }

    // 影响力分布分析
    const topInfluencers = (results.influencers as any[]).filter((i: any) => i.score > 0.3);
    if (topInfluencers.length > 0) {
      insights.push({
        type: 'influence',
        title: '影响力分布',
        description: `${topInfluencers.length} 个节点具有高影响力`,
        level: 'medium',
        nodes: topInfluencers.map((n: any) => n.id)
      });
    }

    results.insights = insights;
    setAnalysisResults(results);
  }, [nodes]);

  // 表格列配置
  const getColumns = () => {
    const baseColumns = [
      {
        title: '节点',
        dataIndex: 'name',
        key: 'name',
        render: (text: string, record: NodeCentrality) => (
          <div className="flex items-center space-x-2">
            <Badge
              color={getNodeColor(record.type)}
            />
            <span className="font-medium text-primary">{text}</span>
            <Tag color="blue">
              {record.percentage}%
            </Tag>
          </div>
        )
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        render: (type: string) => (
          <Tag color={getTypeColor(type)}>
            {getTypeName(type)}
          </Tag>
        )
      },
      {
        title: '层级',
        dataIndex: 'level',
        key: 'level',
        render: (level: number) => (
          <Badge count={level} style={{ backgroundColor: '#52c41a' }} />
        )
      }
    ];

    // 根据排序字段添加对应的中心性列
    const centralityColumns = {
      betweenness: {
        title: '介数中心性',
        dataIndex: ['centrality', 'betweenness'],
        key: 'betweenness',
        render: (value: number) => (
          <div className="flex items-center space-x-2">
            <Progress 
              percent={Math.round(value * 100)} 
              size="small" 
              strokeColor="#1890ff"
              showInfo={false}
              style={{ width: 80 }}
            />
            <span className="text-xs text-secondary">{value.toFixed(3)}</span>
          </div>
        ),
        sorter: (a: NodeCentrality, b: NodeCentrality) => 
          a.centrality.betweenness - b.centrality.betweenness
      },
      closeness: {
        title: '接近中心性',
        dataIndex: ['centrality', 'closeness'],
        key: 'closeness',
        render: (value: number) => (
          <div className="flex items-center space-x-2">
            <Progress 
              percent={Math.round(value * 100)} 
              size="small" 
              strokeColor="#52c41a"
              showInfo={false}
              style={{ width: 80 }}
            />
            <span className="text-xs text-secondary">{value.toFixed(3)}</span>
          </div>
        ),
        sorter: (a: NodeCentrality, b: NodeCentrality) => 
          a.centrality.closeness - b.centrality.closeness
      },
      degree: {
        title: '度中心性',
        dataIndex: ['centrality', 'degree'],
        key: 'degree',
        render: (value: number) => (
          <div className="flex items-center space-x-2">
            <Badge count={value} style={{ backgroundColor: '#fa8c16' }} />
            <span className="text-xs text-secondary">连接数</span>
          </div>
        ),
        sorter: (a: NodeCentrality, b: NodeCentrality) => 
          a.centrality.degree - b.centrality.degree
      },
      pageRank: {
        title: 'PageRank',
        dataIndex: ['centrality', 'pageRank'],
        key: 'pageRank',
        render: (value: number) => (
          <div className="flex items-center space-x-2">
            <Progress 
              percent={Math.round(value * 1000)} 
              size="small" 
              strokeColor="#722ed1"
              showInfo={false}
              style={{ width: 80 }}
            />
            <span className="text-xs text-secondary">{value.toFixed(4)}</span>
          </div>
        ),
        sorter: (a: NodeCentrality, b: NodeCentrality) => 
          a.centrality.pageRank - b.centrality.pageRank
      }
    };

    return [...baseColumns, centralityColumns[sortBy]];
  };

  // 辅助函数
  const getNodeColor = (type: string) => {
    const colors: Record<string, string> = {
      parent: '#2563eb',
      subsidiary: '#06b6d4',
      holding: '#10b981',
      partner: '#8b5cf6',
      person: '#f59e0b'
    };
    return colors[type] ?? '#64748b';
  };

  const getTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      parent: 'blue',
      subsidiary: 'cyan',
      holding: 'green',
      partner: 'purple',
      person: 'orange'
    };
    return colors[type] ?? 'default';
  };

  const getTypeName = (type: string) => {
    const names: Record<string, string> = {
      parent: '母公司',
      subsidiary: '子公司',
      holding: '控股公司',
      partner: '参股公司',
      person: '个人'
    };
    return names[type] ?? type;
  };



  const handleNodeClick = (nodeId: string) => {
    if (onNodeSelect) {
      onNodeSelect(nodeId);
    }
  };

  const sortedNodes = [...nodes].sort((a, b) => 
    b.centrality[sortBy] - a.centrality[sortBy]
  );

  return (
    <div className={`space-y-6 ${className}`}>
      <Card className="professional-card">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <TrendingUp className="text-2xl text-accent-primary" />
            <div>
              <Title level={4} className="m-0 text-primary">节点中心性分析</Title>
              <Text className="text-secondary">
                识别网络中的关键节点和角色
              </Text>
            </div>
          </div>
          <Select
            value={sortBy}
            onChange={setSortBy}
            style={{ width: 140 }}
          >
            <Option value="betweenness">介数中心性</Option>
            <Option value="closeness">接近中心性</Option>
            <Option value="degree">度中心性</Option>
            <Option value="pageRank">PageRank</Option>
          </Select>
        </div>

        <Tabs 
          defaultActiveKey="ranking" 
          className="professional-tabs"
          items={[
            {
              label: "中心性排名",
              key: "ranking",
              children: (
                <Table
                  dataSource={sortedNodes}
                  columns={getColumns()}
                  rowKey="id"
                  size="small"
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => 
                      `${range[0]}-${range[1]} 共 ${total} 个节点`
                  }}
                  onRow={(record) => ({
                    onClick: () => handleNodeClick(record.id),
                    style: { cursor: 'pointer' }
                  })}
                  className="professional-table"
                />
              )
            },
            {
              label: "关键角色",
              key: "roles",
              children: (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* 关键角色 */}
                  <Card size="small" className="bg-surface">
                    <div className="flex items-center space-x-2 mb-3">
                      <Star size={16} className="text-yellow-500" />
                      <Text className="font-medium">关键角色</Text>
                    </div>
                    <div className="space-y-2">
                      {analysisResults.keyPlayers?.slice(0, 3).map((node: any) => (
                        <div 
                          key={node.id}
                          className="flex items-center justify-between p-2 bg-bg-elevated rounded cursor-pointer hover:bg-bg-surface"
                          onClick={() => handleNodeClick(node.id)}
                        >
                          <div className="flex items-center space-x-2">
                            <Badge color={getNodeColor(node.type)} />
                            <span className="text-sm font-medium">{node.name}</span>
                          </div>
                          <div className="text-xs text-secondary">
                            {node.score.toFixed(4)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </Card>

                  {/* 桥梁节点 */}
                  <Card size="small" className="bg-surface">
                    <div className="flex items-center space-x-2 mb-3">
                      <Share2 size={16} className="text-red-500" />
                      <Text className="font-medium">桥梁节点</Text>
                    </div>
                    <div className="space-y-2">
                      {analysisResults.bridges?.slice(0, 3).map((node: any) => (
                        <div 
                          key={node.id}
                          className="flex items-center justify-between p-2 bg-bg-elevated rounded cursor-pointer hover:bg-bg-surface"
                          onClick={() => handleNodeClick(node.id)}
                        >
                          <div className="flex items-center space-x-2">
                            <Badge color={getNodeColor(node.type)} />
                            <span className="text-sm font-medium">{node.name}</span>
                          </div>
                          <div className="text-xs text-secondary">
                            {node.score.toFixed(3)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </Card>

                  {/* 影响者 */}
                  <Card size="small" className="bg-surface">
                    <div className="flex items-center space-x-2 mb-3">
                      <Target size={16} className="text-cyan-500" />
                      <Text className="font-medium">影响者</Text>
                    </div>
                    <div className="space-y-2">
                      {analysisResults.influencers?.slice(0, 3).map((node: any) => (
                        <div 
                          key={node.id}
                          className="flex items-center justify-between p-2 bg-bg-elevated rounded cursor-pointer hover:bg-bg-surface"
                          onClick={() => handleNodeClick(node.id)}
                        >
                          <div className="flex items-center space-x-2">
                            <Badge color={getNodeColor(node.type)} />
                            <span className="text-sm font-medium">{node.name}</span>
                          </div>
                          <div className="text-xs text-secondary">
                            {node.score.toFixed(3)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </Card>

                  {/* 连接器 */}
                  <Card size="small" className="bg-surface">
                    <div className="flex items-center space-x-2 mb-3">
                      <Network size={16} className="text-green-500" />
                      <Text className="font-medium">连接器</Text>
                    </div>
                    <div className="space-y-2">
                      {analysisResults.connectors?.slice(0, 3).map((node: any) => (
                        <div 
                          key={node.id}
                          className="flex items-center justify-between p-2 bg-bg-elevated rounded cursor-pointer hover:bg-bg-surface"
                          onClick={() => handleNodeClick(node.id)}
                        >
                          <div className="flex items-center space-x-2">
                            <Badge color={getNodeColor(node.type)} size="small" />
                            <span className="text-sm font-medium">{node.name}</span>
                          </div>
                          <div className="text-xs text-secondary">
                            {node.score} 连接
                          </div>
                        </div>
                      ))}
                    </div>
                  </Card>
                </div>
              )
            },
            {
              label: "网络洞察",
              key: "insights",
              children: (
                <div className="space-y-4">
                  {analysisResults.insights?.map((insight: any, index: number) => (
                    <Card 
                      key={index}
                      size="small" 
                      className={`border-l-4 ${
                        insight.level === 'high' 
                          ? 'border-l-red-500 bg-red-50/5' 
                          : insight.level === 'medium'
                          ? 'border-l-yellow-500 bg-yellow-50/5'
                          : 'border-l-blue-500 bg-blue-50/5'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`p-2 rounded-full ${
                          insight.level === 'high' 
                            ? 'bg-red-100 text-red-600' 
                            : insight.level === 'medium'
                            ? 'bg-yellow-100 text-yellow-600'
                            : 'bg-blue-100 text-blue-600'
                        }`}>
                          <Info size={16} />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <Text className="font-medium text-primary">{insight.title}</Text>
                            <Badge 
                              color={insight.level === 'high' ? 'red' : insight.level === 'medium' ? 'orange' : 'blue'}
                              text={insight.level === 'high' ? '高' : insight.level === 'medium' ? '中' : '低'}
                            />
                          </div>
                          <Text className="text-sm text-secondary mt-1">
                            {insight.description}
                          </Text>
                          {insight.nodes && insight.nodes.length > 0 && (
                            <div className="mt-2">
                              <Text className="text-xs text-tertiary">相关节点：</Text>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {insight.nodes.map((nodeId: string) => {
                                  const node = nodes.find(n => n.id === nodeId);
                                  return node ? (
                                    <Tag 
                                      key={nodeId}

                                      className="cursor-pointer"
                                      onClick={() => handleNodeClick(nodeId)}
                                    >
                                      {node.name}
                                    </Tag>
                                  ) : null;
                                })}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )
            }
          ]}
        />
      </Card>
    </div>
  );
};