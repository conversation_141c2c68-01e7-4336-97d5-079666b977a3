import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows
import xlsxwriter
from pathlib import Path
import tempfile
import uuid
import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple
import aiofiles
import os
from datetime import datetime

from ..models.excel_models import (
    ExcelMergeConfig, 
    ExcelMergeResponse, 
    ExcelFileInfo, 
    ExcelSheetInfo,
    ExcelPreviewData,
    MergeMode
)

logger = logging.getLogger(__name__)


class ExcelService:
    """Excel处理服务"""
    
    def __init__(self):
        self.temp_dir = Path(tempfile.gettempdir()) / "excel_merger"
        self.temp_dir.mkdir(exist_ok=True)
    
    async def merge_excel_files(
        self, 
        files: List, 
        config: ExcelMergeConfig
    ) -> ExcelMergeResponse:
        """合并Excel文件"""
        task_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        try:
            # 保存上传的文件到临时目录
            temp_files = []
            for file in files:
                temp_file_path = self.temp_dir / f"{uuid.uuid4()}_{file.filename}"
                async with aiofiles.open(temp_file_path, 'wb') as f:
                    content = await file.read()
                    await f.write(content)
                temp_files.append(temp_file_path)
            
            # 根据合并模式执行不同的合并逻辑
            if config.merge_mode == MergeMode.MULTIPLE_FILES:
                output_path = await self._merge_multiple_files(temp_files, config)
                merged_files_count = len(temp_files)
                merged_sheets_count = len(temp_files)  # 每个文件取一个sheet
            else:  # MULTIPLE_SHEETS
                output_path = await self._merge_multiple_sheets(temp_files[0], config)
                merged_files_count = 1
                merged_sheets_count = len(config.sheet_indices) if config.sheet_indices else 1
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # 获取输出文件信息
            file_size = output_path.stat().st_size
            
            # 计算总行数
            total_rows = await self._count_rows_in_excel(output_path)
            
            # 生成输出文件名
            if config.output_filename:
                output_filename = config.output_filename
                if not output_filename.endswith('.xlsx'):
                    output_filename += '.xlsx'
            else:
                output_filename = f"merged_excel_{task_id[:8]}.xlsx"
            
            # 移动文件到最终位置
            final_output_path = self.temp_dir / output_filename
            output_path.rename(final_output_path)
            
            # 清理临时文件
            for temp_file in temp_files:
                try:
                    temp_file.unlink()
                except:
                    pass
            
            return ExcelMergeResponse(
                task_id=task_id,
                status="completed",
                output_filename=output_filename,
                output_file_id=str(final_output_path),
                merged_files_count=merged_files_count,
                merged_sheets_count=merged_sheets_count,
                total_rows=total_rows,
                processing_time=processing_time,
                file_size=file_size,
                download_url=f"/api/excel/download/{final_output_path.name}"
            )
            
        except Exception as e:
            logger.error(f"Excel合并失败: {str(e)}")
            # 清理临时文件
            for temp_file in temp_files:
                try:
                    temp_file.unlink()
                except:
                    pass
            raise e
    
    async def _merge_multiple_files(
        self, 
        file_paths: List[Path], 
        config: ExcelMergeConfig
    ) -> Path:
        """合并多个Excel文件"""
        output_path = self.temp_dir / f"merged_{uuid.uuid4()}.xlsx"
        
        # 使用openpyxl创建新的工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "合并数据"
        
        current_row = 1
        is_first_file = True
        
        for file_path in file_paths:
            logger.info(f"处理文件: {file_path}")
            
            # 读取Excel文件
            df = pd.read_excel(file_path, sheet_name=0)  # 读取第一个sheet
            
            # 应用行数配置
            if config.skip_header_rows > 0:
                df = df.iloc[config.skip_header_rows:]
            if config.skip_footer_rows > 0:
                df = df.iloc[:-config.skip_footer_rows]
            
            # 如果是第一个文件或者需要保留表头
            if is_first_file:
                # 写入表头
                for col_idx, column in enumerate(df.columns, 1):
                    ws.cell(row=current_row, column=col_idx, value=column)
                current_row += 1
                is_first_file = False
            
            # 写入数据
            for row_data in df.values:
                for col_idx, value in enumerate(row_data, 1):
                    ws.cell(row=current_row, column=col_idx, value=value)
                current_row += 1
        
        # 保存文件
        wb.save(output_path)
        return output_path
    
    async def _merge_multiple_sheets(
        self, 
        file_path: Path, 
        config: ExcelMergeConfig
    ) -> Path:
        """合并一个Excel文件的多个sheet"""
        output_path = self.temp_dir / f"merged_sheets_{uuid.uuid4()}.xlsx"
        
        # 读取Excel文件
        excel_file = pd.ExcelFile(file_path)
        
        # 确定要合并的sheet
        if config.sheet_indices:
            sheet_names = [excel_file.sheet_names[i] for i in config.sheet_indices 
                          if i < len(excel_file.sheet_names)]
        else:
            sheet_names = excel_file.sheet_names
        
        # 使用openpyxl创建新的工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "合并数据"
        
        current_row = 1
        is_first_sheet = True
        
        for sheet_name in sheet_names:
            logger.info(f"处理工作表: {sheet_name}")
            
            # 读取工作表
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            # 应用行数配置
            if config.skip_header_rows > 0:
                df = df.iloc[config.skip_header_rows:]
            if config.skip_footer_rows > 0:
                df = df.iloc[:-config.skip_footer_rows]
            
            # 如果是第一个sheet或者需要保留表头
            if is_first_sheet:
                # 写入表头
                for col_idx, column in enumerate(df.columns, 1):
                    ws.cell(row=current_row, column=col_idx, value=column)
                current_row += 1
                is_first_sheet = False
            
            # 写入数据
            for row_data in df.values:
                for col_idx, value in enumerate(row_data, 1):
                    ws.cell(row=current_row, column=col_idx, value=value)
                current_row += 1
        
        # 保存文件
        wb.save(output_path)
        return output_path
    
    async def get_excel_info(self, file_path: str) -> ExcelFileInfo:
        """获取Excel文件信息"""
        path = Path(file_path)
        
        # 读取Excel文件
        excel_file = pd.ExcelFile(path)
        
        # 计算总行数和列数
        total_rows = 0
        total_columns = 0
        
        for sheet_name in excel_file.sheet_names:
            df = pd.read_excel(path, sheet_name=sheet_name)
            total_rows += len(df)
            total_columns = max(total_columns, len(df.columns))
        
        return ExcelFileInfo(
            filename=path.name,
            sheet_count=len(excel_file.sheet_names),
            sheet_names=excel_file.sheet_names,
            total_rows=total_rows,
            total_columns=total_columns,
            file_size=path.stat().st_size
        )
    
    async def preview_excel_file(
        self, 
        file_path: str, 
        sheet_index: int = 0, 
        max_rows: int = 100
    ) -> ExcelPreviewData:
        """预览Excel文件内容"""
        path = Path(file_path)
        
        # 读取Excel文件
        excel_file = pd.ExcelFile(path)
        
        if sheet_index >= len(excel_file.sheet_names):
            raise ValueError(f"Sheet索引 {sheet_index} 超出范围")
        
        sheet_name = excel_file.sheet_names[sheet_index]
        df = pd.read_excel(path, sheet_name=sheet_name)
        
        # 限制预览行数
        preview_df = df.head(max_rows)
        
        return ExcelPreviewData(
            filename=path.name,
            sheet_name=sheet_name,
            sheet_index=sheet_index,
            headers=list(df.columns),
            data=preview_df.values.tolist(),
            total_rows=len(df),
            total_columns=len(df.columns),
            preview_rows=len(preview_df)
        )
    
    async def _count_rows_in_excel(self, file_path: Path) -> int:
        """计算Excel文件的总行数"""
        try:
            df = pd.read_excel(file_path)
            return len(df)
        except Exception as e:
            logger.warning(f"无法计算行数: {e}")
            return 0
