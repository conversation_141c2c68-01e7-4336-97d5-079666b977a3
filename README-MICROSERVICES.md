# IDEALAB 微服务

本文档介绍了 IDEALAB 项目中的后端微服务，以及如何独立启动它们进行开发和测试。

## 微服务概览

- **Auth Service**: 处理用户认证和授权。 **(已迁移至 PostgreSQL)**
- **Gateway Service**: API 网关，所有服务的入口。
- **Document Service**: 处理文档相关操作（OCR, 水印等）。
- **Financial Service**: 提供金融数据分析功能。
- **NLP Service**: 提供自然语言处理功能。

## 启动微服务 (独立模式)

### 准备工作: `.env` 文件

在启动任何服务前, 请在对应的服务目录 (e.g., `services/auth`) 下创建一个 `.env` 文件来配置环境变量。

### 1. Auth Service (使用 PostgreSQL)

1.  **安装依赖**:
    ```bash
    cd services/auth
    npm install
    ```

2.  **创建 `.env` 文件**: 在 `services/auth` 目录下创建 `.env` 文件，内容如下 (请替换为您的 PostgreSQL 连接信息):
    ```
    # 数据库连接, 格式: postgresql://USER:PASSWORD@HOST:PORT/DATABASE
    DATABASE_URL="postgresql://postgres:password@localhost:5432/idealab_auth"

    # JWT 配置
    JWT_SECRET="your-super-secret-jwt-key"
    JWT_REFRESH_SECRET="your-super-secret-refresh-key"
    ```

3.  **创建数据库和 Schema**:
    *   请确保您已在 PostgreSQL 中创建了名为 `idealab_auth` 的数据库。
    *   运行以下命令来创建数据表并填充初始数据：
    ```bash
    npx prisma migrate dev --name init
    ```
    *(此命令会自动运行 seed 脚本，创建管理员账户。)*
    
    *   如果您只想单独运行 seed 脚本：
    ```bash
    npx prisma db seed
    ```

4.  **启动服务**:
    ```bash
    npm start
    ```
    服务将运行在 `http://localhost:8001`。

### 2. Gateway Service

1.  **安装依赖**:
    ```bash
    cd services/gateway
    npm install
    ```
2.  **启动服务**:
    ```bash
    npm start
    ```
    服务将运行在 `http://localhost:3000`。

### 3. Python 服务 (通用指南)

`document`, `financial`, `nlp` 服务使用 Python 和 Poetry。

1.  **安装 Poetry**: 如果您没有安装，请参考 [Poetry 官方文档](https://python-poetry.org/docs/#installation)。
2.  **安装依赖**:
    ```bash
    # 以 financial-service 为例
    cd services/financial-service
    poetry install
    ```
3.  **创建 `.env` 文件**: 根据每个服务具体需要的环境变量创建。
4.  **启动服务**:
    ```bash
    # 以 financial-service 为例
    poetry run uvicorn main:app --host 0.0.0.0 --port 8003
    ```

#### 各 Python 服务端口



## 使用的数据库

- **PostgreSQL**: 用于 `auth-service`, `financial-service`, `nlp-service`。
- **Neo4j**: 用于 `financial-service`。
- **Redis**: 用于多个服务的缓存。
- **MinIO**: 用于 `document-service` 的对象存储。

## 项目结构

```
idealab-microservices/
├── frontend/                    # React前端应用
├── services/                   # 微服务目录
│   ├── gateway/               # API网关服务 (Node.js)
│   ├── auth/                  # 认证授权服务 (Node.js)
│   ├── nlp-service/           # NLP服务 (Python)
│   ├── financial-service/     # 金融数据服务 (Python)
│   └── document-service/      # 文档处理服务 (Python)
├── shared/                    # 共享代码和配置
│   ├── types/                # TypeScript类型定义
│   ├── configs/              # 配置文件
│   └── utils/                # 工具函数
├── infrastructure/           # 基础设施代码
│   ├── docker/              # Docker配置
│   ├── k8s/                 # Kubernetes配置
│   └── nginx/               # Nginx配置
└── docs/                    # 文档
```

## 微服务说明

### 1. API网关服务 (gateway)
- **技术栈**: Node.js + Express + nginx
- **职责**: 请求路由、负载均衡、API限流、统一认证
- **端口**: 8000

### 2. 认证授权服务 (auth)
- **技术栈**: Node.js + Express + JWT + Redis
- **职责**: 用户认证、权限管理、会话管理
- **端口**: 8001

### 3. NLP服务 (nlp-service)
- **技术栈**: Python + FastAPI + transformers + langchain
- **职责**: 
  - 对话机器人 (/api/nlp/chat)
  - 舆情分析 (/api/nlp/sentiment)
- **端口**: 8002

### 4. 金融数据服务 (financial-service)
- **技术栈**: Python + FastAPI + NetworkX + Neo4j
- **职责**:
  - 股权穿透分析 (/api/financial/equity-penetration)
  - 股权质押分析 (/api/financial/equity-pledge)
  - 债券市场分析 (/api/financial/bond-analysis)
- **端口**: 8003

### 5. 文档处理服务 (document-service)
- **技术栈**: Python + FastAPI + OpenCV + pandas + pytesseract
- **职责**:
  - 批量OCR (/api/document/ocr)
  - 批量加水印 (/api/document/watermark)
  - Excel文件合并 (/api/document/excel-merge)
- **端口**: 8004

## 数据库设计

- **PostgreSQL**: 用户数据、配置数据、元数据
- **Redis**: 缓存、会话存储
- **Neo4j**: 图数据库(股权关系)
- **MinIO/S3**: 文件存储

## 部署架构

```
[Load Balancer] 
    ↓
[API Gateway:8000]
    ↓
┌─────────────────────────────────────┐
│  [Auth:8001]  [NLP:8002]            │
│  [Financial:8003]  [Document:8004]  │
└─────────────────────────────────────┘
    ↓
┌─────────────────────────────────────┐
│  [PostgreSQL]  [Redis]  [Neo4j]     │
│  [MinIO]       [Frontend:3000]      │
└─────────────────────────────────────┘
```