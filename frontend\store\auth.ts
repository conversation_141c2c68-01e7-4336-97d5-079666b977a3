import { create } from 'zustand';
import { User } from '@/types';
import { api, ApiResponse } from '@/services/api';

interface LoginResponse {
  user: User;
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  passwordChangeRequired: boolean;
  login: (username: string, password: string, rememberMe?: boolean) => Promise<boolean>;
  logout: () => void;
  checkAuth: () => void;
  passwordChanged: () => void;
}

const useAuthStore = create<AuthState>((set) => ({
  user: null,
  token: null,
  isAuthenticated: false,
  passwordChangeRequired: false,
  
  login: async (username, password, rememberMe = false) => {
    try {
      const response = await api.post<ApiResponse<LoginResponse>>('/auth/login', { username, password });

      if (response.success && response.data) {
        const { user, tokens } = response.data;

        set({
          user,
          token: tokens.accessToken,
          isAuthenticated: true,
          passwordChangeRequired: (user as any).passwordChangeRequired ?? false
        });
        
        // 根据记住登录选择存储方式
        if (rememberMe) {
          localStorage.setItem('user', JSON.stringify(user));
          localStorage.setItem('token', tokens.accessToken);
        } else {
          sessionStorage.setItem('user', JSON.stringify(user));
          sessionStorage.setItem('token', tokens.accessToken);
        }
        
        return true;
      }
      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  },
  
  logout: () => {
    set({ user: null, token: null, isAuthenticated: false, passwordChangeRequired: false });
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    sessionStorage.removeItem('user');
    sessionStorage.removeItem('token');
    // Consider redirecting to login page here
    window.location.href = '/login';
  },

  checkAuth: () => {
    // 首先检查 localStorage (记住登录)
    let userString = localStorage.getItem('user');
    let token = localStorage.getItem('token');
    
    // 如果 localStorage 没有，检查 sessionStorage
    if (!userString || !token) {
      userString = sessionStorage.getItem('user');
      token = sessionStorage.getItem('token');
    }
    
    if (userString && token) {
      const user = JSON.parse(userString);
      set({ user, token, isAuthenticated: true, passwordChangeRequired: user.passwordChangeRequired });
    }
  },

  passwordChanged: () => {
    set((state) => {
      // Create a new user object to avoid direct mutation
      const updatedUser = state.user ? { ...state.user, passwordChangeRequired: false } : null;

      if (updatedUser) {
        localStorage.setItem('user', JSON.stringify(updatedUser));
      }

      return {
        user: updatedUser,
        passwordChangeRequired: false
      };
    });
  }
}));

export default useAuthStore;