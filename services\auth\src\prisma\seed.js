const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  const adminUsername = 'admin';
  const adminEmail = '<EMAIL>';
  const adminPassword = 'password'; // Default password, should be changed

  // 检查管理员用户是否已存在
  const adminExists = await prisma.user.findUnique({
    where: { username: adminUsername },
  });

  if (!adminExists) {
    console.log(`👤 Admin user not found, creating one...`);
    const hashedPassword = await bcrypt.hash(adminPassword, 10);
    
    await prisma.user.create({
      data: {
        username: adminUsername,
        email: adminEmail,
        password: hashedPassword,
        role: 'admin',
        passwordChangeRequired: true,
      },
    });
    console.log(`✅ Admin user created with username: ${adminUsername} and password: ${adminPassword}`);
  } else {
    console.log('ℹ️ Admin user already exists.');
  }

  console.log('🌱 Database seeding finished.');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 