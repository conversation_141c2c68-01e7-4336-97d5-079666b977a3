#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进度日志管理器
- 过滤和格式化爬虫日志，提供用户友好的进度信息
- 分离系统调试日志和用户展示日志
"""
import logging
import re
from typing import Dict, List, Optional, Callable
from enum import Enum
from datetime import datetime

class LogLevel(Enum):
    """用户日志级别"""
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    PROGRESS = "progress"

class ProgressLogger:
    """进度日志管理器"""
    
    def __init__(self, task_id: str, callback: Optional[Callable] = None):
        self.task_id = task_id
        self.callback = callback
        self.current_step = 0
        self.total_steps = 0
        self.current_phase = "初始化"
        self.processed_companies = 0
        self.user_logs: List[Dict] = []
        
        # 配置系统日志记录器
        self.system_logger = logging.getLogger(f"crawler.{task_id}")
        
    def set_total_steps(self, total: int):
        """设置总步数"""
        self.total_steps = total
        
    def set_current_phase(self, phase: str):
        """设置当前阶段"""
        self.current_phase = phase
        self._send_user_log(LogLevel.INFO, f"进入阶段: {phase}")
        
    def log_company_start(self, company_name: str, depth: int):
        """记录开始处理公司"""
        self.processed_companies += 1
        message = f"开始分析: {company_name} (深度: {depth})"
        self._send_user_log(LogLevel.PROGRESS, message)
        
        # 系统日志（详细信息）
        self.system_logger.info(f"Starting crawl for {company_name} at depth {depth}")
        
    def log_company_complete(self, company_name: str, shareholders_count: int, investments_count: int):
        """记录公司处理完成"""
        message = f"✅ {company_name}: 发现 {shareholders_count} 个股东, {investments_count} 项投资"
        self._send_user_log(LogLevel.SUCCESS, message)
        
        self.current_step += 1
        self._update_progress()
        
    def log_company_skip(self, company_name: str, reason: str):
        """记录跳过公司"""
        user_reason = self._translate_skip_reason(reason)
        message = f"⏭️ 跳过 {company_name}: {user_reason}"
        self._send_user_log(LogLevel.INFO, message)
        
        # 系统日志（详细原因）
        self.system_logger.info(f"Skipped {company_name}: {reason}")
        
    def log_error(self, company_name: str, error: str):
        """记录错误（过滤敏感信息）"""
        user_error = self._sanitize_error(error)
        message = f"❌ {company_name}: {user_error}"
        self._send_user_log(LogLevel.ERROR, message)
        
        # 系统日志（完整错误信息）
        self.system_logger.error(f"Error processing {company_name}: {error}")
        
    def log_network_delay(self, delay_seconds: float):
        """记录网络延迟（用户友好）"""
        if delay_seconds > 10:
            message = f"⏳ 网络请求较慢，请耐心等待... ({int(delay_seconds)}s)"
            self._send_user_log(LogLevel.WARNING, message)
        
    def log_anti_spider_detected(self):
        """记录检测到反爬虫"""
        message = "🛡️ 检测到访问限制，正在智能处理..."
        self._send_user_log(LogLevel.WARNING, message)
        
    def log_phase_complete(self, phase: str, duration: float):
        """记录阶段完成"""
        message = f"✅ {phase} 完成 (用时: {duration:.1f}秒)"
        self._send_user_log(LogLevel.SUCCESS, message)
        
    def log_final_stats(self, total_companies: int, total_relationships: int, duration: float):
        """记录最终统计"""
        message = f"🎉 分析完成! 共处理 {total_companies} 家企业，发现 {total_relationships} 个关系 (总用时: {duration:.1f}秒)"
        self._send_user_log(LogLevel.SUCCESS, message)
        
    def _translate_skip_reason(self, reason: str) -> str:
        """将系统跳过原因翻译为用户友好的描述"""
        reason_map = {
            "already_processed": "已处理过",
            "max_depth_reached": "已达最大深度",
            "invalid_url": "链接无效",
            "no_company_info": "未找到企业信息",
            "cache_hit": "使用缓存数据",
            "duplicate_node": "重复节点"
        }
        return reason_map.get(reason, "未知原因")
        
    def _sanitize_error(self, error: str) -> str:
        """清理错误信息，移除技术细节"""
        # 移除文件路径
        error = re.sub(r'File "[^"]*"', 'File "..."', error)
        # 移除行号信息
        error = re.sub(r', line \d+', '', error)
        # 移除具体的URL参数
        error = re.sub(r'https?://[^\s]+', '[URL]', error)
        
        # 将常见技术错误转换为用户友好的描述
        error_translations = {
            "ConnectionError": "网络连接失败",
            "TimeoutException": "请求超时",
            "NoSuchElementException": "页面元素未找到",
            "WebDriverException": "浏览器异常",
            "HTTPError": "服务器响应异常"
        }
        
        for tech_error, user_error in error_translations.items():
            if tech_error in error:
                return user_error
                
        # 如果错误太长，截取前100个字符
        if len(error) > 100:
            return error[:100] + "..."
            
        return error
        
    def _send_user_log(self, level, message: str):
        """发送用户日志"""
        # 处理level参数，支持字符串和枚举
        if hasattr(level, 'value'):
            level_str = level.value
        else:
            level_str = str(level)

        log_entry = {
            "type": "log_message",
            "timestamp": datetime.now().isoformat(),
            "level": level_str,
            "message": message,
            "phase": self.current_phase,
            "progress": self._calculate_progress(),
            "task_id": self.task_id
        }
        
        self.user_logs.append(log_entry)
        
        # 如果有回调函数，立即发送
        if self.callback:
            self.callback(log_entry)
            
    def _calculate_progress(self) -> int:
        """计算当前进度百分比"""
        if self.total_steps == 0:
            return 0
        return min(int((self.current_step / self.total_steps) * 100), 100)
        
    def _update_progress(self):
        """更新进度"""
        progress = self._calculate_progress()
        if self.callback:
            self.callback({
                "type": "progress_update",
                "progress": progress,
                "current_step": self.current_step,
                "total_steps": self.total_steps,
                "processed_companies": self.processed_companies,
                "task_id": self.task_id
            })
            
    def get_user_logs(self) -> List[Dict]:
        """获取所有用户日志"""
        return self.user_logs
        
    def get_system_logger(self) -> logging.Logger:
        """获取系统日志记录器"""
        return self.system_logger

class CrawlProgressFilter:
    """爬虫进度过滤器 - 用于过滤现有爬虫的stdout输出"""
    
    def __init__(self, progress_logger: ProgressLogger):
        self.progress_logger = progress_logger
        
    def filter_crawler_output(self, output_line: str) -> bool:
        """
        过滤爬虫输出行，提取有用信息并转换为用户友好格式
        返回True表示这行需要显示给用户
        """
        # 过滤掉的内容（不显示给用户）
        skip_patterns = [
            r"DEBUG",
            r"webdriver",
            r"selenium",
            r"HTTP\s+\d+",
            r"urllib3",
            r"正在设置ChromeDriver",
            r"ChromeDriverManager",
            r"screenshot_",
            r"File.*line\s+\d+",
            r"Traceback"
        ]
        
        for pattern in skip_patterns:
            if re.search(pattern, output_line, re.IGNORECASE):
                return False
                
        # 提取有用信息并转换
        if "成功获取网页" in output_line:
            self.progress_logger.log_network_delay(0)  # 网络正常
            return False  # 不直接显示技术信息
            
        elif "找到.*行股东数据" in output_line:
            match = re.search(r"找到\s+(\d+)\s+行股东数据", output_line)
            if match:
                count = match.group(1)
                return False  # 会在company_complete中统一显示
                
        elif "验证码" in output_line or "访问限制" in output_line:
            self.progress_logger.log_anti_spider_detected()
            return False
            
        elif "暂停" in output_line:
            return False  # 内部暂停不需要告知用户
            
        # 其他信息可以显示
        return True