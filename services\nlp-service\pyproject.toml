[tool.poetry]
name = "idealab-nlp-service"
version = "1.0.0"
description = "IDEALAB NLP Service for Chatbot and Sentiment Analysis"
authors = ["IDEALAB Team <<EMAIL>>"]
license = "MIT"
package-mode = false

[tool.poetry.dependencies]
python = "^3.9"
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
pydantic = "^2.5.0"
transformers = "^4.36.0"
torch = "^2.1.0"
langchain = "^0.0.340"
openai = "^1.3.0"
requests = "^2.31.0"
python-multipart = "^0.0.6"
python-dotenv = "^1.0.0"
redis = "^5.0.1"
sqlalchemy = "^2.0.23"
psycopg2-binary = "^2.9.9"
alembic = "^1.13.0"
jieba = "^0.42.1"
numpy = "^1.24.0"
pandas = "^2.1.0"
scikit-learn = "^1.3.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
black = "^23.0.0"
flake8 = "^6.0.0"
mypy = "^1.7.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"