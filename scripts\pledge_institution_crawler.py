#!/usr/bin/env python3
"""
质押机构分布统计数据爬取脚本
功能：
1. 从AKShare获取证券公司和银行的质押机构分布统计数据
2. 智能检测数据变动，只在有变化时更新数据库
3. 记录更新时间和数据版本

接口：
- stock_gpzy_distribute_statistics_company_em (证券公司)
- stock_gpzy_distribute_statistics_bank_em (银行)

作者：IDEALAB团队
创建时间：2024-01-15
"""

import os
import sys
import logging
import pandas as pd
import psycopg2
from psycopg2.extras import execute_values
import akshare as ak
import time
import hashlib
import json
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)


class PledgeInstitutionCrawler:
    """质押机构分布统计数据爬取器"""
    
    def __init__(self):
        """初始化"""
        self.connection = None
        self.cursor = None
        self.db_config = self._get_db_config()
    
    def _get_db_config(self):
        """获取数据库配置"""
        config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 5432)),
            'database': os.getenv('DB_NAME', 'idealab'),
            'user': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', '123456')
        }
        
        if not all([config['host'], config['database'], config['user'], config['password']]):
            raise ValueError("数据库配置不完整，请检查环境变量")
        
        return config
    
    def connect_db(self):
        """连接数据库"""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_db(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("数据库连接已关闭")
    
    def create_tables(self):
        """创建数据表"""
        try:
            # 创建证券公司质押统计表
            company_table_sql = """
            CREATE TABLE IF NOT EXISTS pledge_institution_company (
                id SERIAL PRIMARY KEY,
                sequence_number INTEGER,
                institution_name VARCHAR(200) NOT NULL,
                pledge_company_count INTEGER,
                pledge_transaction_count INTEGER,
                pledge_quantity BIGINT,
                below_warning_ratio DECIMAL(10, 4),
                warning_to_liquidation_ratio DECIMAL(10, 4),
                above_liquidation_ratio DECIMAL(10, 4),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            
            # 创建银行质押统计表
            bank_table_sql = """
            CREATE TABLE IF NOT EXISTS pledge_institution_bank (
                id SERIAL PRIMARY KEY,
                sequence_number INTEGER,
                institution_name VARCHAR(200) NOT NULL,
                pledge_company_count INTEGER,
                pledge_transaction_count INTEGER,
                pledge_quantity BIGINT,
                below_warning_ratio DECIMAL(10, 4),
                warning_to_liquidation_ratio DECIMAL(10, 4),
                above_liquidation_ratio DECIMAL(10, 4),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            
            # 创建数据版本控制表
            version_table_sql = """
            CREATE TABLE IF NOT EXISTS pledge_institution_version (
                id SERIAL PRIMARY KEY,
                table_name VARCHAR(50) NOT NULL,
                data_hash VARCHAR(64) NOT NULL,
                record_count INTEGER,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(table_name)
            );
            """
            
            self.cursor.execute(company_table_sql)
            self.cursor.execute(bank_table_sql)
            self.cursor.execute(version_table_sql)
            
            # 创建索引
            index_sql = """
            CREATE INDEX IF NOT EXISTS idx_company_institution ON pledge_institution_company(institution_name);
            CREATE INDEX IF NOT EXISTS idx_bank_institution ON pledge_institution_bank(institution_name);
            CREATE INDEX IF NOT EXISTS idx_company_updated ON pledge_institution_company(updated_at);
            CREATE INDEX IF NOT EXISTS idx_bank_updated ON pledge_institution_bank(updated_at);
            """
            
            self.cursor.execute(index_sql)
            self.connection.commit()
            logger.info("数据表创建/检查完成")
            
        except Exception as e:
            logger.error(f"创建数据表失败: {e}")
            self.connection.rollback()
            raise
    
    def fetch_company_data(self):
        """获取证券公司数据"""
        try:
            logger.info("获取证券公司质押统计数据...")
            start_time = time.time()
            
            df = ak.stock_gpzy_distribute_statistics_company_em()
            
            end_time = time.time()
            logger.info(f"证券公司数据获取完成，耗时: {end_time - start_time:.2f}秒，获取 {len(df)} 条记录")
            
            return df
        except Exception as e:
            logger.error(f"获取证券公司数据失败: {e}")
            return None
    
    def fetch_bank_data(self):
        """获取银行数据"""
        try:
            logger.info("获取银行质押统计数据...")
            start_time = time.time()
            
            df = ak.stock_gpzy_distribute_statistics_bank_em()
            
            end_time = time.time()
            logger.info(f"银行数据获取完成，耗时: {end_time - start_time:.2f}秒，获取 {len(df)} 条记录")
            
            return df
        except Exception as e:
            logger.error(f"获取银行数据失败: {e}")
            return None
    
    def clean_data(self, df, data_type):
        """清洗数据"""
        logger.info(f"开始清洗{data_type}数据...")
        
        # 重命名列
        column_mapping = {
            '序号': 'sequence_number',
            '质押机构': 'institution_name',
            '质押公司数量': 'pledge_company_count',
            '质押笔数': 'pledge_transaction_count',
            '质押数量': 'pledge_quantity',
            '未达预警线比例': 'below_warning_ratio',
            '达到预警线未达平仓线比例': 'warning_to_liquidation_ratio',
            '达到平仓线比例': 'above_liquidation_ratio'
        }
        
        df = df.rename(columns=column_mapping)
        
        try:
            # 数值字段处理
            numeric_cols = [
                'sequence_number', 'pledge_company_count', 'pledge_transaction_count',
                'pledge_quantity', 'below_warning_ratio', 'warning_to_liquidation_ratio',
                'above_liquidation_ratio'
            ]
            
            for col in numeric_cols:
                if col in ['below_warning_ratio', 'warning_to_liquidation_ratio', 'above_liquidation_ratio']:
                    # 比例字段转换为百分比
                    df[col] = pd.to_numeric(df[col], errors='coerce') * 100
                else:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 文本字段处理
            df['institution_name'] = df['institution_name'].astype(str).str.strip()
            
            # 移除关键字段为空的记录
            initial_count = len(df)
            df = df.dropna(subset=['institution_name'])
            final_count = len(df)
            
            if initial_count != final_count:
                logger.warning(f"{data_type}数据清洗：原始 {initial_count} 条 -> 有效 {final_count} 条")
            
            logger.info(f"{data_type}数据清洗完成，有效记录: {final_count} 条")
            
        except Exception as e:
            logger.error(f"{data_type}数据清洗失败: {e}")
            raise
        
        return df
    
    def calculate_data_hash(self, df):
        """计算数据哈希值用于变动检测"""
        try:
            # 将DataFrame转换为字符串并计算MD5哈希
            data_str = df.to_string(index=False)
            return hashlib.md5(data_str.encode('utf-8')).hexdigest()
        except Exception as e:
            logger.error(f"计算数据哈希失败: {e}")
            return None
    
    def get_last_data_hash(self, table_name):
        """获取上次数据的哈希值"""
        try:
            self.cursor.execute(
                "SELECT data_hash, record_count, last_updated FROM pledge_institution_version WHERE table_name = %s",
                (table_name,)
            )
            result = self.cursor.fetchone()
            return result if result else (None, 0, None)
        except Exception as e:
            logger.error(f"获取数据版本信息失败: {e}")
            return None, 0, None
    
    def update_data_version(self, table_name, data_hash, record_count):
        """更新数据版本信息"""
        try:
            self.cursor.execute("""
                INSERT INTO pledge_institution_version (table_name, data_hash, record_count, last_updated)
                VALUES (%s, %s, %s, %s)
                ON CONFLICT (table_name)
                DO UPDATE SET
                    data_hash = EXCLUDED.data_hash,
                    record_count = EXCLUDED.record_count,
                    last_updated = EXCLUDED.last_updated
            """, (table_name, data_hash, record_count, datetime.now()))
            
        except Exception as e:
            logger.error(f"更新数据版本信息失败: {e}")
            raise

    def insert_data(self, df, table_name, data_type):
        """插入数据到指定表"""
        try:
            logger.info(f"开始插入{data_type}数据...")

            # 清空现有数据
            self.cursor.execute(f"TRUNCATE TABLE {table_name} RESTART IDENTITY")

            insert_sql = f"""
            INSERT INTO {table_name} (
                sequence_number, institution_name, pledge_company_count,
                pledge_transaction_count, pledge_quantity, below_warning_ratio,
                warning_to_liquidation_ratio, above_liquidation_ratio
            ) VALUES %s
            """

            # 准备数据
            data_tuples = []
            for _, row in df.iterrows():
                data_tuple = (
                    int(row['sequence_number']) if pd.notna(row['sequence_number']) else None,
                    row['institution_name'],
                    int(row['pledge_company_count']) if pd.notna(row['pledge_company_count']) else None,
                    int(row['pledge_transaction_count']) if pd.notna(row['pledge_transaction_count']) else None,
                    int(row['pledge_quantity']) if pd.notna(row['pledge_quantity']) else None,
                    float(row['below_warning_ratio']) if pd.notna(row['below_warning_ratio']) else None,
                    float(row['warning_to_liquidation_ratio']) if pd.notna(row['warning_to_liquidation_ratio']) else None,
                    float(row['above_liquidation_ratio']) if pd.notna(row['above_liquidation_ratio']) else None
                )
                data_tuples.append(data_tuple)

            # 批量插入
            execute_values(self.cursor, insert_sql, data_tuples, page_size=100)

            logger.info(f"成功插入{data_type}数据 {len(data_tuples)} 条记录")
            return True

        except Exception as e:
            logger.error(f"{data_type}数据插入失败: {e}")
            return False

    def process_data_type(self, data_type):
        """处理指定类型的数据"""
        try:
            # 根据数据类型选择获取函数和表名
            if data_type == "证券公司":
                df = self.fetch_company_data()
                table_name = "pledge_institution_company"
            elif data_type == "银行":
                df = self.fetch_bank_data()
                table_name = "pledge_institution_bank"
            else:
                logger.error(f"不支持的数据类型: {data_type}")
                return False

            if df is None or df.empty:
                logger.error(f"未获取到{data_type}数据")
                return False

            # 清洗数据
            df_clean = self.clean_data(df, data_type)

            # 计算数据哈希
            current_hash = self.calculate_data_hash(df_clean)
            if not current_hash:
                logger.error(f"计算{data_type}数据哈希失败")
                return False

            # 检查数据是否有变动
            last_hash, last_count, last_updated = self.get_last_data_hash(table_name)

            if last_hash == current_hash:
                logger.info(f"{data_type}数据无变动，跳过更新")
                logger.info(f"  当前记录数: {len(df_clean)}")
                logger.info(f"  上次更新: {last_updated}")
                return True

            logger.info(f"{data_type}数据有变动，开始更新")
            logger.info(f"  新记录数: {len(df_clean)}")
            logger.info(f"  旧记录数: {last_count}")
            logger.info(f"  上次更新: {last_updated}")

            # 插入数据
            success = self.insert_data(df_clean, table_name, data_type)

            if success:
                # 更新版本信息
                self.update_data_version(table_name, current_hash, len(df_clean))
                self.connection.commit()
                logger.info(f"{data_type}数据更新完成")
            else:
                self.connection.rollback()
                logger.error(f"{data_type}数据更新失败")

            return success

        except Exception as e:
            logger.error(f"处理{data_type}数据失败: {e}")
            self.connection.rollback()
            return False

    def get_stats(self):
        """获取统计信息"""
        try:
            # 证券公司统计
            self.cursor.execute("""
                SELECT
                    COUNT(*) as total_records,
                    SUM(pledge_company_count) as total_companies,
                    SUM(pledge_transaction_count) as total_transactions,
                    MAX(updated_at) as last_updated
                FROM pledge_institution_company
            """)
            company_stats = self.cursor.fetchone()

            # 银行统计
            self.cursor.execute("""
                SELECT
                    COUNT(*) as total_records,
                    SUM(pledge_company_count) as total_companies,
                    SUM(pledge_transaction_count) as total_transactions,
                    MAX(updated_at) as last_updated
                FROM pledge_institution_bank
            """)
            bank_stats = self.cursor.fetchone()

            logger.info("=== 数据库统计信息 ===")
            if company_stats:
                logger.info(f"证券公司: {company_stats[0]}家机构, {company_stats[1]}家质押公司, {company_stats[2]}笔交易")
            if bank_stats:
                logger.info(f"银行: {bank_stats[0]}家机构, {bank_stats[1]}家质押公司, {bank_stats[2]}笔交易")

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")

    def run(self):
        """执行完整流程"""
        start_time = time.time()

        try:
            logger.info("=== 质押机构分布统计数据爬取开始 ===")

            # 1. 连接数据库
            if not self.connect_db():
                return False

            # 2. 创建表
            self.create_tables()

            # 3. 处理证券公司数据
            company_success = self.process_data_type("证券公司")

            # 4. 处理银行数据
            bank_success = self.process_data_type("银行")

            # 5. 统计信息
            if company_success or bank_success:
                self.get_stats()

            end_time = time.time()
            logger.info(f"=== 执行完成，总耗时: {end_time - start_time:.2f}秒 ===")

            return company_success and bank_success

        except Exception as e:
            logger.error(f"执行失败: {e}")
            return False
        finally:
            self.close_db()


def main():
    """主函数"""
    try:
        crawler = PledgeInstitutionCrawler()
        success = crawler.run()

        if success:
            logger.info("质押机构分布统计数据更新成功")
            sys.exit(0)
        else:
            logger.error("质押机构分布统计数据更新失败")
            sys.exit(1)

    except Exception as e:
        logger.error(f"程序执行异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
