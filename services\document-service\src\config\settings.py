import os
from typing import List
from pydantic import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 服务配置
    HOST: str = "0.0.0.0"
    PORT: int = 8003
    DEBUG: bool = True
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173"
    ]
    
    # 文件上传配置
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    ALLOWED_EXTENSIONS: List[str] = [".xlsx", ".xls", ".pdf", ".jpg", ".jpeg", ".png"]
    
    # 存储配置
    STORAGE_PATH: str = "/tmp/document_storage"
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379"
    
    # MinIO配置
    MINIO_ENDPOINT: str = "localhost:9000"
    MINIO_ACCESS_KEY: str = "minioadmin"
    MINIO_SECRET_KEY: str = "minioadmin"
    MINIO_BUCKET: str = "documents"
    
    class Config:
        env_file = ".env"


settings = Settings()
