const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const winston = require('winston');
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/user');

const app = express();
const prisma = new PrismaClient();
const PORT = process.env.PORT || 8001;

// 日志配置
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'logs/auth.log' })
  ]
});

// 中间件
app.use(helmet());
app.use(cors());
app.use(express.json());

// 健康检查
app.get('/health', async (req, res) => {
  try {
    // 检查数据库连接
    await prisma.$queryRaw`SELECT 1`;
    res.json({
      service: 'auth-service',
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: 'connected'
    });
  } catch (e) {
    logger.error('Health check failed:', e);
    res.status(503).json({
      service: 'auth-service',
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      database: 'disconnected'
    });
  }
});

// 路由
app.use('/auth', authRoutes);
app.use('/user', userRoutes);

// 错误处理
app.use((error, req, res, next) => {
  logger.error('Auth Service Error:', error);
  res.status(500).json({
    error: 'Internal Auth Service Error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Authentication failed'
  });
});

// 启动服务
app.listen(PORT, () => {
  logger.info(`🔐 Auth Service running on port ${PORT}`);
});