import React, { useState, useEffect, useCallback } from 'react';
import {
  Layout, Typography, Button, Statistic, message, Input, Select,
  Table, Card, Row, Col, Progress, Tag, Tooltip, DatePicker,
  Tabs, Space, Divider, Alert, Spin, Empty, Badge
} from 'antd';
import {
  TrendingUp, Shield, AlertTriangle, BarChart, Search,
  Building2, Users, Calendar, DollarSign, Activity,
  FileText, Download, Filter, RefreshCw, Info
} from 'lucide-react';
import { GlassmorphicCard } from '@/components/GlassmorphicCard';
import { financialApi, PledgeAnalysisResponse, PledgeMarketProfile, CompanyPledgeRatio, PledgeDetail, InstitutionStats } from '@/services';
import type { Dayjs } from 'dayjs';
import '@/styles/equity-pledge-tool.css';

const { Sider, Content } = Layout;
const { Title, Text, Paragraph } = Typography;
const { Search: AntSearch } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

export const EquityPledgeTool: React.FC = () => {
    // 状态管理
    const [loading, setLoading] = useState(false);
    const [activeTab, setActiveTab] = useState('analysis');
    const [searchKeyword, setSearchKeyword] = useState('');
    const [selectedCompany, setSelectedCompany] = useState<string>('');
    const [dateRange, setDateRange] = useState<[Dayjs, Dayjs] | null>(null);

    // 数据状态
    const [analysisResult, setAnalysisResult] = useState<PledgeAnalysisResponse | null>(null);
    const [marketProfile, setMarketProfile] = useState<PledgeMarketProfile | null>(null);
    const [companyRatios, setCompanyRatios] = useState<CompanyPledgeRatio[]>([]);
    const [pledgeDetails, setPledgeDetails] = useState<PledgeDetail[]>([]);
    const [institutionStats, setInstitutionStats] = useState<{
        securities: InstitutionStats[];
        banks: InstitutionStats[];
    }>({ securities: [], banks: [] });
    const [companySuggestions, setCompanySuggestions] = useState<any[]>([]);

    // 加载市场概况数据
    const loadMarketProfile = useCallback(async () => {
        try {
            const response = await financialApi.getPledgeMarketProfile();
            if (response.success && response.data) {
                setMarketProfile(response.data);
            }
        } catch (error) {
            console.error('Failed to load market profile:', error);
        }
    }, []);

    // 加载公司质押比例数据
    const loadCompanyRatios = useCallback(async () => {
        try {
            const response = await financialApi.getCompanyPledgeRatio({
                page: 1,
                size: 50
            });
            if (response.success && response.data) {
                setCompanyRatios(response.data);
            }
        } catch (error) {
            console.error('Failed to load company ratios:', error);
        }
    }, []);

    // 加载机构统计数据
    const loadInstitutionStats = useCallback(async () => {
        try {
            const [securitiesResponse, banksResponse] = await Promise.all([
                financialApi.getPledgeInstitutionStats('securities'),
                financialApi.getPledgeInstitutionStats('bank')
            ]);

            setInstitutionStats({
                securities: securitiesResponse.success ? securitiesResponse.data || [] : [],
                banks: banksResponse.success ? banksResponse.data || [] : []
            });
        } catch (error) {
            console.error('Failed to load institution stats:', error);
        }
    }, []);

    // 搜索公司
    const handleSearchCompanies = useCallback(async (keyword: string) => {
        if (!keyword.trim()) {
            setCompanySuggestions([]);
            return;
        }

        try {
            const response = await financialApi.searchPledgeCompanies(keyword);
            if (response.success && response.data?.suggestions) {
                setCompanySuggestions(response.data.suggestions);
            }
        } catch (error) {
            console.error('Failed to search companies:', error);
        }
    }, []);

    // 分析公司质押情况
    const handleAnalyzeCompany = useCallback(async () => {
        if (!selectedCompany) {
            message.warning('请先选择要分析的公司');
            return;
        }

        setLoading(true);
        try {
            const request = {
                company_code: selectedCompany,
                include_subsidiaries: true,
                date_range: dateRange ? {
                    start_date: dateRange[0].format('YYYY-MM-DD'),
                    end_date: dateRange[1].format('YYYY-MM-DD')
                } : undefined
            };

            const response = await financialApi.equityPledge(request);
            if (response.success && response.data) {
                setAnalysisResult(response.data);
                setActiveTab('analysis');
                message.success('分析完成');
            } else {
                message.error(response.message || '分析失败');
            }
        } catch (error) {
            console.error('Analysis failed:', error);
            message.error('分析失败，请稍后重试');
        } finally {
            setLoading(false);
        }
    }, [selectedCompany, dateRange]);

    // 初始化数据
    useEffect(() => {
        loadMarketProfile();
        loadCompanyRatios();
        loadInstitutionStats();
    }, [loadMarketProfile, loadCompanyRatios, loadInstitutionStats]);

    // 辅助函数
    const getRiskColor = (riskLevel: string) => {
        switch (riskLevel) {
            case 'low': return '#52c41a';
            case 'medium': return '#faad14';
            case 'high': return '#ff7875';
            case 'critical': return '#ff4d4f';
            default: return '#d9d9d9';
        }
    };

    const getRiskText = (riskLevel: string) => {
        switch (riskLevel) {
            case 'low': return '低风险';
            case 'medium': return '中等风险';
            case 'high': return '高风险';
            case 'critical': return '极高风险';
            default: return '未知';
        }
    };

    const formatNumber = (num: number, decimals: number = 2) => {
        if (num >= 1e8) return `${(num / 1e8).toFixed(decimals)}亿`;
        if (num >= 1e4) return `${(num / 1e4).toFixed(decimals)}万`;
        return num.toFixed(decimals);
    };

    const formatPercent = (num: number) => `${num.toFixed(2)}%`;

    // 渲染控制面板
    const renderControlPanel = () => (
        <GlassmorphicCard title={<Title level={5} className="!text-white">控制面板</Title>}>
            <Space direction="vertical" style={{ width: '100%' }} size="large">
                {/* 市场概况统计 */}
                {marketProfile && (
                    <div>
                        <Title level={5} className="!text-white" style={{ marginBottom: '16px' }}>
                            市场概况
                        </Title>
                        <Row gutter={[8, 8]}>
                            <Col span={12}>
                                <Statistic
                                    title={<Text className="!text-gray-300">质押公司</Text>}
                                    value={marketProfile.pledged_companies}
                                    suffix={`/${marketProfile.total_companies}`}
                                    prefix={<Building2 size={14} />}
                                    valueStyle={{ color: '#fff', fontSize: '14px' }}
                                />
                            </Col>
                            <Col span={12}>
                                <Statistic
                                    title={<Text className="!text-gray-300">平均质押率</Text>}
                                    value={marketProfile.average_pledge_ratio}
                                    suffix="%"
                                    prefix={<BarChart size={14} />}
                                    valueStyle={{ color: '#fff', fontSize: '14px' }}
                                />
                            </Col>
                        </Row>
                    </div>
                )}

                <Divider style={{ borderColor: 'rgba(255,255,255,0.1)', margin: '16px 0' }} />

                {/* 搜索和分析 */}
                <div>
                    <Title level={5} className="!text-white" style={{ marginBottom: '16px' }}>
                        公司分析
                    </Title>

                    <AntSearch
                        placeholder="搜索公司名称或代码"
                        value={searchKeyword}
                        onChange={(e) => {
                            setSearchKeyword(e.target.value);
                            handleSearchCompanies(e.target.value);
                        }}
                        style={{ marginBottom: '12px' }}
                        allowClear
                    />

                    {companySuggestions.length > 0 && (
                        <Select
                            placeholder="选择公司"
                            style={{ width: '100%', marginBottom: '12px' }}
                            value={selectedCompany}
                            onChange={setSelectedCompany}
                            showSearch
                            filterOption={false}
                        >
                            {companySuggestions.map((company) => (
                                <Option key={company.code} value={company.code}>
                                    {company.name} ({company.code})
                                </Option>
                            ))}
                        </Select>
                    )}

                    <RangePicker
                        style={{ width: '100%', marginBottom: '12px' }}
                        value={dateRange}
                        onChange={setDateRange}
                        placeholder={['开始日期', '结束日期']}
                    />

                    <Button
                        type="primary"
                        icon={<Search size={16} />}
                        onClick={handleAnalyzeCompany}
                        loading={loading}
                        style={{ width: '100%', marginBottom: '8px' }}
                        disabled={!selectedCompany}
                    >
                        分析质押情况
                    </Button>

                    <Button
                        icon={<RefreshCw size={16} />}
                        onClick={() => {
                            loadMarketProfile();
                            loadCompanyRatios();
                            loadInstitutionStats();
                        }}
                        style={{ width: '100%' }}
                    >
                        刷新数据
                    </Button>
                </div>
            </Space>
        </GlassmorphicCard>
    );

    // 渲染分析结果
    const renderAnalysisResult = () => {
        if (!analysisResult) {
            return (
                <div style={{
                    padding: '48px',
                    textAlign: 'center',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                    <Shield size={64} style={{ color: '#22d3ee', marginBottom: '24px' }} />
                    <Title level={3} className="!text-white" style={{ marginBottom: '16px' }}>
                        股权质押分析工具
                    </Title>
                    <Paragraph style={{
                        color: 'rgba(255, 255, 255, 0.7)',
                        fontSize: '16px',
                        maxWidth: '400px',
                        lineHeight: 1.6
                    }}>
                        请在左侧搜索并选择公司，开始进行股权质押分析
                    </Paragraph>
                </div>
            );
        }

        const { company, pledge_records, total_pledged_ratio, active_pledges_count, risk_assessment, trend_analysis } = analysisResult;

        return (
            <div style={{ padding: '24px', height: '100%', overflow: 'auto' }}>
                {/* 公司基本信息 */}
                <Card
                    style={{
                        marginBottom: '24px',
                        background: 'rgba(255, 255, 255, 0.05)',
                        border: '1px solid rgba(255, 255, 255, 0.1)'
                    }}
                >
                    <Row gutter={[24, 16]}>
                        <Col span={24}>
                            <Title level={4} className="!text-white" style={{ marginBottom: '16px' }}>
                                <Building2 size={20} style={{ marginRight: '8px' }} />
                                {company.name} ({company.code})
                            </Title>
                        </Col>
                        <Col span={6}>
                            <Statistic
                                title={<Text className="!text-gray-300">总质押比例</Text>}
                                value={total_pledged_ratio}
                                suffix="%"
                                valueStyle={{ color: getRiskColor(risk_assessment.risk_level) }}
                            />
                        </Col>
                        <Col span={6}>
                            <Statistic
                                title={<Text className="!text-gray-300">活跃质押数</Text>}
                                value={active_pledges_count}
                                valueStyle={{ color: '#fff' }}
                            />
                        </Col>
                        <Col span={6}>
                            <Statistic
                                title={<Text className="!text-gray-300">风险等级</Text>}
                                value={getRiskText(risk_assessment.risk_level)}
                                valueStyle={{ color: getRiskColor(risk_assessment.risk_level) }}
                            />
                        </Col>
                        <Col span={6}>
                            <Statistic
                                title={<Text className="!text-gray-300">风险评分</Text>}
                                value={risk_assessment.risk_score}
                                suffix="/100"
                                valueStyle={{ color: getRiskColor(risk_assessment.risk_level) }}
                            />
                        </Col>
                    </Row>
                </Card>

                {/* 风险评估 */}
                <Card
                    title={<Text className="!text-white">风险评估</Text>}
                    style={{
                        marginBottom: '24px',
                        background: 'rgba(255, 255, 255, 0.05)',
                        border: '1px solid rgba(255, 255, 255, 0.1)'
                    }}
                >
                    <Row gutter={[16, 16]}>
                        <Col span={12}>
                            <div>
                                <Text className="!text-gray-300" style={{ display: 'block', marginBottom: '8px' }}>
                                    风险因素
                                </Text>
                                <Space wrap>
                                    {risk_assessment.risk_factors.map((factor, index) => (
                                        <Tag key={index} color="orange">{factor}</Tag>
                                    ))}
                                </Space>
                            </div>
                        </Col>
                        <Col span={12}>
                            <div>
                                <Text className="!text-gray-300" style={{ display: 'block', marginBottom: '8px' }}>
                                    建议措施
                                </Text>
                                <ul style={{ color: 'rgba(255, 255, 255, 0.8)', margin: 0, paddingLeft: '16px' }}>
                                    {risk_assessment.recommendations.map((rec, index) => (
                                        <li key={index}>{rec}</li>
                                    ))}
                                </ul>
                            </div>
                        </Col>
                    </Row>
                </Card>

                {/* 质押记录表格 */}
                <Card
                    title={<Text className="!text-white">质押记录明细</Text>}
                    style={{
                        background: 'rgba(255, 255, 255, 0.05)',
                        border: '1px solid rgba(255, 255, 255, 0.1)'
                    }}
                >
                    <Table
                        dataSource={pledge_records}
                        rowKey="pledge_date"
                        pagination={{ pageSize: 10 }}
                        scroll={{ x: 800 }}
                        style={{
                            background: 'transparent',
                        }}
                        columns={[
                            {
                                title: '出质人',
                                dataIndex: 'pledger',
                                key: 'pledger',
                                width: 120,
                            },
                            {
                                title: '质权人',
                                dataIndex: 'pledgee',
                                key: 'pledgee',
                                width: 120,
                            },
                            {
                                title: '质押股数(万股)',
                                dataIndex: 'pledged_shares',
                                key: 'pledged_shares',
                                width: 120,
                                render: (value) => formatNumber(value),
                            },
                            {
                                title: '质押比例',
                                dataIndex: 'pledged_ratio',
                                key: 'pledged_ratio',
                                width: 100,
                                render: (value) => formatPercent(value),
                            },
                            {
                                title: '质押日期',
                                dataIndex: 'pledge_date',
                                key: 'pledge_date',
                                width: 100,
                            },
                            {
                                title: '状态',
                                dataIndex: 'status',
                                key: 'status',
                                width: 80,
                                render: (status) => {
                                    const statusMap = {
                                        active: { color: 'green', text: '有效' },
                                        released: { color: 'blue', text: '已解除' },
                                        expired: { color: 'red', text: '已到期' }
                                    };
                                    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
                                    return <Tag color={config.color}>{config.text}</Tag>;
                                }
                            },
                            {
                                title: '质押用途',
                                dataIndex: 'pledge_purpose',
                                key: 'pledge_purpose',
                                ellipsis: true,
                            }
                        ]}
                    />
                </Card>
            </div>
        );
    };

    // 渲染市场概况
    const renderMarketOverview = () => (
        <div style={{ padding: '24px', height: '100%', overflow: 'auto' }}>
            {marketProfile ? (
                <Row gutter={[24, 24]}>
                    {/* 市场统计卡片 */}
                    <Col span={24}>
                        <Card
                            title={<Text className="!text-white">市场概况统计</Text>}
                            style={{
                                background: 'rgba(255, 255, 255, 0.05)',
                                border: '1px solid rgba(255, 255, 255, 0.1)'
                            }}
                        >
                            <Row gutter={[16, 16]}>
                                <Col span={6}>
                                    <Statistic
                                        title={<Text className="!text-gray-300">总公司数</Text>}
                                        value={marketProfile.total_companies}
                                        prefix={<Building2 size={16} />}
                                        valueStyle={{ color: '#fff' }}
                                    />
                                </Col>
                                <Col span={6}>
                                    <Statistic
                                        title={<Text className="!text-gray-300">质押公司数</Text>}
                                        value={marketProfile.pledged_companies}
                                        prefix={<Shield size={16} />}
                                        valueStyle={{ color: '#faad14' }}
                                    />
                                </Col>
                                <Col span={6}>
                                    <Statistic
                                        title={<Text className="!text-gray-300">质押股份总数</Text>}
                                        value={formatNumber(marketProfile.total_pledged_shares)}
                                        prefix={<BarChart size={16} />}
                                        valueStyle={{ color: '#ff7875' }}
                                    />
                                </Col>
                                <Col span={6}>
                                    <Statistic
                                        title={<Text className="!text-gray-300">质押市值</Text>}
                                        value={formatNumber(marketProfile.total_pledged_market_value)}
                                        prefix={<DollarSign size={16} />}
                                        valueStyle={{ color: '#52c41a' }}
                                    />
                                </Col>
                            </Row>
                        </Card>
                    </Col>

                    {/* 质押比例分布 */}
                    <Col span={24}>
                        <Card
                            title={<Text className="!text-white">质押比例分布</Text>}
                            style={{
                                background: 'rgba(255, 255, 255, 0.05)',
                                border: '1px solid rgba(255, 255, 255, 0.1)'
                            }}
                        >
                            <Row gutter={[16, 16]}>
                                <Col span={6}>
                                    <div style={{ textAlign: 'center' }}>
                                        <Progress
                                            type="circle"
                                            percent={marketProfile.pledge_ratio_distribution.low}
                                            strokeColor="#52c41a"
                                            size={80}
                                        />
                                        <div style={{ marginTop: '8px' }}>
                                            <Text className="!text-white">低风险 (0-30%)</Text>
                                        </div>
                                    </div>
                                </Col>
                                <Col span={6}>
                                    <div style={{ textAlign: 'center' }}>
                                        <Progress
                                            type="circle"
                                            percent={marketProfile.pledge_ratio_distribution.medium}
                                            strokeColor="#faad14"
                                            size={80}
                                        />
                                        <div style={{ marginTop: '8px' }}>
                                            <Text className="!text-white">中等风险 (30-50%)</Text>
                                        </div>
                                    </div>
                                </Col>
                                <Col span={6}>
                                    <div style={{ textAlign: 'center' }}>
                                        <Progress
                                            type="circle"
                                            percent={marketProfile.pledge_ratio_distribution.high}
                                            strokeColor="#ff7875"
                                            size={80}
                                        />
                                        <div style={{ marginTop: '8px' }}>
                                            <Text className="!text-white">高风险 (50-80%)</Text>
                                        </div>
                                    </div>
                                </Col>
                                <Col span={6}>
                                    <div style={{ textAlign: 'center' }}>
                                        <Progress
                                            type="circle"
                                            percent={marketProfile.pledge_ratio_distribution.critical}
                                            strokeColor="#ff4d4f"
                                            size={80}
                                        />
                                        <div style={{ marginTop: '8px' }}>
                                            <Text className="!text-white">极高风险 (80%+)</Text>
                                        </div>
                                    </div>
                                </Col>
                            </Row>
                        </Card>
                    </Col>

                    {/* 公司质押排行 */}
                    <Col span={24}>
                        <Card
                            title={<Text className="!text-white">公司质押比例排行</Text>}
                            style={{
                                background: 'rgba(255, 255, 255, 0.05)',
                                border: '1px solid rgba(255, 255, 255, 0.1)'
                            }}
                        >
                            <Table
                                dataSource={companyRatios.slice(0, 20)}
                                rowKey="company_code"
                                pagination={false}
                                scroll={{ y: 400 }}
                                size="small"
                                columns={[
                                    {
                                        title: '排名',
                                        key: 'rank',
                                        width: 60,
                                        render: (_, __, index) => (
                                            <Badge
                                                count={index + 1}
                                                style={{
                                                    backgroundColor: index < 3 ? '#ff4d4f' : '#d9d9d9',
                                                    color: '#fff'
                                                }}
                                            />
                                        )
                                    },
                                    {
                                        title: '公司名称',
                                        dataIndex: 'company_name',
                                        key: 'company_name',
                                        ellipsis: true,
                                    },
                                    {
                                        title: '质押比例',
                                        dataIndex: 'pledge_ratio',
                                        key: 'pledge_ratio',
                                        width: 100,
                                        render: (value) => (
                                            <Text style={{ color: getRiskColor(value > 80 ? 'critical' : value > 50 ? 'high' : value > 30 ? 'medium' : 'low') }}>
                                                {formatPercent(value)}
                                            </Text>
                                        ),
                                        sorter: (a, b) => a.pledge_ratio - b.pledge_ratio,
                                    },
                                    {
                                        title: '质押市值',
                                        dataIndex: 'pledged_market_value',
                                        key: 'pledged_market_value',
                                        width: 100,
                                        render: (value) => formatNumber(value),
                                    },
                                    {
                                        title: '股价变动',
                                        dataIndex: 'price_change_ratio',
                                        key: 'price_change_ratio',
                                        width: 100,
                                        render: (value) => (
                                            <Text style={{ color: value >= 0 ? '#52c41a' : '#ff4d4f' }}>
                                                {value >= 0 ? '+' : ''}{formatPercent(value)}
                                            </Text>
                                        ),
                                    }
                                ]}
                            />
                        </Card>
                    </Col>
                </Row>
            ) : (
                <div style={{ textAlign: 'center', padding: '48px' }}>
                    <Spin size="large" />
                    <div style={{ marginTop: '16px' }}>
                        <Text className="!text-white">加载市场数据中...</Text>
                    </div>
                </div>
            )}
        </div>
    );

    // 渲染机构统计
    const renderInstitutionStats = () => (
        <div style={{ padding: '24px', height: '100%', overflow: 'auto' }}>
            <Row gutter={[24, 24]}>
                {/* 证券公司统计 */}
                <Col span={12}>
                    <Card
                        title={<Text className="!text-white">证券公司质押统计</Text>}
                        style={{
                            background: 'rgba(255, 255, 255, 0.05)',
                            border: '1px solid rgba(255, 255, 255, 0.1)'
                        }}
                    >
                        <Table
                            dataSource={institutionStats.securities.slice(0, 10)}
                            rowKey="institution_name"
                            pagination={false}
                            scroll={{ y: 300 }}
                            size="small"
                            columns={[
                                {
                                    title: '机构名称',
                                    dataIndex: 'institution_name',
                                    key: 'institution_name',
                                    ellipsis: true,
                                },
                                {
                                    title: '质押笔数',
                                    dataIndex: 'pledge_count',
                                    key: 'pledge_count',
                                    width: 80,
                                },
                                {
                                    title: '质押金额',
                                    dataIndex: 'total_pledge_amount',
                                    key: 'total_pledge_amount',
                                    width: 100,
                                    render: (value) => formatNumber(value),
                                },
                                {
                                    title: '风险率',
                                    dataIndex: 'risk_ratio',
                                    key: 'risk_ratio',
                                    width: 80,
                                    render: (value) => (
                                        <Text style={{ color: getRiskColor(value > 20 ? 'high' : value > 10 ? 'medium' : 'low') }}>
                                            {formatPercent(value)}
                                        </Text>
                                    ),
                                }
                            ]}
                        />
                    </Card>
                </Col>

                {/* 银行统计 */}
                <Col span={12}>
                    <Card
                        title={<Text className="!text-white">银行质押统计</Text>}
                        style={{
                            background: 'rgba(255, 255, 255, 0.05)',
                            border: '1px solid rgba(255, 255, 255, 0.1)'
                        }}
                    >
                        <Table
                            dataSource={institutionStats.banks.slice(0, 10)}
                            rowKey="institution_name"
                            pagination={false}
                            scroll={{ y: 300 }}
                            size="small"
                            columns={[
                                {
                                    title: '机构名称',
                                    dataIndex: 'institution_name',
                                    key: 'institution_name',
                                    ellipsis: true,
                                },
                                {
                                    title: '质押笔数',
                                    dataIndex: 'pledge_count',
                                    key: 'pledge_count',
                                    width: 80,
                                },
                                {
                                    title: '质押金额',
                                    dataIndex: 'total_pledge_amount',
                                    key: 'total_pledge_amount',
                                    width: 100,
                                    render: (value) => formatNumber(value),
                                },
                                {
                                    title: '风险率',
                                    dataIndex: 'risk_ratio',
                                    key: 'risk_ratio',
                                    width: 80,
                                    render: (value) => (
                                        <Text style={{ color: getRiskColor(value > 20 ? 'high' : value > 10 ? 'medium' : 'low') }}>
                                            {formatPercent(value)}
                                        </Text>
                                    ),
                                }
                            ]}
                        />
                    </Card>
                </Col>

                {/* 机构对比图表 */}
                <Col span={24}>
                    <Card
                        title={<Text className="!text-white">机构质押业务对比</Text>}
                        style={{
                            background: 'rgba(255, 255, 255, 0.05)',
                            border: '1px solid rgba(255, 255, 255, 0.1)'
                        }}
                    >
                        <Row gutter={[16, 16]}>
                            <Col span={8}>
                                <Statistic
                                    title={<Text className="!text-gray-300">证券公司总笔数</Text>}
                                    value={institutionStats.securities.reduce((sum, item) => sum + item.pledge_count, 0)}
                                    prefix={<FileText size={16} />}
                                    valueStyle={{ color: '#1890ff' }}
                                />
                            </Col>
                            <Col span={8}>
                                <Statistic
                                    title={<Text className="!text-gray-300">银行总笔数</Text>}
                                    value={institutionStats.banks.reduce((sum, item) => sum + item.pledge_count, 0)}
                                    prefix={<FileText size={16} />}
                                    valueStyle={{ color: '#52c41a' }}
                                />
                            </Col>
                            <Col span={8}>
                                <Statistic
                                    title={<Text className="!text-gray-300">平均风险率</Text>}
                                    value={
                                        [...institutionStats.securities, ...institutionStats.banks]
                                            .reduce((sum, item, _, arr) => sum + item.risk_ratio / arr.length, 0)
                                    }
                                    suffix="%"
                                    prefix={<AlertTriangle size={16} />}
                                    valueStyle={{ color: '#faad14' }}
                                />
                            </Col>
                        </Row>
                    </Card>
                </Col>
            </Row>
        </div>
    );

    return (
        <div style={{ width: '100%', height: '100%', minHeight: '600px', position: 'relative' }}>
            <Layout style={{ background: 'transparent', padding: '16px', height: '100%', minHeight: '600px' }}>
                <Sider width={320} style={{ background: 'transparent', marginRight: '16px' }}>
                    {renderControlPanel()}
                </Sider>

                <Content style={{ position: 'relative' }}>
                    <GlassmorphicCard style={{ padding: 0, height: '100%' }}>
                        <Tabs
                            activeKey={activeTab}
                            onChange={setActiveTab}
                            style={{ height: '100%' }}
                            tabBarStyle={{
                                padding: '0 24px',
                                background: 'rgba(255, 255, 255, 0.05)',
                                margin: 0
                            }}
                        >
                            <TabPane
                                tab={
                                    <span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                                        <Search size={16} style={{ marginRight: '8px' }} />
                                        公司分析
                                    </span>
                                }
                                key="analysis"
                            >
                                {renderAnalysisResult()}
                            </TabPane>

                            <TabPane
                                tab={
                                    <span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                                        <BarChart size={16} style={{ marginRight: '8px' }} />
                                        市场概况
                                    </span>
                                }
                                key="market"
                            >
                                {renderMarketOverview()}
                            </TabPane>

                            <TabPane
                                tab={
                                    <span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                                        <Building2 size={16} style={{ marginRight: '8px' }} />
                                        机构统计
                                    </span>
                                }
                                key="institutions"
                            >
                                {renderInstitutionStats()}
                            </TabPane>
                        </Tabs>
                    </GlassmorphicCard>
                </Content>
            </Layout>
        </div>
    );
};