# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

IDEALAB is a microservices-based web application platform providing intelligent tools and analytics. It consists of a React frontend and multiple backend services including authentication, NLP, financial analysis, and document processing capabilities.

## Architecture

### Microservices Structure
- **Frontend**: React + TypeScript + Vite (port 3000)
- **API Gateway**: Node.js + Express (port 8000, production port 16576)
- **Auth Service**: Node.js + Express + PostgreSQL (port 8001)
- **NLP Service**: Python + FastAPI (port 8002)
- **Financial Service**: Python + FastAPI + Neo4j (port 8003)
- **Document Service**: Python + FastAPI + MinIO (port 8004)

### Key Technologies
- Frontend: React 18, TypeScript, Tailwind CSS, Ant Design, Zustand, Vite
- Backend: Node.js/Express, Python/FastAPI, PostgreSQL, Neo4j, Redis, MinIO
- Authentication: JWT tokens with refresh mechanism, Prisma ORM
- State Management: Zustand for React state
- Graph Database: Neo4j for financial network analysis
- Object Storage: MinIO for document processing

## Development Commands

### Quick Start (Recommended)
```bash
# Start all services with monitoring script
chmod +x scripts/start.sh
./scripts/start.sh start          # Start all services
./scripts/start.sh status         # Check service health
./scripts/start.sh logs [service] # View logs
./scripts/start.sh stop           # Stop all services
```

### Frontend Development
```bash
cd frontend
npm install           # Install dependencies
npm run dev          # Start development server (port 3000)
npm run build        # Build for production
npm run lint         # Run ESLint
```

### Full Stack Development
```bash
# Using Docker Compose
docker-compose up -d
docker-compose ps           # Check service status
docker-compose logs -f      # View all logs
docker-compose logs -f auth # View specific service logs
```

### Individual Service Development

#### Auth Service (Node.js + Prisma)
```bash
cd services/auth
npm install
npm run dev                    # Development with nodemon
npm start                      # Production (includes DB migration)
npx prisma migrate dev         # Run database migrations
npx prisma db seed            # Seed database with test data
```

#### Python Services (NLP, Financial, Document)
```bash
cd services/[service-name]
poetry install
poetry run uvicorn main:app --reload --port [PORT]

# Code quality checks
poetry run black src/         # Format code
poetry run flake8 src/        # Lint code
poetry run mypy src/          # Type checking
```

#### API Gateway
```bash
cd services/gateway
npm install
npm run dev                   # Development with nodemon
npm run test                  # Run Jest tests
```

### Database Operations
```bash
# PostgreSQL setup (Auth service)
cd services/auth
npx prisma migrate dev --name init
npx prisma db seed

# Neo4j operations (Financial service)
# Access Neo4j browser at http://localhost:7474
# Default credentials: neo4j/[NEO4J_PASSWORD from .env]
```

## Code Architecture Patterns

### Frontend Structure
- **Components**: Located in `frontend/components/`, includes UI components and layout components
- **Pages**: Main application pages in `frontend/pages/`, with tool-specific pages in `pages/tools/`
- **State Management**: Zustand stores in `frontend/store/` for auth and application state
- **Services**: API communication layer in `frontend/services/`
- **Types**: TypeScript definitions in `frontend/types/`
- **Utils**: Helper functions in `frontend/utils/`
- **Hooks**: Custom React hooks in `frontend/hooks/`

### Backend Services Architecture
- **Auth Service**: Prisma ORM with PostgreSQL, JWT token management, user authentication
- **Python Services**: FastAPI with dependency injection, Pydantic models, async patterns
- **API Gateway**: Express middleware for routing, authentication, rate limiting
- **Database Patterns**: 
  - PostgreSQL for relational data (auth, user management)
  - Neo4j for graph data (financial networks, equity analysis)
  - Redis for caching and session management
  - MinIO for object storage (documents, files)

### Service Communication
- All frontend requests go through API Gateway (port 8000/16576)
- Gateway routes to appropriate microservices
- Services communicate via HTTP REST APIs
- Authentication handled centrally through Gateway middleware

### Key Features
The application provides 8 core tools:
1. AI Chatbot (NLP service) - OpenAI integration, conversation management
2. Sentiment Analysis (NLP service) - Text analysis, Chinese language support
3. Equity Penetration Analysis (Financial service) - Neo4j graph analysis
4. Equity Pledge Analysis (Financial service) - Financial risk assessment
5. Bond Market Analysis (Financial service) - Market data visualization
6. Batch OCR (Document service) - Tesseract OCR, image processing
7. Batch Watermarking (Document service) - Image manipulation
8. Excel Merger (Document service) - Pandas-based data processing

## Environment Configuration

### Required Environment Variables
Create `.env` files with these key variables:
- `JWT_SECRET`: Secret for JWT token signing
- `POSTGRES_PASSWORD`: PostgreSQL database password
- `NEO4J_PASSWORD`: Neo4j database password
- `REDIS_PASSWORD`: Redis cache password
- `MINIO_ACCESS_KEY` / `MINIO_SECRET_KEY`: Object storage credentials
- `OPENAI_API_KEY`: OpenAI API key for NLP services

### Development vs Production
- Development: Individual service ports (3000, 8000-8004)
- Production: Gateway on port 16576, internal service communication
- Docker Compose handles service discovery and networking

## Testing

### Current Test Setup
- Auth Service: Jest configured but tests need implementation
- Gateway: Jest configured but tests need implementation  
- Python Services: pytest configured in pyproject.toml
- Frontend: No test framework currently configured

### Adding Tests
When implementing tests:
- Node.js services: Use `npm run test` (Jest)
- Python services: Use `poetry run pytest`
- Check individual package.json/pyproject.toml for service-specific test commands

## Python Service Dependencies

### Key Python Libraries by Service
- **NLP Service**: transformers, torch, langchain, openai, jieba (Chinese text processing)
- **Financial Service**: networkx, neo4j, pandas, numpy, matplotlib, plotly, pyvis
- **Document Service**: opencv-python, pillow, pytesseract, pandas, openpyxl, python-docx, PyPDF2, minio

### Common Python Development Commands
```bash
# Code quality for all Python services
poetry run black src/         # Code formatting
poetry run flake8 src/        # Linting
poetry run mypy src/          # Type checking
poetry run pytest            # Run tests
```

## Database Schema

### Auth Service (PostgreSQL)
- Users table with fields: id, username, password, email, role, passwordChangeRequired
- Managed through Prisma ORM with schema at `services/auth/src/prisma/schema.prisma`

### Default Credentials
- Admin user: username `admin`, password `admin123` (created by seed script)

## Service Ports Reference
- Frontend: 3000
- API Gateway: 8000 (dev), 16576 (production)
- Auth Service: 8001
- NLP Service: 8002
- Financial Service: 8003
- Document Service: 8004
- PostgreSQL: 5432
- Neo4j: 7474 (browser), 7687 (bolt)
- Redis: 6379
- MinIO: 9000 (API), 9001 (console)