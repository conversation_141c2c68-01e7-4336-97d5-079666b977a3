#!/usr/bin/env python3
"""
股权质押数据爬取脚本 - 简化版
功能：从AKShare获取重要股东股权质押明细数据并存储到PostgreSQL数据库
适用于调度平台，专注核心功能
作者：IDEALAB团队
创建时间：2024-01-15
"""

import os
import sys
import logging
import pandas as pd
import psycopg2
from psycopg2.extras import execute_values
from datetime import datetime
import akshare as ak
import time
from typing import Optional, Dict, Any

# 配置日志 - 只输出到控制台，避免文件路径问题
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

class PledgeDataCrawler:
    """股权质押数据爬取器"""
    
    def __init__(self, db_config: Dict[str, Any]):
        """
        初始化爬取器
        
        Args:
            db_config: 数据库连接配置
        """
        self.db_config = db_config
        self.connection = None
        self.cursor = None
        
    def connect_database(self) -> bool:
        """
        连接数据库
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_database(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("数据库连接已关闭")
    
    def create_table(self):
        """创建股权质押明细表"""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS pledge_details (
            id SERIAL PRIMARY KEY,
            sequence_number INTEGER,
            stock_code VARCHAR(20) NOT NULL,
            stock_name VARCHAR(100) NOT NULL,
            shareholder_name VARCHAR(200) NOT NULL,
            pledged_shares BIGINT,
            pledge_ratio_of_holdings DECIMAL(10, 4),
            pledge_ratio_of_total_shares DECIMAL(10, 4),
            pledge_institution VARCHAR(200),
            latest_price DECIMAL(10, 4),
            pledge_date_closing_price DECIMAL(10, 4),
            estimated_liquidation_line DECIMAL(10, 4),
            announcement_date DATE,
            pledge_start_date DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            -- 创建索引
            CONSTRAINT unique_pledge_record UNIQUE (
                stock_code, shareholder_name, pledge_start_date, announcement_date
            )
        );
        
        -- 创建索引以提高查询性能
        CREATE INDEX IF NOT EXISTS idx_pledge_stock_code ON pledge_details(stock_code);
        CREATE INDEX IF NOT EXISTS idx_pledge_shareholder ON pledge_details(shareholder_name);
        CREATE INDEX IF NOT EXISTS idx_pledge_announcement_date ON pledge_details(announcement_date);
        CREATE INDEX IF NOT EXISTS idx_pledge_start_date ON pledge_details(pledge_start_date);
        CREATE INDEX IF NOT EXISTS idx_pledge_ratio ON pledge_details(pledge_ratio_of_total_shares);
        
        -- 创建更新时间触发器
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = CURRENT_TIMESTAMP;
            RETURN NEW;
        END;
        $$ language 'plpgsql';
        
        DROP TRIGGER IF EXISTS update_pledge_details_updated_at ON pledge_details;
        CREATE TRIGGER update_pledge_details_updated_at
            BEFORE UPDATE ON pledge_details
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        """
        
        try:
            self.cursor.execute(create_table_sql)
            self.connection.commit()
            logger.info("数据表创建/更新成功")
        except Exception as e:
            logger.error(f"创建数据表失败: {e}")
            self.connection.rollback()
            raise
    
    def fetch_pledge_data(self) -> Optional[pd.DataFrame]:
        """
        从AKShare获取股权质押数据
        
        Returns:
            pd.DataFrame: 质押数据，如果失败返回None
        """
        try:
            logger.info("开始获取股权质押数据...")
            start_time = time.time()
            
            # 获取数据
            df = ak.stock_gpzy_pledge_ratio_detail_em()
            
            end_time = time.time()
            logger.info(f"数据获取完成，耗时: {end_time - start_time:.2f}秒")
            logger.info(f"获取到 {len(df)} 条记录")
            
            return df
            
        except Exception as e:
            logger.error(f"获取数据失败: {e}")
            return None
    
    def clean_and_validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清洗和验证数据
        
        Args:
            df: 原始数据
            
        Returns:
            pd.DataFrame: 清洗后的数据
        """
        logger.info("开始数据清洗和验证...")
        
        # 重命名列以匹配数据库字段
        column_mapping = {
            '序号': 'sequence_number',
            '股票代码': 'stock_code',
            '股票简称': 'stock_name',
            '股东名称': 'shareholder_name',
            '质押股份数量': 'pledged_shares',
            '占所持股份比例': 'pledge_ratio_of_holdings',
            '占总股本比例': 'pledge_ratio_of_total_shares',
            '质押机构': 'pledge_institution',
            '最新价': 'latest_price',
            '质押日收盘价': 'pledge_date_closing_price',
            '预估平仓线': 'estimated_liquidation_line',
            '公告日期': 'announcement_date',
            '质押开始日期': 'pledge_start_date'
        }
        
        df = df.rename(columns=column_mapping)
        
        # 数据类型转换和清洗
        try:
            # 处理日期字段
            df['announcement_date'] = pd.to_datetime(df['announcement_date'], errors='coerce')
            df['pledge_start_date'] = pd.to_datetime(df['pledge_start_date'], errors='coerce')
            
            # 处理数值字段
            numeric_columns = [
                'pledged_shares', 'pledge_ratio_of_holdings', 'pledge_ratio_of_total_shares',
                'latest_price', 'pledge_date_closing_price', 'estimated_liquidation_line'
            ]
            
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 处理文本字段
            text_columns = ['stock_code', 'stock_name', 'shareholder_name', 'pledge_institution']
            for col in text_columns:
                df[col] = df[col].astype(str).str.strip()
            
            # 移除无效记录
            initial_count = len(df)
            df = df.dropna(subset=['stock_code', 'stock_name', 'shareholder_name'])
            final_count = len(df)
            
            if initial_count != final_count:
                logger.warning(f"移除了 {initial_count - final_count} 条无效记录")
            
            logger.info(f"数据清洗完成，有效记录: {final_count} 条")
            
        except Exception as e:
            logger.error(f"数据清洗失败: {e}")
            raise
        
        return df
    
    def insert_data(self, df: pd.DataFrame) -> bool:
        """
        将数据插入数据库
        
        Args:
            df: 要插入的数据
            
        Returns:
            bool: 插入是否成功
        """
        try:
            logger.info("开始插入数据到数据库...")
            
            # 准备插入数据
            insert_sql = """
            INSERT INTO pledge_details (
                sequence_number, stock_code, stock_name, shareholder_name,
                pledged_shares, pledge_ratio_of_holdings, pledge_ratio_of_total_shares,
                pledge_institution, latest_price, pledge_date_closing_price,
                estimated_liquidation_line, announcement_date, pledge_start_date
            ) VALUES %s
            ON CONFLICT (stock_code, shareholder_name, pledge_start_date, announcement_date)
            DO UPDATE SET
                sequence_number = EXCLUDED.sequence_number,
                pledged_shares = EXCLUDED.pledged_shares,
                pledge_ratio_of_holdings = EXCLUDED.pledge_ratio_of_holdings,
                pledge_ratio_of_total_shares = EXCLUDED.pledge_ratio_of_total_shares,
                pledge_institution = EXCLUDED.pledge_institution,
                latest_price = EXCLUDED.latest_price,
                pledge_date_closing_price = EXCLUDED.pledge_date_closing_price,
                estimated_liquidation_line = EXCLUDED.estimated_liquidation_line,
                updated_at = CURRENT_TIMESTAMP
            """
            
            # 转换数据为元组列表
            data_tuples = []
            for _, row in df.iterrows():
                data_tuple = (
                    int(row['sequence_number']) if pd.notna(row['sequence_number']) else None,
                    row['stock_code'],
                    row['stock_name'],
                    row['shareholder_name'],
                    int(row['pledged_shares']) if pd.notna(row['pledged_shares']) else None,
                    float(row['pledge_ratio_of_holdings']) if pd.notna(row['pledge_ratio_of_holdings']) else None,
                    float(row['pledge_ratio_of_total_shares']) if pd.notna(row['pledge_ratio_of_total_shares']) else None,
                    row['pledge_institution'],
                    float(row['latest_price']) if pd.notna(row['latest_price']) else None,
                    float(row['pledge_date_closing_price']) if pd.notna(row['pledge_date_closing_price']) else None,
                    float(row['estimated_liquidation_line']) if pd.notna(row['estimated_liquidation_line']) else None,
                    row['announcement_date'].date() if pd.notna(row['announcement_date']) else None,
                    row['pledge_start_date'].date() if pd.notna(row['pledge_start_date']) else None
                )
                data_tuples.append(data_tuple)
            
            # 批量插入
            execute_values(
                self.cursor,
                insert_sql,
                data_tuples,
                template=None,
                page_size=1000
            )
            
            self.connection.commit()
            logger.info(f"成功插入/更新 {len(data_tuples)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"数据插入失败: {e}")
            self.connection.rollback()
            return False

    def get_data_statistics(self) -> Dict[str, Any]:
        """获取数据统计信息"""
        try:
            stats_sql = """
            SELECT
                COUNT(*) as total_records,
                COUNT(DISTINCT stock_code) as unique_stocks,
                COUNT(DISTINCT shareholder_name) as unique_shareholders,
                MIN(announcement_date) as earliest_date,
                MAX(announcement_date) as latest_date
            FROM pledge_details
            """

            self.cursor.execute(stats_sql)
            result = self.cursor.fetchone()

            if result:
                return {
                    'total_records': result[0],
                    'unique_stocks': result[1],
                    'unique_shareholders': result[2],
                    'earliest_date': result[3],
                    'latest_date': result[4]
                }
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
        return {}

    def run(self) -> bool:
        """执行完整的数据更新流程"""
        try:
            logger.info("开始执行股权质押数据更新...")
            start_time = time.time()

            # 连接数据库
            if not self.connect_database():
                return False

            # 创建表
            self.create_table()

            # 获取数据
            df = self.fetch_pledge_data()
            if df is None or df.empty:
                logger.error("未获取到有效数据")
                return False

            # 清洗数据
            df_clean = self.clean_and_validate_data(df)

            # 插入数据
            success = self.insert_data(df_clean)

            if success:
                # 获取统计信息
                stats = self.get_data_statistics()
                logger.info("数据更新完成，统计信息:")
                for key, value in stats.items():
                    logger.info(f"  {key}: {value}")

                end_time = time.time()
                logger.info(f"总耗时: {end_time - start_time:.2f}秒")

            return success

        except Exception as e:
            logger.error(f"数据更新失败: {e}")
            return False
        finally:
            self.close_database()


def get_database_config() -> Dict[str, Any]:
    """获取数据库配置"""
    return {
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': int(os.getenv('DB_PORT', 5432)),
        'database': os.getenv('DB_NAME', 'idealab'),
        'user': os.getenv('DB_USER', 'postgres'),
        'password': os.getenv('DB_PASSWORD', 'password')
    }


def main():
    """主函数"""
    try:
        logger.info("股权质押数据爬取脚本启动")

        # 获取数据库配置
        db_config = get_database_config()

        # 创建爬取器并执行
        crawler = PledgeDataCrawler(db_config)
        success = crawler.run()

        if success:
            logger.info("股权质押数据更新成功")
        else:
            logger.error("股权质押数据更新失败")
            sys.exit(1)

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
