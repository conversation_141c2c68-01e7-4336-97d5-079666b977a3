{"name": "idealab-gateway", "version": "1.0.0", "description": "IDEALAB API Gateway Service", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "http-proxy-middleware": "^2.0.6", "jsonwebtoken": "^9.0.2", "redis": "^4.6.11", "dotenv": "^16.3.1", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "keywords": ["gateway", "api", "microservices"], "author": "IDEALAB Team", "license": "MIT"}