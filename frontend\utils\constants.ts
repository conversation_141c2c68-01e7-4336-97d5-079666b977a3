import { Tool } from '@/types';
import { 
  BarChart3, 
  Search, 
  TrendingUp, 
  DollarSign,
  FileText, 
  Tag, 
  FileSpreadsheet 
} from 'lucide-react';

export const TOOLS_DATA: Tool[] = [
  {
    id: 'sentiment-analysis',
    name: '舆情报告生成',
    description: '分析网络舆情，生成专业舆情报告',
    icon: BarChart3,
    category: '分析工具',
    path: '/tools/sentiment-analysis',
    featured: true
  },
  {
    id: 'equity-penetration',
    name: '股权穿透工具',
    description: '深度分析企业股权结构和穿透关系',
    icon: Search,
    category: '金融工具',
    path: '/tools/equity-penetration',
    featured: true
  },
  {
    id: 'equity-pledge',
    name: '股权质押分析',
    description: '分析股权质押情况和风险评估',
    icon: TrendingUp,
    category: '金融工具',
    path: '/tools/equity-pledge'
  },
  {
    id: 'bond-analysis',
    name: '债券市场分析',
    description: '债券市场数据分析和投资建议',
    icon: DollarSign,
    category: '金融工具',
    path: '/tools/bond-analysis'
  },
  {
    id: 'batch-ocr',
    name: '批量OCR',
    description: '批量识别图片和PDF中的文字内容',
    icon: FileText,
    category: '文档工具',
    path: '/tools/batch-ocr'
  },
  {
    id: 'batch-watermark',
    name: '批量加水印',
    description: '为图片和文档批量添加水印',
    icon: Tag,
    category: '文档工具',
    path: '/tools/batch-watermark'
  },
  {
    id: 'excel-merger',
    name: 'Excel暴力合并',
    description: '智能合并多个Excel文件和工作表',
    icon: FileSpreadsheet,
    category: '文档工具',
    path: '/tools/excel-merger'
  }
];