import os
import aiofiles
from pathlib import Path
import tempfile
import uuid
import logging
from typing import Optional

logger = logging.getLogger(__name__)


class StorageService:
    """文件存储服务"""
    
    def __init__(self):
        self.storage_dir = Path(tempfile.gettempdir()) / "document_storage"
        self.storage_dir.mkdir(exist_ok=True)
    
    async def initialize(self):
        """初始化存储服务"""
        logger.info(f"Storage service initialized at: {self.storage_dir}")
    
    async def save_file(self, file_content: bytes, filename: str) -> str:
        """保存文件并返回文件ID"""
        file_id = str(uuid.uuid4())
        file_path = self.storage_dir / f"{file_id}_{filename}"
        
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(file_content)
        
        return file_id
    
    async def get_file_path(self, file_id: str) -> Optional[str]:
        """根据文件ID获取文件路径"""
        # 查找以file_id开头的文件
        for file_path in self.storage_dir.glob(f"{file_id}_*"):
            return str(file_path)
        return None
    
    async def delete_file(self, file_id: str) -> bool:
        """删除文件"""
        file_path = await self.get_file_path(file_id)
        if file_path and Path(file_path).exists():
            Path(file_path).unlink()
            return True
        return False
