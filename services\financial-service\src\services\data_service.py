from typing import Dict, List, Any, Optional
import asyncio
import logging
from datetime import datetime
from ..services.graph_service import GraphService

logger = logging.getLogger(__name__)


class DataService:
    def __init__(self):
        self.graph_service = None

    async def initialize(self):
        """初始化数据服务"""
        self.graph_service = GraphService()
        await self.graph_service.initialize()
        logger.info("DataService初始化完成")

    async def close(self):
        """关闭服务"""
        if self.graph_service:
            await self.graph_service.close()

    async def import_company_data(self, companies: List[Dict[str, Any]]) -> Dict:
        """批量导入公司数据"""
        results = {
            "success": 0,
            "failed": 0,
            "errors": []
        }
        
        for company in companies:
            try:
                await self.graph_service.create_company_node(company)
                results["success"] += 1
            except Exception as e:
                results["failed"] += 1
                results["errors"].append(f"Company {company.get('name', 'Unknown')}: {str(e)}")
                logger.error(f"导入公司数据失败: {e}")
        
        return results

    async def import_person_data(self, persons: List[Dict[str, Any]]) -> Dict:
        """批量导入个人数据"""
        results = {
            "success": 0,
            "failed": 0,
            "errors": []
        }
        
        for person in persons:
            try:
                await self.graph_service.create_person_node(person)
                results["success"] += 1
            except Exception as e:
                results["failed"] += 1
                results["errors"].append(f"Person {person.get('name', 'Unknown')}: {str(e)}")
                logger.error(f"导入个人数据失败: {e}")
        
        return results

    async def import_investment_data(self, investments: List[Dict[str, Any]]) -> Dict:
        """批量导入投资数据"""
        results = {
            "success": 0,
            "failed": 0,
            "errors": []
        }
        
        for investment in investments:
            try:
                await self.graph_service.create_investment_relationship(
                    investment["investor_id"],
                    investment["company_id"],
                    investment
                )
                results["success"] += 1
            except Exception as e:
                results["failed"] += 1
                results["errors"].append(f"Investment {investment.get('investor_id', 'Unknown')}: {str(e)}")
                logger.error(f"导入投资数据失败: {e}")
        
        return results

    async def import_shareholding_data(self, shareholdings: List[Dict[str, Any]]) -> Dict:
        """批量导入持股数据"""
        results = {
            "success": 0,
            "failed": 0,
            "errors": []
        }
        
        for shareholding in shareholdings:
            try:
                await self.graph_service.create_shareholding_relationship(
                    shareholding["shareholder_id"],
                    shareholding["company_id"],
                    shareholding
                )
                results["success"] += 1
            except Exception as e:
                results["failed"] += 1
                results["errors"].append(f"Shareholding {shareholding.get('shareholder_id', 'Unknown')}: {str(e)}")
                logger.error(f"导入持股数据失败: {e}")
        
        return results

    async def get_company_profile(self, company_id: str) -> Optional[Dict]:
        """获取公司档案"""
        try:
            # 获取公司基本信息
            company_query = """
            MATCH (c:Company {unified_code: $company_id})
            RETURN c {.*} as company
            """
            company_result = await self.graph_service.neo4j_client.execute_query(
                company_query, {"company_id": company_id}
            )
            
            if not company_result:
                return None
            
            company = company_result[0]["company"]
            
            # 获取股东信息
            shareholders = await self.graph_service.get_company_shareholders(company_id)
            
            # 获取对外投资信息
            investments = await self.graph_service.get_investment_portfolio(company_id)
            
            # 获取关联公司
            related_companies = await self.graph_service.get_related_companies(company_id)
            
            return {
                "company": company,
                "shareholders": shareholders,
                "investments": investments,
                "related_companies": related_companies,
                "updated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取公司档案失败: {e}")
            return None

    async def validate_data_integrity(self) -> Dict:
        """验证数据完整性"""
        validation_results = {
            "total_companies": 0,
            "total_persons": 0,
            "total_investments": 0,
            "total_shareholdings": 0,
            "orphaned_nodes": 0,
            "missing_relationships": 0,
            "data_quality_score": 0.0
        }
        
        try:
            # 获取统计信息
            stats = await self.graph_service.neo4j_client.get_database_stats()
            validation_results.update(stats)
            
            # 检查孤立节点
            orphaned_query = """
            MATCH (n)
            WHERE NOT (n)--()
            RETURN count(n) as orphaned_count
            """
            orphaned_result = await self.graph_service.neo4j_client.execute_query(orphaned_query)
            validation_results["orphaned_nodes"] = orphaned_result[0]["orphaned_count"]
            
            # 计算数据质量分数
            total_nodes = validation_results["total_companies"] + validation_results["total_persons"]
            total_relationships = validation_results["total_investments"] + validation_results["total_shareholdings"]
            
            if total_nodes > 0:
                quality_score = 1.0 - (validation_results["orphaned_nodes"] / total_nodes)
                validation_results["data_quality_score"] = round(quality_score, 2)
            
            return validation_results
            
        except Exception as e:
            logger.error(f"数据完整性验证失败: {e}")
            return validation_results

    async def cleanup_duplicate_data(self) -> Dict:
        """清理重复数据"""
        cleanup_results = {
            "duplicate_companies": 0,
            "duplicate_persons": 0,
            "duplicate_relationships": 0
        }
        
        try:
            # 清理重复公司
            duplicate_companies_query = """
            MATCH (c1:Company), (c2:Company)
            WHERE c1.name = c2.name AND c1.unified_code <> c2.unified_code
            WITH c1, c2
            ORDER BY c1.updated_at DESC
            WITH c1, collect(c2) as duplicates
            FOREACH (dup in duplicates | 
                OPTIONAL MATCH (dup)-[r]-()
                DELETE r, dup
            )
            RETURN count(duplicates) as cleaned_count
            """
            
            # 清理重复个人
            duplicate_persons_query = """
            MATCH (p1:Person), (p2:Person)
            WHERE p1.name = p2.name AND p1.id_number <> p2.id_number
            WITH p1, p2
            ORDER BY p1.updated_at DESC
            WITH p1, collect(p2) as duplicates
            FOREACH (dup in duplicates | 
                OPTIONAL MATCH (dup)-[r]-()
                DELETE r, dup
            )
            RETURN count(duplicates) as cleaned_count
            """
            
            # 执行清理
            company_result = await self.graph_service.neo4j_client.execute_write_query(duplicate_companies_query)
            person_result = await self.graph_service.neo4j_client.execute_write_query(duplicate_persons_query)
            
            cleanup_results["duplicate_companies"] = company_result.get("nodes_deleted", 0)
            cleanup_results["duplicate_persons"] = person_result.get("nodes_deleted", 0)
            
            return cleanup_results
            
        except Exception as e:
            logger.error(f"清理重复数据失败: {e}")
            return cleanup_results

    async def export_data_summary(self) -> Dict:
        """导出数据摘要"""
        try:
            summary = {
                "export_time": datetime.now().isoformat(),
                "database_stats": await self.graph_service.neo4j_client.get_database_stats(),
                "data_quality": await self.validate_data_integrity(),
                "top_companies": [],
                "top_investors": []
            }
            
            # 获取前10大公司（按对外投资数量）
            top_companies_query = """
            MATCH (c:Company)-[:INVESTS_IN]->()
            WITH c, count(*) as investment_count
            ORDER BY investment_count DESC
            LIMIT 10
            RETURN c {.*, investment_count: investment_count} as company
            """
            top_companies = await self.graph_service.neo4j_client.execute_query(top_companies_query)
            summary["top_companies"] = [result["company"] for result in top_companies]
            
            # 获取前10大投资人（按投资金额）
            top_investors_query = """
            MATCH (investor)-[r:INVESTS_IN]->()
            WITH investor, sum(r.investment_amount) as total_investment
            ORDER BY total_investment DESC
            LIMIT 10
            RETURN investor {
                .*, 
                total_investment: total_investment,
                type: CASE 
                    WHEN 'Company' IN labels(investor) THEN 'company' 
                    ELSE 'person' 
                END
            } as investor
            """
            top_investors = await self.graph_service.neo4j_client.execute_query(top_investors_query)
            summary["top_investors"] = [result["investor"] for result in top_investors]
            
            return summary
            
        except Exception as e:
            logger.error(f"导出数据摘要失败: {e}")
            return {"error": str(e)}