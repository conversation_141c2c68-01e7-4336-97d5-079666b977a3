[tool.poetry]
name = "idealab-document-service"
version = "1.0.0"
description = "IDEALAB Document Processing Service for OCR, Watermark and Excel Operations"
authors = ["IDEALAB Team <<EMAIL>>"]
license = "MIT"
package-mode = false

[tool.poetry.dependencies]
python = "^3.9"
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
pydantic = "^2.5.0"
python-multipart = "^0.0.6"
opencv-python = "^4.8.1"
pillow = "^10.1.0"
pytesseract = "^0.3.10"
pandas = "^2.1.0"
openpyxl = "^3.1.2"
xlsxwriter = "^3.1.9"
python-docx = "^1.1.0"
PyPDF2 = "^3.0.1"
pdf2image = "^1.16.3"
reportlab = "^4.0.7"
aiofiles = "^23.2.0"
celery = "^5.3.4"
redis = "^5.0.1"
minio = "^7.2.0"
python-dotenv = "^1.0.0"
requests = "^2.31.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
black = "^23.0.0"
flake8 = "^6.0.0"
mypy = "^1.7.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"