import React, { useState } from 'react';
import { Layout, Typography, Button, Statistic, message } from 'antd';
import { BarChart3, FileText, TrendingUp, Brain } from 'lucide-react';
import { GlassmorphicCard } from '@/components/GlassmorphicCard';

const { Sider, Content } = Layout;
const { Title, Text, Paragraph } = Typography;

export const SentimentAnalysisTool: React.FC = () => {
    const [loading, setLoading] = useState(false);

    const handleAnalyze = () => {
        setLoading(true);
        message.info('舆情分析功能正在开发中...');
        setTimeout(() => setLoading(false), 2000);
    };

    return (
        <div style={{ width: '100%', height: '100%', minHeight: '600px', position: 'relative' }}>
            <Layout style={{ background: 'transparent', padding: '16px', height: '100%', minHeight: '600px' }}>
                <Sider width={280} style={{ background: 'transparent', marginRight: '16px' }}>
                    <GlassmorphicCard title={<Title level={5} className="!text-white">控制面板</Title>}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
                            <Statistic 
                                title={<Text className="!text-gray-300">分析文档</Text>} 
                                value={0} 
                                prefix={<FileText size={16} />} 
                                valueStyle={{ color: '#fff', fontSize: '18px' }} 
                            />
                            <Statistic 
                                title={<Text className="!text-gray-300">情感分值</Text>} 
                                value={0} 
                                prefix={<TrendingUp size={16} />} 
                                valueStyle={{ color: '#fff', fontSize: '18px' }} 
                            />
                        </div>
                        
                        <Title level={5} className="!text-white" style={{ marginTop: '24px' }}>快速操作</Title>
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                            <Button 
                                type="primary" 
                                icon={<Brain size={16} />} 
                                onClick={handleAnalyze}
                                loading={loading}
                                style={{ width: '100%' }}
                            >
                                开始分析
                            </Button>
                            <Button 
                                icon={<BarChart3 size={16} />} 
                                style={{ width: '100%' }} 
                                disabled
                            >
                                生成报告
                            </Button>
                        </div>
                    </GlassmorphicCard>
                </Sider>
                
                <Content style={{ position: 'relative' }}>
                    <GlassmorphicCard style={{ padding: 0, height: '100%' }}>
                        <div style={{ 
                            padding: '48px', 
                            height: '100%', 
                            display: 'flex', 
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            textAlign: 'center'
                        }}>
                            <Brain size={64} style={{ color: '#22d3ee', marginBottom: '24px' }} />
                            <Title level={3} className="!text-white" style={{ marginBottom: '16px' }}>
                                舆情分析工具
                            </Title>
                            <Paragraph style={{ 
                                color: 'rgba(255, 255, 255, 0.7)', 
                                fontSize: '16px',
                                maxWidth: '400px',
                                lineHeight: 1.6
                            }}>
                                智能分析网络舆情，生成专业舆情报告。
                                该功能正在开发中，敬请期待。
                            </Paragraph>
                        </div>
                    </GlassmorphicCard>
                </Content>
            </Layout>
        </div>
    );
};