import{j as n,r as z,Q as Y,V as Q,X as U}from"./react-vendor-DnpxqCDv.js";import{c as N,d as Z,a as q,f as X,b as J}from"./cytoscape-DSQcBgoQ.js";import{d as K,i as O,T as P,D as ee,y as te,B as A,a as W,G as oe}from"./antd-DnRyuF5C.js";const{Text:B}=P,re=[{type:"company",label:"公司/企业",color:"#3b82f6"},{type:"partnership",label:"有限合伙",color:"#10b981"},{type:"person",label:"个人",color:"#8b5cf6"}],ne=[{color:"#64748b",style:"solid",label:"有效数据"},{color:"#ef4444",style:"dashed",label:"数据异常"}],se=({color:e})=>{const o={width:"12px",height:"12px",backgroundColor:e,border:"1px solid rgba(255, 255, 255, 0.6)",marginRight:"8px",flexShrink:0,borderRadius:"50%"};return n.jsx("div",{style:o})},ae=({color:e,style:o})=>{const r={width:"20px",height:"2px",backgroundColor:e,marginRight:"8px",flexShrink:0,borderStyle:o==="dashed"?"dashed":"solid",borderWidth:o==="dashed"?"1px 0":"0",borderColor:e,background:o==="dashed"?"transparent":e};return n.jsx("div",{style:r})},ie=()=>n.jsx(K,{size:"small",bordered:!1,style:{background:"rgba(30, 41, 59, 0.7)",backdropFilter:"blur(8px)",border:"1px solid rgba(255, 255, 255, 0.15)",borderRadius:"8px 8px 0 0",margin:0},bodyStyle:{padding:"8px",margin:0},children:n.jsxs(O,{direction:"vertical",size:4,children:[n.jsx(B,{strong:!0,style:{color:"#f1f5f9",fontSize:12,marginBottom:"2px"},children:"图例"}),re.map(e=>n.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"2px"},children:[n.jsx(se,{color:e.color}),n.jsx(B,{style:{fontSize:11,color:"#cbd5e1"},children:e.label})]},e.type)),n.jsx(ee,{style:{margin:"4px 0 2px 0",borderColor:"rgba(255, 255, 255, 0.2)"}}),n.jsx(B,{strong:!0,style:{color:"#f1f5f9",fontSize:11,marginBottom:"2px"},children:"数据质量"}),ne.map((e,o)=>n.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"2px"},children:[n.jsx(ae,{color:e.color,style:e.style}),n.jsx(B,{style:{fontSize:11,color:"#cbd5e1"},children:e.label})]},o))]})});N.use(Z);N.use(q);N.use(X);N.use(J);const E=e=>{if(!e||e==="-")return-1;const o=e.toString().replace(/[%\s]/g,""),r=parseFloat(o);return isNaN(r)||r<0||r>=1900&&r<=2100?-1:r>100?r>1e4?-1:r/100:r},D={dagre:{name:"dagre",rankDir:"TB",spacingFactor:1.2,nodeDimensionsIncludeLabels:!0,animate:!0,animationDuration:500,fit:!0,padding:20},fcose:{name:"fcose",quality:"proof",animate:!0,animationDuration:2e3,fit:!0,padding:20,gravity:.3,gravityRangeCompound:3,gravityCompound:.2,gravityRange:8,initialEnergyOnIncremental:.8,nodeSeparation:200,piTol:1e-7,idealEdgeLength:e=>{try{const o=e.source(),r=e.target();if(!o||!r)return 200;const x=o.degree()||0,h=r.degree()||0,k=Math.max(x,h),y=Math.min(x,h);let f=200;return x>=5&&h>=5?f=600+Math.random()*200:k>=15?y===1?f=100+Math.random()*80:y>=2&&y<=4?f=250+Math.random()*60:f=400+Math.random()*100:x>=2&&h>=2?f=100+(x+h)/2*30+Math.random()*50:x===1&&h===1?f=10+Math.random()*60:f=80+Math.random()*60,f}catch{return 200}},nodeRepulsion:e=>{try{if(!e)return 15e3;const o=e.degree()||0;let r=15e3;return o>=15?r=1e5+o*5e3:o>=5?r=6e4+o*4e3:o>=2?r=18e3+o*2e3:r=6e3,r}catch{return 4e3}}},"cose-bilkent":{name:"cose-bilkent",animate:"end",animationDuration:800,gravity:.1,fit:!0,padding:20,nodeRepulsion:15e3,idealEdgeLength:150},concentric:{name:"concentric",concentric:e=>{const o=e.data("degree")||e.degree()||0;return o>=15?100:o>=5?80:o>=2?60:40-Math.random()*20},levelWidth:e=>{const o=e.data("degree")||e.degree()||0;return o>=15?1:o>=5?2:o>=2?3:4},minNodeSpacing:150,animate:!0,animationDuration:800,fit:!0,padding:20},avsdf:{name:"avsdf",nodeSeparation:120,fit:!0,padding:20}},le=[{selector:"node",style:{"font-family":"sans-serif",label:"data(name)",color:"#e2e8f0","font-size":14,"text-valign":"bottom","text-halign":"center","text-margin-y":8,shape:"ellipse",width:e=>{const o=e.data("node_size")||1e4,r=Math.log10(Math.max(1,o))*15;return Math.max(40,Math.min(120,r))},height:e=>{const o=e.data("node_size")||1e4,r=Math.log10(Math.max(1,o))*15;return Math.max(40,Math.min(120,r))},"transition-property":"background-color, border-color, width, height","transition-duration":"0.3s","text-background-color":"rgba(15, 23, 42, 0.7)","text-background-opacity":1,"text-background-padding":"4px","text-background-shape":"round-rectangle","border-width":2,"border-color":"#475569"}},{selector:'node[type="company"]',style:{"background-color":"#3b82f6","border-color":"#1d4ed8"}},{selector:'node[type="listed"]',style:{"background-color":"#f59e0b","border-color":"#b45309"}},{selector:'node[type="partnership"]',style:{"background-color":"#10b981","border-color":"#047857"}},{selector:'node[type="person"]',style:{"background-color":"#8b5cf6","border-color":"#6d28d9"}},{selector:"edge",style:{width:e=>{var r;const o=E((r=e.data("properties"))==null?void 0:r.percentage);return o===-1?2:Math.max(1,o/100*8)},"line-color":e=>{var r;return E((r=e.data("properties"))==null?void 0:r.percentage)===-1?"#ef4444":"#64748b"},"line-style":e=>{var r;return E((r=e.data("properties"))==null?void 0:r.percentage)===-1?"dashed":"solid"},"target-arrow-shape":"triangle","target-arrow-color":e=>{var r;return E((r=e.data("properties"))==null?void 0:r.percentage)===-1?"#ef4444":"#64748b"},"curve-style":"unbundled-bezier","control-point-distances":"20 -20","control-point-weights":"0.25 0.75","transition-property":"line-color, target-arrow-color, width","transition-duration":"0.3s",opacity:.7}},{selector:'edge[type="control"]',style:{"line-color":"#f43f5e","target-arrow-color":"#f43f5e","line-style":"solid"}},{selector:'edge[type="holding"]',style:{"line-color":"#eab308","target-arrow-color":"#eab308","line-style":"dashed"}},{selector:'edge[type="partnership"]',style:{"line-color":"#0ea5e9","target-arrow-color":"#0ea5e9","line-style":"dotted"}},{selector:"node:selected",style:{"border-width":5,"border-color":"#f43f5e","z-index":99}},{selector:".highlighted",style:{"border-color":"#38bdf8","border-width":4,"z-index":99}},{selector:".faded",style:{opacity:.15,"transition-duration":"0.2s"}},{selector:".threshold-hidden",style:{display:"none"}},{selector:".relationship-hidden",style:{display:"none"}},{selector:".tag-highlighted",style:{"border-width":6,"border-color":"#faad14","border-style":"solid","z-index":99}}],he=({data:e,onNodeClick:o,onNodeExpand:r,height:x="100%",searchProps:h,equityThreshold:k=0,relationshipFilter:y={showShareholders:!0,showInvestments:!0},selectedTags:f=new Set,onVisibleNodesChange:T})=>{const $=z.useRef(null),b=z.useRef(null),[I,F]=z.useState("fcose"),[j,L]=z.useState(null),C=(h==null?void 0:h.value)||"";z.useEffect(()=>{if(!(!$.current||!e.nodes.length))try{const s=e.nodes.filter(t=>t.id&&t.name),a=e.edges.filter(t=>t.source&&t.target);if(s.length===0)return;const i=s.map(t=>({...t,id:String(t.id)})),c=a.map(t=>({source:String(t.source),target:String(t.target),type:t.type,properties:t.properties})),l=new Map;i.forEach(t=>{const m=c.filter(u=>u.source===t.id||u.target===t.id).length;l.set(t.id,m)});const v=i.map(t=>({group:"nodes",data:{...t,...t.properties,degree:l.get(t.id)||0}})),g=c.map((t,m)=>({group:"edges",data:{id:`${t.source}->${t.target}-${m}`,source:t.source,target:t.target,type:t.type,properties:t.properties}})),S=[...v,...g],p=D[I]||D.dagre;b.current=N({container:$.current,elements:S,style:le,layout:p});const d=b.current;return d.on("tap","node",t=>{const u=t.target.data();d.elements().removeClass("faded"),d.getElementById(u.id).neighborhood().addClass("highlighted"),o&&o(u)}),d.on("mouseover","node",t=>{const m=t.target,u=m.data(),w=t.renderedPosition;document.body.style.cursor="pointer";const R=m.neighborhood();d.elements().addClass("faded"),m.removeClass("faded").addClass("highlighted"),R.removeClass("faded");const M=`
          <div style="font-weight: bold; margin-bottom: 4px; color: #f1f5f9;">${u.name}</div>
          <div style="color: #cbd5e1;">法定代表人: ${u.法定代表人||"N/A"}</div>
          <div style="color: #cbd5e1;">注册资本: ${u.注册资本||"N/A"}</div>
        `;L({content:M,x:w.x,y:w.y,visible:!0})}),d.on("mouseover","edge",t=>{var G;const u=t.target.data(),w=t.renderedPosition,R=(G=u.properties)==null?void 0:G.percentage,M=E(R),V=`
          <div style="font-weight: bold; margin-bottom: 4px; color: #f1f5f9;">股权关系</div>
          <div style="color: #cbd5e1;">持股比例: ${M===-1?"数据异常":M.toFixed(2)+"%"}</div>
          ${M===-1?`<div style="color: #fbbf24; font-size: 11px; margin-top: 2px;">原值: "${R}"</div>`:""}
        `;L({content:V,x:w.x,y:w.y,visible:!0})}),d.on("mouseout","node, edge",()=>{d.elements().removeClass("faded highlighted"),document.body.style.cursor="default",L(t=>t?{...t,visible:!1}:null)}),d.on("pan zoom",()=>{L(null)}),()=>{try{d==null||d.destroy()}catch{}}}catch(s){console.error("Error initializing cytoscape:",s)}},[e,o,r,I]),z.useEffect(()=>{const s=b.current;if(!(!s||!s.elements().length))try{if(s.elements().removeClass("threshold-hidden relationship-hidden"),!y.showShareholders||!y.showInvestments){let i=s.nodes().filter(c=>(c.data("name")||c.data("企业名称"))===C);if(i.length===0&&C&&(i=s.nodes().filter(c=>{const l=c.data("name")||c.data("企业名称");return l&&(l.includes(C)||C.includes(l))})),i.length===0){let c=0,l=null;s.nodes().forEach(v=>{const g=v.degree();g>c&&(c=g,l=v)}),l&&(i=s.collection([l]))}if(i.length>0){const c=i[0],l=c.id();let v=!1;if(s.edges().forEach(g=>{const S=g.source().id(),p=g.target().id();let d=!1;!y.showShareholders&&p===l&&(d=!0),!y.showInvestments&&S===l&&(d=!0),d&&(g.addClass("relationship-hidden"),v=!0)}),v){const g=new Set([l]),S=[c];for(;S.length>0;){const p=S.shift();p&&p.connectedEdges().forEach(d=>{if(!d.hasClass("relationship-hidden")){const t=d.source(),m=d.target(),u=t.id()===p.id()?m:t,w=u.id();g.has(w)||(g.add(w),S.push(u))}})}s.nodes().forEach(p=>{!g.has(p.id())&&!p.hasClass("relationship-hidden")&&p.addClass("relationship-hidden")}),s.edges().forEach(p=>{if(!p.hasClass("relationship-hidden")){const d=p.source().id(),t=p.target().id();(!g.has(d)||!g.has(t))&&p.addClass("relationship-hidden")}})}}}k>0&&s.edges().forEach(i=>{var c;if(!i.hasClass("relationship-hidden")){const l=E((c=i.data("properties"))==null?void 0:c.percentage);l!==-1&&l<k&&i.addClass("threshold-hidden")}}),s.nodes().forEach(i=>{i.connectedEdges().filter(l=>!l.hasClass("threshold-hidden")&&!l.hasClass("relationship-hidden")).length===0&&(i.data("name")===C||i.data("企业名称")===C||i.addClass("threshold-hidden"))});const a=s.nodes().filter(i=>!i.hasClass("threshold-hidden")&&!i.hasClass("relationship-hidden"));T&&T(a.length)}catch(a){console.error("关系过滤错误:",a)}},[k,y,C]),z.useEffect(()=>{const s=b.current;if(!(!s||!s.elements().length))try{s.nodes().removeClass("tag-highlighted"),f.size>0&&s.nodes().forEach(a=>{var c;const i=a.data("标签")||((c=a.data("properties"))==null?void 0:c.标签);i&&typeof i=="string"&&i.split("|").map(g=>g.trim()).filter(g=>g).some(g=>f.has(g))&&a.addClass("tag-highlighted")})}catch(a){console.error("标签高亮错误:",a)}},[f]),z.useEffect(()=>{if(b.current)try{const s=D[I]||D.dagre;b.current.layout(s).run()}catch(s){console.error("Error switching layout:",s);try{b.current.layout(D.dagre).run()}catch(a){console.error("Fallback layout also failed:",a)}}},[I]);const H=s=>{F(s)},_=s=>{const a=b.current;a&&(s==="zoomIn"&&a.zoom({level:a.zoom()*1.2,renderedPosition:{x:a.width()/2,y:a.height()/2}}),s==="zoomOut"&&a.zoom({level:a.zoom()/1.2,renderedPosition:{x:a.width()/2,y:a.height()/2}}),s==="fit"&&a.fit(void 0,50))};return n.jsxs("div",{style:{position:"relative",width:"100%",height:x,minHeight:480,overflow:"hidden",margin:0,padding:0,display:"flex",flexDirection:"column"},children:[n.jsx("div",{ref:$,style:{width:"100%",height:"100%",background:"transparent",margin:0,padding:0,flex:1}}),j&&n.jsx("div",{dangerouslySetInnerHTML:{__html:j.content},style:{position:"absolute",left:j.x+20,top:j.y-10,background:"rgba(15, 23, 42, 0.85)",color:"white",padding:"8px 12px",borderRadius:"6px",border:"1px solid rgba(255, 255, 255, 0.2)",backdropFilter:"blur(4px)",zIndex:100,pointerEvents:"none",fontSize:"13px",maxWidth:"300px",transition:"opacity 0.2s, transform 0.2s",opacity:j.visible?1:0,transform:j.visible?"translateY(0)":"translateY(10px)"}}),n.jsxs("div",{style:{position:"absolute",top:16,left:16,right:16,zIndex:10},children:[n.jsx("div",{style:{display:"flex",alignItems:"center",gap:"12px",marginBottom:"12px",flexWrap:"wrap"},children:h&&n.jsxs(n.Fragment,{children:[n.jsx(te,{style:{width:"min(280px, calc(100% - 100px))",minWidth:"200px"},options:h.options,onSelect:h.onSelect,onSearch:h.onSearch,placeholder:"输入公司名进行查询",value:h.value}),n.jsx(A,{type:"primary",onClick:()=>h.onSelect(h.value),loading:h.loading,style:{flexShrink:0},children:"查询"})]})}),n.jsx("div",{style:{display:"flex",justifyContent:"flex-end",alignItems:"center",gap:"8px",flexWrap:"wrap"},children:n.jsxs(O,{size:"small",wrap:!0,children:[n.jsx(W,{title:"放大",children:n.jsx(A,{ghost:!0,size:"small",icon:n.jsx(Y,{size:14}),onClick:()=>_("zoomIn")})}),n.jsx(W,{title:"缩小",children:n.jsx(A,{ghost:!0,size:"small",icon:n.jsx(Q,{size:14}),onClick:()=>_("zoomOut")})}),n.jsx(W,{title:"适应屏幕",children:n.jsx(A,{ghost:!0,size:"small",icon:n.jsx(U,{size:14}),onClick:()=>_("fit")})}),n.jsx(oe,{value:I,onChange:H,size:"small",style:{width:"min(140px, calc(100vw - 200px))",minWidth:"120px"},options:[{value:"fcose",label:"智能防重叠"},{value:"concentric",label:"星型同心圆"},{value:"dagre",label:"层级树状"},{value:"cose-bilkent",label:"经典力导向"},{value:"avsdf",label:"对称布局"}]})]})})]}),n.jsx("div",{style:{position:"absolute",bottom:0,left:8,zIndex:10},children:n.jsx(ie,{})})]})};export{he as default};
//# sourceMappingURL=AdvancedEquityGraph-B6dIjYW0.js.map
