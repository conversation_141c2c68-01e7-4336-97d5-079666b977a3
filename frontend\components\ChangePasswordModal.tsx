import React, { useState } from 'react';
import { Modal, Form, Input, Button, message } from 'antd';
import { Lock } from 'lucide-react';
import { api } from '@/services/api';
import useAuthStore from '@/store/auth';

export const ChangePasswordModal: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { passwordChanged, logout } = useAuthStore();

  const handleFinish = async (values: any) => {
    if (values.newPassword !== values.confirmPassword) {
      message.error('两次输入的新密码不一致!');
      return;
    }
    setLoading(true);
    try {
      const response = await api.post('/user/change-password', {
        oldPassword: values.oldPassword,
        newPassword: values.newPassword,
      });
      if (response.success) {
        message.success('密码修改成功！');
        passwordChanged(); // This will close the modal via state change in App.tsx
      } else {
        message.error(response.message || '密码修改失败，请检查您的旧密码是否正确。');
      }
    } catch (error) {
      message.error('密码修改失败，发生网络错误。');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="首次登录 - 请修改密码"
      open={true}
      closable={false}
      maskClosable={false}
      keyboard={false}
      footer={[
        <Button key="logout" onClick={logout}>
          退出登录
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={() => form.submit()}>
          确认修改
        </Button>,
      ]}
    >
      <p>为了您的账户安全，首次登录系统需要修改初始密码。</p>
      <Form form={form} layout="vertical" onFinish={handleFinish} requiredMark={false} style={{ marginTop: '24px' }}>
        <Form.Item
          name="oldPassword"
          label="旧密码"
          rules={[{ required: true, message: '请输入您的当前密码' }]}
        >
          <Input.Password prefix={<Lock size={16} />} placeholder="请输入旧密码" />
        </Form.Item>
        <Form.Item
          name="newPassword"
          label="新密码"
          rules={[{ required: true, message: '请输入新密码' }, { min: 6, message: '密码长度至少为6位' }]}
        >
          <Input.Password prefix={<Lock size={16} />} placeholder="请输入新密码" />
        </Form.Item>
        <Form.Item
          name="confirmPassword"
          label="确认新密码"
          dependencies={['newPassword']}
          rules={[
            { required: true, message: '请再次输入新密码' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('newPassword') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次输入的密码不一致!'));
              },
            }),
          ]}
        >
          <Input.Password prefix={<Lock size={16} />} placeholder="请确认新密码" />
        </Form.Item>
      </Form>
    </Modal>
  );
}; 