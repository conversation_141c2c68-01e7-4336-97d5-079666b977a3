from fastapi import APIRouter, HTTPException, Depends, Query
import logging
import time
from typing import Dict, Any
from datetime import datetime

from ..models.schemas import (
    EquityPenetrationRequest, EquityPenetrationResponse,
    PledgeAnalysisRequest, PledgeAnalysisResponse,
    AnalysisResponse
)
from ..services.equity_service import EquityService
from ..services.graph_service import GraphService
from ..dependencies import get_equity_service, get_graph_service

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/equity-penetration", response_model=EquityPenetrationResponse)
async def analyze_equity_penetration(
    equity_service: EquityService = Depends(get_equity_service),
    company_id: str = Query(..., description="公司名称"),
    up_depth: int = Query(2, description="向上查询深度"),
    down_depth: int = Query(2, description="向下查询深度")
):
    """股权穿透分析"""
    start_time = time.time()
    
    try:
        logger.info(f"Starting equity penetration analysis for company: {company_id}")
        
        graph_data = await equity_service.get_equity_penetration_graph(
            company_name=company_id,
            up_depth=up_depth,
            down_depth=down_depth
        )
        
        processing_time = time.time() - start_time
        logger.info(f"Equity penetration analysis completed in {processing_time:.2f}s")
        
        # Note: The response model might need adjustment if it doesn't match the new flat structure.
        # For now, we wrap the flat data in a structure that resembles the old response.
        return EquityPenetrationResponse(
            company_name=company_id,
            graph_data=graph_data,
            analysis_depth=max(up_depth, down_depth),
            analysis_time=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Equity penetration analysis error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to analyze equity penetration: {str(e)}"
        )


@router.get("/expand-node/{node_id}")
async def expand_node_data(
    node_id: str,
    direction: str = Query(..., description="扩展方向: 'up' 或 'down'"),
    equity_service: EquityService = Depends(get_equity_service)
):
    """获取节点的扩展数据"""
    if direction not in ["up", "down"]:
        raise HTTPException(status_code=400, detail="无效的扩展方向")
    
    try:
        logger.info(f"Expanding node {node_id} in direction {direction}")
        graph_data = await equity_service.get_expanded_graph_data(node_id, direction)
        return graph_data
    except Exception as e:
        logger.error(f"Node expansion error for {node_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to expand node: {str(e)}"
        )


@router.get("/search-companies")
async def search_companies(
    keyword: str,
    graph_service: GraphService = Depends(get_graph_service)
):
    """搜索公司"""
    try:
        companies = await graph_service.search_companies(keyword)
        return {"suggestions": companies}
    except Exception as e:
        logger.error(f"Search companies error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to search companies: {str(e)}"
        )


@router.post("/equity-pledge", response_model=PledgeAnalysisResponse)
async def analyze_equity_pledge(
    request: PledgeAnalysisRequest,
    equity_service: EquityService = Depends(get_equity_service)
):
    """股权质押分析"""
    start_time = time.time()
    
    try:
        logger.info(f"Starting equity pledge analysis for company: {request.company_code}")
        
        # 执行股权质押分析
        result = await equity_service.analyze_pledge(
            company_code=request.company_code,
            include_subsidiaries=request.include_subsidiaries,
            date_range=request.date_range
        )
        
        processing_time = time.time() - start_time
        logger.info(f"Equity pledge analysis completed in {processing_time:.2f}s")
        
        return result
        
    except Exception as e:
        logger.error(f"Equity pledge analysis error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to analyze equity pledge: {str(e)}"
        )


@router.get("/company/{company_code}/info")
async def get_company_basic_info(
    company_code: str,
    equity_service: EquityService = Depends(get_equity_service)
):
    """获取公司基本信息"""
    try:
        company_info = await equity_service.get_company_info(company_code)
        
        return AnalysisResponse(
            success=True,
            message="Company information retrieved successfully",
            data={"company": company_info}
        )
        
    except Exception as e:
        logger.error(f"Get company info error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get company information: {str(e)}"
        )


@router.get("/company/{company_code}/shareholders")
async def get_company_shareholders(
    company_code: str,
    top_n: int = 10,
    equity_service: EquityService = Depends(get_equity_service)
):
    """获取公司股东信息"""
    try:
        shareholders = await equity_service.get_shareholders(company_code, top_n)
        
        return AnalysisResponse(
            success=True,
            message="Shareholders information retrieved successfully",
            data={
                "company_code": company_code,
                "shareholders": shareholders,
                "total_count": len(shareholders)
            }
        )
        
    except Exception as e:
        logger.error(f"Get shareholders error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get shareholders: {str(e)}"
        )


@router.get("/company/{company_code}/investments")
async def get_company_investments(
    company_code: str,
    include_indirect: bool = False,
    equity_service: EquityService = Depends(get_equity_service)
):
    """获取公司对外投资信息"""
    try:
        investments = await equity_service.get_investments(company_code, include_indirect)
        
        return AnalysisResponse(
            success=True,
            message="Investment information retrieved successfully",
            data={
                "company_code": company_code,
                "investments": investments,
                "include_indirect": include_indirect
            }
        )
        
    except Exception as e:
        logger.error(f"Get investments error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get investments: {str(e)}"
        )


@router.get("/company/{company_code}/control-chain")
async def get_control_chain(
    company_code: str,
    equity_service: EquityService = Depends(get_equity_service),
    graph_service: GraphService = Depends(get_graph_service)
):
    """获取公司控制链"""
    try:
        control_chain = await equity_service.get_control_chain(
            company_code, 
            graph_service
        )
        
        return AnalysisResponse(
            success=True,
            message="Control chain retrieved successfully",
            data={
                "company_code": company_code,
                "control_chain": control_chain
            }
        )
        
    except Exception as e:
        logger.error(f"Get control chain error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get control chain: {str(e)}"
        )


@router.post("/batch-analysis")
async def batch_equity_analysis(
    company_codes: list[str],
    analysis_type: str = "penetration",
    equity_service: EquityService = Depends(get_equity_service)
):
    """批量股权分析"""
    try:
        if len(company_codes) > 50:
            raise HTTPException(
                status_code=400,
                detail="Maximum 50 companies allowed for batch analysis"
            )
        
        logger.info(f"Starting batch {analysis_type} analysis for {len(company_codes)} companies")
        
        results = await equity_service.batch_analysis(company_codes, analysis_type)
        
        return AnalysisResponse(
            success=True,
            message=f"Batch {analysis_type} analysis completed",
            data={
                "analysis_type": analysis_type,
                "company_count": len(company_codes),
                "results": results
            }
        )
        
    except Exception as e:
        logger.error(f"Batch equity analysis error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to perform batch analysis: {str(e)}"
        )