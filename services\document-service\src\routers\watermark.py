from fastapi import APIRouter, HTTPException
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/")
async def watermark_root():
    """水印服务根端点"""
    return {
        "service": "Watermark Processing",
        "status": "available", 
        "features": ["批量加水印", "图片水印", "文档水印"]
    }


@router.post("/batch")
async def batch_watermark():
    """批量水印处理"""
    # TODO: 实现批量水印功能
    raise HTTPException(status_code=501, detail="水印功能开发中")
