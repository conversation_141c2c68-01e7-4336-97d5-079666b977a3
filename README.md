# IDEALAB应用实验室

## 🚀 项目简介

IDEALAB应用实验室是一个集成了多种智能化工具的现代化Web应用平台。该平台采用React + TypeScript技术栈，提供美观的用户界面和强大的功能模块，旨在为用户提供一站式的智能化工具服务。

### ✨ 主要特性

- 🎨 现代化渐变背景设计，支持响应式布局
- 🔐 安全的用户认证系统
- 🔍 智能工具搜索和分类筛选
- 🤖 AI驱动的多种专业工具
- 📱 多平台兼容，移动端友好
- ⚡ 高性能，快速响应

## 🛠️ 技术栈

### 前端框架
- **React 18** - 现代化UI框架
- **TypeScript** - 类型安全的JavaScript
- **Vite** - 快速构建工具

### UI组件库
- **Ant Design 5** - 企业级UI设计语言
- **Tailwind CSS** - 原子化CSS框架
- **Lucide React** - 美观的图标库

### 路由与状态管理
- **React Router** - 客户端路由
- **Zustand** - 轻量级状态管理

### 开发工具
- **ESLint** - 代码质量检查
- **PostCSS** - CSS处理工具
- **Autoprefixer** - 自动添加CSS前缀

## 🎯 核心功能模块

### 1. 对话机器人 🤖
- 智能AI对话助手
- 支持多轮对话
- 实时消息流式展示
- 知识问答和任务执行

### 2. 舆情报告生成 📊
- 网络舆情数据分析
- 情感倾向自动识别
- 关键词提取和统计
- 专业报告自动生成

### 3. 股权穿透工具 🔍
- 企业股权结构分析
- 多层级股权关系展示
- 穿透式股权图谱
- 投资风险评估

### 4. 股权质押分析工具 📈
- 股权质押情况查询
- 质押风险等级评估
- 质押比例趋势分析
- 预警机制和提醒

### 5. 债券市场分析工具 💰
- 债券市场数据分析
- 收益率曲线展示
- 投资组合优化建议
- 风险评估和预警

### 6. 批量OCR 📝
- 图片文字批量识别
- 支持多种图片格式
- PDF文档文字提取
- 高精度识别引擎

### 7. 批量加水印 🏷️
- 图片批量水印添加
- 自定义水印样式
- 位置和透明度调节
- 批量下载处理结果

### 8. Excel暴力合并 📋
- 多Excel文件智能合并
- 工作表自动对齐
- 数据去重和清理
- 格式统一处理

## 📦 安装和运行

### 系统架构
本项目采用微服务架构，包含以下组件：
- **前端应用**: React + TypeScript + Vite
- **API网关**: Node.js + Express (端口: 8000)
- **认证服务**: Node.js + Express + MongoDB (端口: 8001) 
- **NLP服务**: Python + FastAPI (端口: 8002)
- **金融服务**: Python + FastAPI + Neo4j (端口: 8003)
- **文档处理服务**: Python + FastAPI + MinIO (端口: 8004)
- **数据库**: PostgreSQL, MongoDB, Neo4j, Redis

### 环境要求
- Docker >= 20.0.0
- Docker Compose >= 2.0.0
- Node.js >= 16.0.0 (用于前端开发)
- Python >= 3.9 (用于后端开发)

### 快速启动 (推荐)
```bash
# 1. 复制环境变量配置文件
cp .env.example .env

# 2. 编辑 .env 文件，填入必要的环境变量
# 如：JWT_SECRET, OPENAI_API_KEY, 数据库密码等

# 3. 使用快速启动脚本启动所有服务
chmod +x scripts/start.sh
./scripts/start.sh start

# 4. 查看服务状态
./scripts/start.sh status
```

### Docker Compose 启动
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 单独启动前端 (开发模式)
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 单独启动后端服务 (开发模式)

#### 认证服务
```bash
cd services/auth
npm install
npm run dev
```

#### NLP服务
```bash
cd services/nlp-service
poetry install
poetry run uvicorn main:app --reload --port 8002
```

#### 金融服务
```bash
cd services/financial-service
poetry install
poetry run uvicorn main:app --reload --port 8003
```

#### 文档处理服务
```bash
cd services/document-service
poetry install
poetry run uvicorn main:app --reload --port 8004
```

#### API网关
```bash
cd services/gateway
npm install
npm run dev
```

### 访问地址
- 前端应用: http://localhost:3000
- API网关: http://localhost:8000
- 认证服务: http://localhost:8001
- NLP服务: http://localhost:8002
- 金融服务: http://localhost:8003
- 文档处理服务: http://localhost:8004

### 代码质量检查
```bash
# 前端代码检查
cd frontend && npm run lint

# 后端代码检查 (Python服务)
cd services/nlp-service && poetry run flake8 src/
cd services/financial-service && poetry run flake8 src/
cd services/document-service && poetry run flake8 src/
```

## 🚀 部署指南

### 传统部署

1. **构建项目**
   ```bash
   npm run build
   ```

2. **上传构建文件**
   将 `dist` 目录中的所有文件上传到Web服务器

3. **配置Web服务器**
   - Apache: 配置 `.htaccess` 支持SPA路由
   - Nginx: 配置 `try_files` 指令

### Docker部署

1. **创建 Dockerfile**
   ```dockerfile
   FROM node:18-alpine AS builder
   WORKDIR /app
   COPY package*.json ./
   RUN npm ci
   COPY . .
   RUN npm run build

   FROM nginx:alpine
   COPY --from=builder /app/dist /usr/share/nginx/html
   COPY nginx.conf /etc/nginx/nginx.conf
   EXPOSE 80
   CMD ["nginx", "-g", "daemon off;"]
   ```

2. **构建和运行**
   ```bash
   docker build -t idealab-app .
   docker run -p 80:80 idealab-app
   ```

### 云平台部署

#### Vercel
```bash
npm install -g vercel
vercel --prod
```

#### Netlify
```bash
npm install -g netlify-cli
npm run build
netlify deploy --prod --dir=dist
```

## 🔧 配置说明

### 环境变量
创建 `.env` 文件设置环境变量：
```env
VITE_API_BASE_URL=https://api.idealab.com
VITE_APP_TITLE=IDEALAB应用实验室
VITE_APP_VERSION=1.0.0
```

### 自定义主题
在 `tailwind.config.js` 中修改主题配置：
```javascript
theme: {
  extend: {
    colors: {
      primary: {
        50: '#eff6ff',
        500: '#3b82f6',
        // 自定义颜色
      }
    }
  }
}
```

## 🔐 认证系统

### 默认账号
- **用户名**: admin
- **密码**: admin123

### 自定义认证
修改 `src/store/auth.ts` 实现自定义认证逻辑：
```typescript
login: async (username: string, password: string) => {
  // 实现自定义登录逻辑
  const response = await api.login(username, password);
  // 处理响应...
}
```

## 📁 项目结构

```
src/
├── components/          # 通用组件
├── pages/              # 页面组件
│   ├── LoginPage.tsx   # 登录页面
│   ├── DashboardPage.tsx # 主控制台
│   └── tools/          # 工具页面
├── store/              # 状态管理
├── types/              # TypeScript类型定义
├── utils/              # 工具函数
├── services/           # API服务
└── index.css          # 全局样式
```

## 🔧 API接口

### 认证接口
```typescript
POST /api/auth/login
{
  "username": "string",
  "password": "string"
}
```

### 工具接口
```typescript
// OCR识别
POST /api/tools/ocr
Content-Type: multipart/form-data

// 舆情分析
POST /api/tools/sentiment-analysis
{
  "data": "string[]",
  "options": {}
}
```

## 🧪 测试

### 单元测试
```bash
npm run test
```

### E2E测试
```bash
npm run test:e2e
```

## 📱 移动端支持

- 响应式设计，自适应各种屏幕尺寸
- 触摸友好的交互设计
- PWA支持（可选）

## 🌐 浏览器兼容性

- Chrome >= 80
- Firefox >= 75
- Safari >= 13
- Edge >= 80

## 🔄 更新记录

### v1.0.0 (2024-01-15)
- ✨ 初始版本发布
- 🎨 实现渐变背景登录页面
- 🔧 集成8个核心工具模块
- 📱 响应式设计优化

## 🤝 贡献指南

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开 Pull Request

## 📄 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 支持与联系

- 📧 邮箱: <EMAIL>
- 🌐 官网: https://idealab.com
- 📚 文档: https://docs.idealab.com
- 🐛 Bug报告: https://github.com/idealab/issues

---

**IDEALAB应用实验室** - 让智能化工具触手可及 🚀