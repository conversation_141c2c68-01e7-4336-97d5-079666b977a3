// 股权分析相关的类型定义
export interface BaseEquityNode {
  id: string;
  name: string;
  type: 'parent' | 'subsidiary' | 'holding' | 'partner' | 'person';
  percentage: number;
  registeredCapital?: string;
  legalPerson?: string;
  level: number;
}

export interface EquityNode extends BaseEquityNode {
  children?: EquityNode[];
  centrality?: NodeCentrality;
  community?: number;
  importance?: number;
  internalConnections?: number;
  externalConnections?: number;
}

export interface NodeCentrality {
  betweenness: number;
  closeness: number;
  degree: number;
  pageRank: number;
  eigenvector?: number;
}

export interface CompanyInfo {
  name: string;
  code: string;
  type: string;
  registeredCapital: string;
  shareholdingRatio: string;
  legalPerson: string;
}

export interface Community {
  id: number;
  nodes: EquityNode[];
  size: number;
  density: number;
  totalEquity: number;
  avgEquity: number;
  dominantType: string;
  centralNode?: EquityNode;
  cohesion: number;
  influence: number;
}

export interface GraphStats {
  nodeCount: number;
  edgeCount: number;
  avgDegree: number;
  diameter: number;
  density: number;
}

export interface AnalysisStats {
  totalNodes: number;
  totalEdges: number;
  maxDepth: number;
  totalEquity: number;
  controllingStakes: number;
  communities: number;
}

// 节点角色类型
export type NodeRole = 'key_player' | 'bridge' | 'influencer' | 'connector';

// 中心性分析排序类型
export type CentralitySortType = 'betweenness' | 'closeness' | 'degree' | 'pageRank';

// 社群分析排序类型
export type CommunitySortType = 'size' | 'density' | 'totalEquity' | 'influence';

// 洞察类型
export interface NetworkInsight {
  type: 'control' | 'vulnerability' | 'influence' | 'structure';
  title: string;
  description: string;
  level: 'high' | 'medium' | 'low';
  nodes: string[];
  recommendation?: string;
}