{"version": 3, "file": "AdvancedEquityGraph-B6dIjYW0.js", "sources": ["../../components/GraphLegend.tsx", "../../components/AdvancedEquityGraph.tsx"], "sourcesContent": ["import React from 'react';\nimport { Card, Typography, Space, Divider } from 'antd';\n\nconst { Text } = Typography;\n\nconst LEGEND_DATA = [\n    { type: 'company', label: '公司/企业', color: '#3b82f6' },\n    { type: 'partnership', label: '有限合伙', color: '#10b981' },\n    { type: 'person', label: '个人', color: '#8b5cf6' },\n];\n\nconst DATA_QUALITY_LEGEND = [\n    { color: '#64748b', style: 'solid', label: '有效数据' },\n    { color: '#ef4444', style: 'dashed', label: '数据异常' }\n];\n\nconst ShapePreview: React.FC<{ color: string; }> = ({ color }) => {\n    const style: React.CSSProperties = {\n        width: '12px',\n        height: '12px',\n        backgroundColor: color,\n        border: '1px solid rgba(255, 255, 255, 0.6)',\n        marginRight: '8px',\n        flexShrink: 0,\n        borderRadius: '50%', // All nodes are circles now\n    };\n\n    return <div style={style} />;\n};\n\nconst LinePreview: React.FC<{ color: string; style: string; }> = ({ color, style }) => {\n    const lineStyle: React.CSSProperties = {\n        width: '20px',\n        height: '2px',\n        backgroundColor: color,\n        marginRight: '8px',\n        flexShrink: 0,\n        borderStyle: style === 'dashed' ? 'dashed' : 'solid',\n        borderWidth: style === 'dashed' ? '1px 0' : '0',\n        borderColor: color,\n        background: style === 'dashed' ? 'transparent' : color,\n    };\n\n    return <div style={lineStyle} />;\n};\n\nexport const GraphLegend: React.FC = () => (\n    <Card\n        size=\"small\"\n        bordered={false}\n        style={{\n            background: 'rgba(30, 41, 59, 0.7)',\n            backdropFilter: 'blur(8px)',\n            border: '1px solid rgba(255, 255, 255, 0.15)',\n            borderRadius: '8px 8px 0 0',\n            margin: 0,\n        }}\n        bodyStyle={{ padding: '8px', margin: 0 }}\n    >\n        <Space direction=\"vertical\" size={4}>\n            <Text strong style={{ color: '#f1f5f9', fontSize: 12, marginBottom: '2px' }}>图例</Text>\n\n            {/* 节点类型 */}\n            {LEGEND_DATA.map(item => (\n                <div key={item.type} style={{ display: 'flex', alignItems: 'center', marginBottom: '2px' }}>\n                    <ShapePreview color={item.color} />\n                    <Text style={{ fontSize: 11, color: '#cbd5e1' }}>{item.label}</Text>\n                </div>\n            ))}\n\n            <Divider style={{ margin: '4px 0 2px 0', borderColor: 'rgba(255, 255, 255, 0.2)' }} />\n\n            {/* 数据质量 */}\n            <Text strong style={{ color: '#f1f5f9', fontSize: 11, marginBottom: '2px' }}>数据质量</Text>\n            {DATA_QUALITY_LEGEND.map((item, index) => (\n                <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: '2px' }}>\n                    <LinePreview color={item.color} style={item.style} />\n                    <Text style={{ fontSize: 11, color: '#cbd5e1' }}>{item.label}</Text>\n                </div>\n            ))}\n        </Space>\n    </Card>\n);", "import React, { useEffect, useRef, useState } from 'react';\nimport { <PERSON>ton, Tooltip, Select, Space, AutoComplete } from 'antd';\nimport { ZoomIn, ZoomOut, Maximize2 } from 'lucide-react';\nimport cytoscape from 'cytoscape';\nimport dagre from 'cytoscape-dagre';\nimport coseBilkent from 'cytoscape-cose-bilkent';\nimport fcose from 'cytoscape-fcose';\nimport avsdf from 'cytoscape-avsdf';\nimport { GraphLegend } from './GraphLegend';\n\n\n\ncytoscape.use(dagre);\ncytoscape.use(coseBilkent);\ncytoscape.use(fcose);\ncytoscape.use(avsdf);\n\n// The EquityNode interface is now simplified as detailed properties are handled by the backend.\nexport interface EquityNode {\n  id: string;\n  name: string;\n  type: 'company' | 'listed' | 'partnership' | 'person';\n  node_size: number;\n  properties: Record<string, any>;\n  expandable: {\n    up: boolean;\n    down: boolean;\n  };\n}\n\ninterface AdvancedEquityGraphProps {\n  data: {\n    nodes: EquityNode[];\n    edges: any[];\n  };\n  onNodeClick?: (node: EquityNode) => void;\n  onNodeExpand?: (nodeId: string, direction: 'up' | 'down') => void;\n  height?: number | string;\n  searchProps?: {\n    value: string;\n    options: { value: string }[];\n    onSearch: (value: string) => void;\n    onSelect: (value: string) => void;\n    loading?: boolean;\n  };\n  equityThreshold?: number;\n  relationshipFilter?: {\n    showShareholders: boolean;\n    showInvestments: boolean;\n  };\n  selectedTags?: Set<string>;\n  onVisibleNodesChange?: (visibleCount: number) => void;\n}\n\n// 数据清洗函数\nconst cleanStakeData = (input: any): number => {\n  if (!input || input === '-') return -1; // 用-1表示数据缺失\n\n  const str = input.toString().replace(/[%\\s]/g, '');\n  const num = parseFloat(str);\n\n  if (isNaN(num) || num < 0) return -1;           // 无效数据\n  if (num >= 1900 && num <= 2100) return -1;     // 年份数据\n  if (num > 100) return num > 10000 ? -1 : num/100; // 可能未转换的百分比\n\n  return num;\n};\n\nconst LAYOUT_CONFIGS = {\n  dagre: { name: 'dagre', rankDir: 'TB', spacingFactor: 1.2, nodeDimensionsIncludeLabels: true, animate: true, animationDuration: 500, fit: true, padding: 20 },\n  fcose: {\n    name: 'fcose',\n    quality: 'proof',\n    animate: true,\n    animationDuration: 2000, // 增加动画时间让布局更稳定\n    fit: true,\n    padding: 20, // 减少边距，让图形更贴近边缘\n    // 针对星型网络的特殊配置 - 大幅增强集团分离\n    gravity: 0.3, // 极低重力，几乎不向中心聚拢\n    gravityRangeCompound: 3.0,\n    gravityCompound: 0.2,\n    gravityRange: 8.0,\n    initialEnergyOnIncremental: 0.8,\n\n    // 强化分散效果\n    nodeSeparation: 200, // 大幅增加最小节点分离距离\n    piTol: 0.0000001,\n\n    // 重新设计边长：最大化集团间距离\n    idealEdgeLength: (edge: any) => {\n      try {\n        const sourceNode = edge.source();\n        const targetNode = edge.target();\n        if (!sourceNode || !targetNode) return 200;\n\n        const sourceDegree = sourceNode.degree() || 0;\n        const targetDegree = targetNode.degree() || 0;\n        const maxDegree = Math.max(sourceDegree, targetDegree);\n        const minDegree = Math.min(sourceDegree, targetDegree);\n\n        let edgeLength = 200;\n\n        // 高度数节点之间：超远距离，强制分离不同集团\n        if (sourceDegree >= 5 && targetDegree >= 5) {\n          edgeLength = 600 + Math.random() * 200; // 600-800px 超远距离\n        }\n        // 超高度数节点（中心）到其他节点：分层距离\n        else if (maxDegree >= 15) {\n          if (minDegree === 1) {\n            edgeLength = 100 + Math.random() * 80; // 150-230px\n          } else if (minDegree >= 2 && minDegree <= 4) {\n            edgeLength = 250 + Math.random() * 60; // 250-310px\n          } else {\n            edgeLength = 400 + Math.random() * 100; // 400-500px，高度数节点远离中心\n          }\n        }\n        // 中等度数节点之间：中等距离\n        else if (sourceDegree >= 2 && targetDegree >= 2) {\n          const avgDegree = (sourceDegree + targetDegree) / 2;\n          edgeLength = 100 + (avgDegree * 30) + Math.random() * 50;\n        }\n        // 低度数节点之间：适中距离，避免聚集\n        else if (sourceDegree === 1 && targetDegree === 1) {\n          edgeLength = 10 + Math.random() * 60; // 50-110px\n        }\n        // 其他情况\n        else {\n          edgeLength = 80 + Math.random() * 60; // 150-210px\n        }\n\n        return edgeLength;\n      } catch (e) {\n        return 200;\n      }\n    },\n\n    // 极大化节点斥力：强制集团分离\n    nodeRepulsion: (node: any) => {\n      try {\n        if (!node) return 15000;\n        const degree = node.degree() || 0;\n        let repulsion = 15000;\n\n        // 超高度数节点：极强斥力，成为独立的强中心\n        if (degree >= 15) {\n          repulsion = 100000 + (degree * 5000); // 100000-230000 极强斥力\n        }\n        // 高度数节点：超强斥力，形成独立集团中心\n        else if (degree >= 5) {\n          repulsion = 60000 + (degree * 4000); // 60000-116000 超强斥力\n        }\n        // 中度数节点：强斥力\n        else if (degree >= 2) {\n          repulsion = 18000 + (degree * 2000); // 25000-33000\n        }\n        // 低度数节点：适中斥力，防止重叠但允许聚集\n        else {\n          repulsion = 6000; // 适中斥力\n        }\n\n        return repulsion;\n      } catch (e) {\n        return 4000;\n      }\n    }\n  },\n  'cose-bilkent': {\n    name: 'cose-bilkent',\n    animate: 'end',\n    animationDuration: 800,\n    gravity: 0.1,\n    fit: true,\n    padding: 20,\n    nodeRepulsion: 15000,\n    idealEdgeLength: 150,\n  },\n  concentric: {\n    name: 'concentric',\n    concentric: (node: any) => {\n      // 专门为星型网络设计的同心圆布局\n      const degree = node.data('degree') || node.degree() || 0;\n\n      // 超高度数节点放在最中心\n      if (degree >= 15) return 100;\n      // 高度数节点在第二层\n      else if (degree >= 5) return 80;\n      // 中度数节点在第三层\n      else if (degree >= 2) return 60;\n      // 低度数节点在最外层，但分散开\n      else return 40 - Math.random() * 20; // 20-40，加入随机性避免重叠\n    },\n    levelWidth: (node: any) => {\n      // 根据节点度数调整层宽\n      const degree = node.data('degree') || node.degree() || 0;\n      if (degree >= 15) return 1; // 中心节点紧凑\n      else if (degree >= 5) return 2; // 高度数节点稍宽\n      else if (degree >= 2) return 3; // 中度数节点更宽\n      else return 4; // 低度数节点最宽，分散排列\n    },\n    minNodeSpacing: 150, // 增加节点间距\n    animate: true,\n    animationDuration: 800,\n    fit: true,\n    padding: 20\n  },\n  avsdf: { name: 'avsdf', nodeSeparation: 120, fit: true, padding: 20 }\n};\n\nconst graphStylesheet: any[] = [\n  {\n    selector: 'node',\n    style: {\n      'font-family': 'sans-serif',\n      'label': 'data(name)',\n      'color': '#e2e8f0',\n      'font-size': 14,\n      'text-valign': 'bottom',\n      'text-halign': 'center',\n      'text-margin-y': 8,\n      'shape': 'ellipse', // Default shape to circle\n      'width': (ele: any) => {\n        const size = ele.data('node_size') || 10000;\n        const scaledSize = Math.log10(Math.max(1, size)) * 15;\n        return Math.max(40, Math.min(120, scaledSize));\n      },\n      'height': (ele: any) => {\n        const size = ele.data('node_size') || 10000;\n        const scaledSize = Math.log10(Math.max(1, size)) * 15;\n        return Math.max(40, Math.min(120, scaledSize));\n      },\n      'transition-property': 'background-color, border-color, width, height',\n      'transition-duration': '0.3s',\n      'text-background-color': 'rgba(15, 23, 42, 0.7)',\n      'text-background-opacity': 1,\n      'text-background-padding': '4px',\n      'text-background-shape': 'round-rectangle',\n      'border-width': 2,\n      'border-color': '#475569'\n    },\n  },\n  // Type-specific styles\n  { selector: 'node[type=\"company\"]', style: { 'background-color': '#3b82f6', 'border-color': '#1d4ed8' } },\n  { selector: 'node[type=\"listed\"]', style: { 'background-color': '#f59e0b', 'border-color': '#b45309' } },\n  { selector: 'node[type=\"partnership\"]', style: { 'background-color': '#10b981', 'border-color': '#047857' } },\n  { selector: 'node[type=\"person\"]', style: { 'background-color': '#8b5cf6', 'border-color': '#6d28d9' } },\n  {\n    selector: 'edge',\n    style: {\n      'width': (ele: any) => {\n        const percentage = cleanStakeData(ele.data('properties')?.percentage);\n        if (percentage === -1) return 2; // 问题数据用固定宽度\n        return Math.max(1, (percentage / 100) * 8);\n      },\n      'line-color': (ele: any) => {\n        const percentage = cleanStakeData(ele.data('properties')?.percentage);\n        return percentage === -1 ? '#ef4444' : '#64748b'; // 问题数据用红色\n      },\n      'line-style': (ele: any) => {\n        const percentage = cleanStakeData(ele.data('properties')?.percentage);\n        return percentage === -1 ? 'dashed' : 'solid'; // 问题数据用虚线\n      },\n      'target-arrow-shape': 'triangle',\n      'target-arrow-color': (ele: any) => {\n        const percentage = cleanStakeData(ele.data('properties')?.percentage);\n        return percentage === -1 ? '#ef4444' : '#64748b';\n      },\n      'curve-style': 'unbundled-bezier',\n      'control-point-distances': '20 -20',\n      'control-point-weights': '0.25 0.75',\n      'transition-property': 'line-color, target-arrow-color, width',\n      'transition-duration': '0.3s',\n      'opacity': 0.7,\n    },\n  },\n  { selector: 'edge[type=\"control\"]', style: { 'line-color': '#f43f5e', 'target-arrow-color': '#f43f5e', 'line-style': 'solid' } },\n  { selector: 'edge[type=\"holding\"]', style: { 'line-color': '#eab308', 'target-arrow-color': '#eab308', 'line-style': 'dashed' } },\n  { selector: 'edge[type=\"partnership\"]', style: { 'line-color': '#0ea5e9', 'target-arrow-color': '#0ea5e9', 'line-style': 'dotted' } },\n\n  // Interaction styles\n  { selector: 'node:selected', style: { 'border-width': 5, 'border-color': '#f43f5e', 'z-index': 99 } },\n  { selector: '.highlighted', style: { 'border-color': '#38bdf8', 'border-width': 4, 'z-index': 99 } },\n  { selector: '.faded', style: { 'opacity': 0.15, 'transition-duration': '0.2s' } },\n\n  // 隐藏被阈值过滤的元素\n  { selector: '.threshold-hidden', style: { 'display': 'none' } },\n  // 隐藏被关系过滤的元素\n  { selector: '.relationship-hidden', style: { 'display': 'none' } },\n  // 标签高亮样式\n  { selector: '.tag-highlighted', style: {\n    'border-width': 6,\n    'border-color': '#faad14',\n    'border-style': 'solid',\n    'z-index': 99\n  } },\n];\n\nconst AdvancedEquityGraph: React.FC<AdvancedEquityGraphProps> = ({ data, onNodeClick, onNodeExpand, height = \"100%\", searchProps, equityThreshold = 0, relationshipFilter = { showShareholders: true, showInvestments: true }, selectedTags = new Set(), onVisibleNodesChange }) => {\n  const containerRef = useRef<HTMLDivElement>(null);\n  const cyRef = useRef<cytoscape.Core | null>(null);\n  const [currentLayout, setCurrentLayout] = useState<string>('fcose'); // 设为默认布局\n  const [tooltip, setTooltip] = useState<{ content: string; x: number; y: number; visible: boolean } | null>(null);\n\n  // 获取当前搜索的公司名称用于过滤逻辑\n  const companyName = searchProps?.value || '';\n\n  useEffect(() => {\n    if (!containerRef.current || !data.nodes.length) return;\n\n    try {\n      // 验证数据完整性 - 不再在这里过滤数据\n      const validNodes = data.nodes.filter(n => n.id && n.name);\n      const validEdges = data.edges.filter(e => e.source && e.target);\n\n      if (validNodes.length === 0) {\n        return;\n      }\n\n      // 先规范化 ID 为字符串，避免数值 ID 与 Cytoscape 匹配失败\n      const normalizedNodes = validNodes.map((n) => ({ ...n, id: String(n.id) }));\n      const normalizedEdges = validEdges.map((e) => ({\n        source: String(e.source),\n        target: String(e.target),\n        type: e.type,\n        properties: e.properties,\n      }));\n\n      // 计算节点度数，用于布局算法\n      const degreeMap = new Map<string, number>();\n      normalizedNodes.forEach((node) => {\n        const degree = normalizedEdges.filter(\n          (e) => e.source === node.id || e.target === node.id\n        ).length;\n        degreeMap.set(node.id, degree);\n      });\n\n      // 组装 Cytoscape 元素\n      const nodes = normalizedNodes.map((n) => ({\n        group: 'nodes' as const,\n        data: {\n          ...n,\n          ...n.properties,\n          degree: degreeMap.get(n.id) || 0,\n        },\n      }));\n\n      const edges = normalizedEdges.map((e, idx) => ({\n        group: 'edges' as const,\n        data: {\n          id: `${e.source}->${e.target}-${idx}`,\n          source: e.source,\n          target: e.target,\n          type: e.type,\n          properties: e.properties,\n        },\n      }));\n\n      const elements = [...nodes, ...edges];\n\n      // 安全地获取布局配置\n      const layoutConfig = LAYOUT_CONFIGS[currentLayout as keyof typeof LAYOUT_CONFIGS] || LAYOUT_CONFIGS.dagre;\n\n      cyRef.current = cytoscape({\n        container: containerRef.current,\n        elements: elements,\n        style: graphStylesheet,\n        layout: layoutConfig,\n        // wheelSensitivity: 0.5, // 移除自定义滚轮敏感度以消除警告\n      });\n\n      const cy = cyRef.current;\n\n      cy.on('tap', 'node', (event) => {\n        const node = event.target;\n        const nodeData = node.data();\n\n        cy.elements().removeClass('faded');\n        cy.getElementById(nodeData.id).neighborhood().addClass('highlighted');\n        if (onNodeClick) { onNodeClick(nodeData); }\n      });\n\n      cy.on('mouseover', 'node', (event) => {\n        const node = event.target;\n        const data = node.data();\n        const pos = event.renderedPosition;\n\n          document.body.style.cursor = 'pointer';\n\n        const neighborhood = node.neighborhood();\n        cy.elements().addClass('faded');\n        node.removeClass('faded').addClass('highlighted');\n        neighborhood.removeClass('faded');\n\n        const content = `\n          <div style=\"font-weight: bold; margin-bottom: 4px; color: #f1f5f9;\">${data.name}</div>\n          <div style=\"color: #cbd5e1;\">法定代表人: ${data.法定代表人 || 'N/A'}</div>\n          <div style=\"color: #cbd5e1;\">注册资本: ${data.注册资本 || 'N/A'}</div>\n        `;\n        setTooltip({ content, x: pos.x, y: pos.y, visible: true });\n      });\n\n      cy.on('mouseover', 'edge', (event) => {\n        const edge = event.target;\n        const data = edge.data();\n        const pos = event.renderedPosition;\n\n        const originalValue = data.properties?.percentage;\n        const cleanedValue = cleanStakeData(originalValue);\n\n        const content = `\n          <div style=\"font-weight: bold; margin-bottom: 4px; color: #f1f5f9;\">股权关系</div>\n          <div style=\"color: #cbd5e1;\">持股比例: ${cleanedValue === -1 ? '数据异常' : cleanedValue.toFixed(2) + '%'}</div>\n          ${cleanedValue === -1 ? `<div style=\"color: #fbbf24; font-size: 11px; margin-top: 2px;\">原值: \"${originalValue}\"</div>` : ''}\n        `;\n        setTooltip({ content, x: pos.x, y: pos.y, visible: true });\n      });\n\n      cy.on('mouseout', 'node, edge', () => {\n        cy.elements().removeClass('faded highlighted');\n        document.body.style.cursor = 'default';\n        setTooltip(prev => prev ? { ...prev, visible: false } : null);\n      });\n\n      cy.on('pan zoom', () => {\n        setTooltip(null);\n      });\n\n      return () => {\n        try {\n          cy?.destroy();\n        } catch (e) {\n          // Silently handle destroy errors\n        }\n      };\n    } catch (error) {\n      console.error('Error initializing cytoscape:', error);\n    }\n  }, [data, onNodeClick, onNodeExpand, currentLayout]);\n\n  // 单独的useEffect处理股权阈值和关系过滤，避免重新布局\n  useEffect(() => {\n    const cy = cyRef.current;\n    if (!cy || !cy.elements().length) return;\n\n    try {\n      // 重置所有元素为可见\n      cy.elements().removeClass('threshold-hidden relationship-hidden');\n\n      // 首先处理关系类型过滤\n      if (!relationshipFilter.showShareholders || !relationshipFilter.showInvestments) {\n        // 智能查找中心节点\n        let searchedNodes = cy.nodes().filter(node => {\n          const nodeName = node.data('name') || node.data('企业名称');\n          return nodeName === companyName;\n        });\n\n        // 如果精确匹配失败，尝试部分匹配\n        if (searchedNodes.length === 0 && companyName) {\n          searchedNodes = cy.nodes().filter(node => {\n            const nodeName = node.data('name') || node.data('企业名称');\n            return nodeName && (nodeName.includes(companyName) || companyName.includes(nodeName));\n          });\n        }\n\n        // 如果还是找不到，选择连接度最高的节点作为中心节点\n        if (searchedNodes.length === 0) {\n          let maxDegree = 0;\n          let centerNode = null;\n          cy.nodes().forEach(node => {\n            const degree = node.degree();\n            if (degree > maxDegree) {\n              maxDegree = degree;\n              centerNode = node;\n            }\n          });\n          if (centerNode) {\n            searchedNodes = cy.collection([centerNode]);\n          }\n        }\n\n        if (searchedNodes.length > 0) {\n          const centerNode = searchedNodes[0];\n          const centerNodeId = centerNode.id();\n\n          // 第一步：根据关系类型隐藏边\n          let hasHiddenEdges = false;\n          cy.edges().forEach(edge => {\n            const sourceId = edge.source().id();\n            const targetId = edge.target().id();\n\n            let shouldHide = false;\n\n            // 判断是否是股东关系（其他节点指向中心节点）\n            if (!relationshipFilter.showShareholders && targetId === centerNodeId) {\n              shouldHide = true;\n            }\n\n            // 判断是否是对外投资（中心节点指向其他节点）\n            if (!relationshipFilter.showInvestments && sourceId === centerNodeId) {\n              shouldHide = true;\n            }\n\n            if (shouldHide) {\n              edge.addClass('relationship-hidden');\n              hasHiddenEdges = true;\n            }\n          });\n\n          // 第二步：如果有边被隐藏，递归隐藏所有不可达的节点和边\n          if (hasHiddenEdges) {\n            // 使用BFS从中心节点开始，找到所有可达的节点\n            const visitedNodes = new Set([centerNodeId]);\n            const queue = [centerNode];\n\n            while (queue.length > 0) {\n              const currentNode = queue.shift();\n              if (!currentNode) continue;\n\n              // 遍历当前节点的所有可见边\n              currentNode.connectedEdges().forEach(edge => {\n                if (!edge.hasClass('relationship-hidden')) {\n                  const source = edge.source();\n                  const target = edge.target();\n                  const otherNode = source.id() === currentNode.id() ? target : source;\n                  const otherNodeId = otherNode.id();\n\n                  if (!visitedNodes.has(otherNodeId)) {\n                    visitedNodes.add(otherNodeId);\n                    queue.push(otherNode as any);\n                  }\n                }\n              });\n            }\n\n            // 隐藏所有不可达的节点和边\n            cy.nodes().forEach(node => {\n              if (!visitedNodes.has(node.id()) && !node.hasClass('relationship-hidden')) {\n                node.addClass('relationship-hidden');\n              }\n            });\n\n            cy.edges().forEach(edge => {\n              if (!edge.hasClass('relationship-hidden')) {\n                const sourceId = edge.source().id();\n                const targetId = edge.target().id();\n\n                if (!visitedNodes.has(sourceId) || !visitedNodes.has(targetId)) {\n                  edge.addClass('relationship-hidden');\n                }\n              }\n            });\n          }\n        }\n      }\n\n      // 然后处理股权阈值过滤\n      if (equityThreshold > 0) {\n        cy.edges().forEach(edge => {\n          if (!edge.hasClass('relationship-hidden')) {\n            const percentage = cleanStakeData(edge.data('properties')?.percentage);\n            if (percentage !== -1 && percentage < equityThreshold) {\n              edge.addClass('threshold-hidden');\n            }\n          }\n        });\n      }\n\n      // 最后隐藏孤立节点\n      cy.nodes().forEach(node => {\n        const connectedVisibleEdges = node.connectedEdges().filter(edge =>\n          !edge.hasClass('threshold-hidden') && !edge.hasClass('relationship-hidden')\n        );\n        if (connectedVisibleEdges.length === 0) {\n          const isSearchedCompany = node.data('name') === companyName ||\n                                 node.data('企业名称') === companyName;\n          if (!isSearchedCompany) {\n            node.addClass('threshold-hidden');\n          }\n        }\n      });\n\n      // 通知父组件可见节点数量\n      const visibleNodes = cy.nodes().filter(node =>\n        !node.hasClass('threshold-hidden') && !node.hasClass('relationship-hidden')\n      );\n      if (onVisibleNodesChange) {\n        onVisibleNodesChange(visibleNodes.length);\n      }\n    } catch (error) {\n      console.error('关系过滤错误:', error);\n    }\n  }, [equityThreshold, relationshipFilter, companyName]);\n\n  // 单独的useEffect处理标签高亮\n  useEffect(() => {\n    const cy = cyRef.current;\n    if (!cy || !cy.elements().length) return;\n\n    try {\n      // 重置所有节点的标签高亮状态\n      cy.nodes().removeClass('tag-highlighted');\n\n      if (selectedTags.size > 0) {\n        cy.nodes().forEach(node => {\n          const tags = node.data('标签') || node.data('properties')?.标签;\n          if (tags && typeof tags === 'string') {\n            const tagList = tags.split('|').map(tag => tag.trim()).filter(tag => tag);\n            const hasSelectedTag = tagList.some(tag => selectedTags.has(tag));\n            if (hasSelectedTag) {\n              node.addClass('tag-highlighted');\n            }\n          }\n        });\n      }\n    } catch (error) {\n      console.error('标签高亮错误:', error);\n    }\n  }, [selectedTags]);\n\n  useEffect(() => {\n    if (cyRef.current) {\n      try {\n        const layoutConfig = LAYOUT_CONFIGS[currentLayout as keyof typeof LAYOUT_CONFIGS] || LAYOUT_CONFIGS.dagre;\n        const layout = cyRef.current.layout(layoutConfig);\n        layout.run();\n      } catch (error) {\n        console.error('Error switching layout:', error);\n        // 回退到默认布局\n        try {\n          const defaultLayout = cyRef.current.layout(LAYOUT_CONFIGS.dagre);\n          defaultLayout.run();\n        } catch (fallbackError) {\n          console.error('Fallback layout also failed:', fallbackError);\n        }\n      }\n    }\n  }, [currentLayout]);\n\n  const handleLayoutChange = (layout: string) => { setCurrentLayout(layout); };\n\n  const handleControlClick = (action: 'zoomIn' | 'zoomOut' | 'fit') => {\n    const cy = cyRef.current;\n    if (!cy) return;\n    if (action === 'zoomIn') cy.zoom({ level: cy.zoom() * 1.2, renderedPosition: { x: cy.width() / 2, y: cy.height() / 2 } });\n    if (action === 'zoomOut') cy.zoom({ level: cy.zoom() / 1.2, renderedPosition: { x: cy.width() / 2, y: cy.height() / 2 } });\n    if (action === 'fit') cy.fit(undefined, 50);\n  };\n\n  return (\n    <div style={{ position: 'relative', width: '100%', height, minHeight: 480, overflow: 'hidden', margin: 0, padding: 0, display: 'flex', flexDirection: 'column' }}>\n      <div ref={containerRef} style={{ width: '100%', height: '100%', background: 'transparent', margin: 0, padding: 0, flex: 1 }} />\n\n      {tooltip && (\n        <div\n            dangerouslySetInnerHTML={{ __html: tooltip.content }}\n            style={{\n                position: 'absolute',\n                left: tooltip.x + 20,\n                top: tooltip.y - 10,\n                background: 'rgba(15, 23, 42, 0.85)',\n                color: 'white',\n                padding: '8px 12px',\n                borderRadius: '6px',\n                border: '1px solid rgba(255, 255, 255, 0.2)',\n                backdropFilter: 'blur(4px)',\n                zIndex: 100,\n                pointerEvents: 'none',\n                fontSize: '13px',\n                maxWidth: '300px',\n                transition: 'opacity 0.2s, transform 0.2s',\n                opacity: tooltip.visible ? 1 : 0,\n                transform: tooltip.visible ? 'translateY(0)' : 'translateY(10px)',\n            }}\n        />\n      )}\n\n      <div style={{ position: 'absolute', top: 16, left: 16, right: 16, zIndex: 10 }}>\n        {/* 第一行：搜索控件 */}\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px', flexWrap: 'wrap' }}>\n          {searchProps && (\n            <>\n              <AutoComplete\n                style={{ width: 'min(280px, calc(100% - 100px))', minWidth: '200px' }}\n                options={searchProps.options}\n                onSelect={searchProps.onSelect}\n                onSearch={searchProps.onSearch}\n                placeholder=\"输入公司名进行查询\"\n                value={searchProps.value}\n              />\n              <Button\n                type=\"primary\"\n                onClick={() => searchProps.onSelect(searchProps.value)}\n                loading={searchProps.loading}\n                style={{ flexShrink: 0 }}\n              >\n                查询\n              </Button>\n            </>\n          )}\n        </div>\n\n        {/* 第二行：控制按钮 */}\n        <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', gap: '8px', flexWrap: 'wrap' }}>\n          <Space size=\"small\" wrap>\n            <Tooltip title=\"放大\"><Button ghost size=\"small\" icon={<ZoomIn size={14} />} onClick={() => handleControlClick('zoomIn')} /></Tooltip>\n            <Tooltip title=\"缩小\"><Button ghost size=\"small\" icon={<ZoomOut size={14} />} onClick={() => handleControlClick('zoomOut')} /></Tooltip>\n            <Tooltip title=\"适应屏幕\"><Button ghost size=\"small\" icon={<Maximize2 size={14} />} onClick={() => handleControlClick('fit')} /></Tooltip>\n            <Select\n              value={currentLayout}\n              onChange={handleLayoutChange}\n              size=\"small\"\n              style={{ width: 'min(140px, calc(100vw - 200px))', minWidth: '120px' }}\n              options={[\n                { value: 'fcose', label: '智能防重叠' },\n                { value: 'concentric', label: '星型同心圆' },\n                { value: 'dagre', label: '层级树状' },\n                { value: 'cose-bilkent', label: '经典力导向' },\n                { value: 'avsdf', label: '对称布局' },\n              ]}\n            />\n          </Space>\n        </div>\n      </div>\n\n      <div style={{ position: 'absolute', bottom: 0, left: 8, zIndex: 10 }}>\n        <GraphLegend />\n      </div>\n    </div>\n  );\n};\n\nexport default AdvancedEquityGraph;\n"], "names": ["Text", "Typography", "LEGEND_DATA", "DATA_QUALITY_LEGEND", "ShapePreview", "color", "style", "jsx", "LinePreview", "lineStyle", "GraphLegend", "Card", "jsxs", "Space", "item", "Divider", "index", "cytoscape", "dagre", "coseBilkent", "fcose", "avsdf", "cleanStakeData", "input", "str", "num", "LAYOUT_CONFIGS", "edge", "sourceNode", "targetNode", "sourceDegree", "targetDegree", "maxDegree", "minDegree", "edge<PERSON><PERSON><PERSON>", "node", "degree", "repulsion", "graphStylesheet", "ele", "size", "scaledSize", "percentage", "_a", "AdvancedEquityGraph", "data", "onNodeClick", "onNodeExpand", "height", "searchProps", "equityThreshold", "<PERSON><PERSON><PERSON>er", "selectedTags", "onVisibleNodesChange", "containerRef", "useRef", "cyRef", "currentLayout", "setCurrentLayout", "useState", "tooltip", "setTooltip", "companyName", "useEffect", "validNodes", "n", "validEdges", "e", "normalizedNodes", "normalizedEdges", "degreeMap", "nodes", "edges", "idx", "elements", "layoutConfig", "cy", "event", "nodeData", "pos", "neighborhood", "content", "originalValue", "cleanedValue", "prev", "error", "searchedNodes", "nodeName", "centerNode", "centerNodeId", "hasHiddenEdges", "sourceId", "targetId", "shouldHide", "visitedNodes", "queue", "currentNode", "source", "target", "otherNode", "otherNodeId", "visibleNodes", "tags", "tag", "fallback<PERSON><PERSON>r", "handleLayoutChange", "layout", "handleControlClick", "action", "Fragment", "AutoComplete", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ZoomIn", "ZoomOut", "Maximize2", "Select"], "mappings": "8OAGA,KAAM,CAAE,KAAAA,GAASC,EAEXC,GAAc,CAChB,CAAE,KAAM,UAAW,MAAO,QAAS,MAAO,SAAA,EAC1C,CAAE,KAAM,cAAe,MAAO,OAAQ,MAAO,SAAA,EAC7C,CAAE,KAAM,SAAU,MAAO,KAAM,MAAO,SAAA,CAC1C,EAEMC,GAAsB,CACxB,CAAE,MAAO,UAAW,MAAO,QAAS,MAAO,MAAA,EAC3C,CAAE,MAAO,UAAW,MAAO,SAAU,MAAO,MAAA,CAChD,EAEMC,GAA6C,CAAC,CAAE,MAAAC,KAAY,CAC9D,MAAMC,EAA6B,CAC/B,MAAO,OACP,OAAQ,OACR,gBAAiBD,EACjB,OAAQ,qCACR,YAAa,MACb,WAAY,EACZ,aAAc,KAAA,EAGlB,OAAOE,MAAC,OAAI,MAAAD,EAAc,CAC9B,EAEME,GAA2D,CAAC,CAAE,MAAAH,EAAO,MAAAC,KAAY,CACnF,MAAMG,EAAiC,CACnC,MAAO,OACP,OAAQ,MACR,gBAAiBJ,EACjB,YAAa,MACb,WAAY,EACZ,YAAaC,IAAU,SAAW,SAAW,QAC7C,YAAaA,IAAU,SAAW,QAAU,IAC5C,YAAaD,EACb,WAAYC,IAAU,SAAW,cAAgBD,CAAA,EAGrD,OAAOE,EAAAA,IAAC,MAAA,CAAI,MAAOE,CAAA,CAAW,CAClC,EAEaC,GAAwB,IACjCH,EAAAA,IAACI,EAAA,CACG,KAAK,QACL,SAAU,GACV,MAAO,CACH,WAAY,wBACZ,eAAgB,YAChB,OAAQ,sCACR,aAAc,cACd,OAAQ,CAAA,EAEZ,UAAW,CAAE,QAAS,MAAO,OAAQ,CAAA,EAErC,SAAAC,EAAAA,KAACC,EAAA,CAAM,UAAU,WAAW,KAAM,EAC9B,SAAA,CAAAN,EAAAA,IAACP,EAAA,CAAK,OAAM,GAAC,MAAO,CAAE,MAAO,UAAW,SAAU,GAAI,aAAc,KAAA,EAAS,SAAA,KAAE,EAG9EE,GAAY,IAAIY,GACbF,EAAAA,KAAC,MAAA,CAAoB,MAAO,CAAE,QAAS,OAAQ,WAAY,SAAU,aAAc,OAC/E,SAAA,CAAAL,EAAAA,IAACH,GAAA,CAAa,MAAOU,EAAK,KAAA,CAAO,EACjCP,EAAAA,IAACP,EAAA,CAAK,MAAO,CAAE,SAAU,GAAI,MAAO,SAAA,EAAc,SAAAc,EAAK,KAAA,CAAM,CAAA,GAFvDA,EAAK,IAGf,CACH,EAEDP,MAACQ,IAAQ,MAAO,CAAE,OAAQ,cAAe,YAAa,4BAA8B,EAGpFR,EAAAA,IAACP,EAAA,CAAK,OAAM,GAAC,MAAO,CAAE,MAAO,UAAW,SAAU,GAAI,aAAc,KAAA,EAAS,SAAA,OAAI,EAChFG,GAAoB,IAAI,CAACW,EAAME,IAC5BJ,EAAAA,KAAC,MAAA,CAAgB,MAAO,CAAE,QAAS,OAAQ,WAAY,SAAU,aAAc,OAC3E,SAAA,CAAAL,MAACC,IAAY,MAAOM,EAAK,MAAO,MAAOA,EAAK,MAAO,EACnDP,EAAAA,IAACP,EAAA,CAAK,MAAO,CAAE,SAAU,GAAI,MAAO,SAAA,EAAc,SAAAc,EAAK,KAAA,CAAM,CAAA,CAAA,EAFvDE,CAGV,CACH,CAAA,CAAA,CACL,CAAA,CACJ,ECrEJC,EAAU,IAAIC,CAAK,EACnBD,EAAU,IAAIE,CAAW,EACzBF,EAAU,IAAIG,CAAK,EACnBH,EAAU,IAAII,CAAK,EAwCnB,MAAMC,EAAkBC,GAAuB,CAC7C,GAAI,CAACA,GAASA,IAAU,IAAK,MAAO,GAEpC,MAAMC,EAAMD,EAAM,SAAA,EAAW,QAAQ,SAAU,EAAE,EAC3CE,EAAM,WAAWD,CAAG,EAG1B,OADI,MAAMC,CAAG,GAAKA,EAAM,GACpBA,GAAO,MAAQA,GAAO,KAAa,GACnCA,EAAM,IAAYA,EAAM,IAAQ,GAAKA,EAAI,IAEtCA,CACT,EAEMC,EAAiB,CACrB,MAAO,CAAE,KAAM,QAAS,QAAS,KAAM,cAAe,IAAK,4BAA6B,GAAM,QAAS,GAAM,kBAAmB,IAAK,IAAK,GAAM,QAAS,EAAA,EACzJ,MAAO,CACL,KAAM,QACN,QAAS,QACT,QAAS,GACT,kBAAmB,IACnB,IAAK,GACL,QAAS,GAET,QAAS,GACT,qBAAsB,EACtB,gBAAiB,GACjB,aAAc,EACd,2BAA4B,GAG5B,eAAgB,IAChB,MAAO,KAGP,gBAAkBC,GAAc,CAC9B,GAAI,CACF,MAAMC,EAAaD,EAAK,OAAA,EAClBE,EAAaF,EAAK,OAAA,EACxB,GAAI,CAACC,GAAc,CAACC,EAAY,MAAO,KAEvC,MAAMC,EAAeF,EAAW,OAAA,GAAY,EACtCG,EAAeF,EAAW,OAAA,GAAY,EACtCG,EAAY,KAAK,IAAIF,EAAcC,CAAY,EAC/CE,EAAY,KAAK,IAAIH,EAAcC,CAAY,EAErD,IAAIG,EAAa,IAGjB,OAAIJ,GAAgB,GAAKC,GAAgB,EACvCG,EAAa,IAAM,KAAK,OAAA,EAAW,IAG5BF,GAAa,GAChBC,IAAc,EAChBC,EAAa,IAAM,KAAK,OAAA,EAAW,GAC1BD,GAAa,GAAKA,GAAa,EACxCC,EAAa,IAAM,KAAK,OAAA,EAAW,GAEnCA,EAAa,IAAM,KAAK,OAAA,EAAW,IAI9BJ,GAAgB,GAAKC,GAAgB,EAE5CG,EAAa,KADMJ,EAAeC,GAAgB,EAClB,GAAM,KAAK,SAAW,GAG/CD,IAAiB,GAAKC,IAAiB,EAC9CG,EAAa,GAAK,KAAK,OAAA,EAAW,GAIlCA,EAAa,GAAK,KAAK,OAAA,EAAW,GAG7BA,CACT,MAAY,CACV,MAAO,IACT,CACF,EAGA,cAAgBC,GAAc,CAC5B,GAAI,CACF,GAAI,CAACA,EAAM,MAAO,MAClB,MAAMC,EAASD,EAAK,OAAA,GAAY,EAChC,IAAIE,EAAY,KAGhB,OAAID,GAAU,GACZC,EAAY,IAAUD,EAAS,IAGxBA,GAAU,EACjBC,EAAY,IAASD,EAAS,IAGvBA,GAAU,EACjBC,EAAY,KAASD,EAAS,IAI9BC,EAAY,IAGPA,CACT,MAAY,CACV,MAAO,IACT,CACF,CAAA,EAEF,eAAgB,CACd,KAAM,eACN,QAAS,MACT,kBAAmB,IACnB,QAAS,GACT,IAAK,GACL,QAAS,GACT,cAAe,KACf,gBAAiB,GAAA,EAEnB,WAAY,CACV,KAAM,aACN,WAAaF,GAAc,CAEzB,MAAMC,EAASD,EAAK,KAAK,QAAQ,GAAKA,EAAK,UAAY,EAGvD,OAAIC,GAAU,GAAW,IAEhBA,GAAU,EAAU,GAEpBA,GAAU,EAAU,GAEjB,GAAK,KAAK,OAAA,EAAW,EACnC,EACA,WAAaD,GAAc,CAEzB,MAAMC,EAASD,EAAK,KAAK,QAAQ,GAAKA,EAAK,UAAY,EACvD,OAAIC,GAAU,GAAW,EAChBA,GAAU,EAAU,EACpBA,GAAU,EAAU,EACjB,CACd,EACA,eAAgB,IAChB,QAAS,GACT,kBAAmB,IACnB,IAAK,GACL,QAAS,EAAA,EAEX,MAAO,CAAE,KAAM,QAAS,eAAgB,IAAK,IAAK,GAAM,QAAS,EAAA,CACnE,EAEME,GAAyB,CAC7B,CACE,SAAU,OACV,MAAO,CACL,cAAe,aACf,MAAS,aACT,MAAS,UACT,YAAa,GACb,cAAe,SACf,cAAe,SACf,gBAAiB,EACjB,MAAS,UACT,MAAUC,GAAa,CACrB,MAAMC,EAAOD,EAAI,KAAK,WAAW,GAAK,IAChCE,EAAa,KAAK,MAAM,KAAK,IAAI,EAAGD,CAAI,CAAC,EAAI,GACnD,OAAO,KAAK,IAAI,GAAI,KAAK,IAAI,IAAKC,CAAU,CAAC,CAC/C,EACA,OAAWF,GAAa,CACtB,MAAMC,EAAOD,EAAI,KAAK,WAAW,GAAK,IAChCE,EAAa,KAAK,MAAM,KAAK,IAAI,EAAGD,CAAI,CAAC,EAAI,GACnD,OAAO,KAAK,IAAI,GAAI,KAAK,IAAI,IAAKC,CAAU,CAAC,CAC/C,EACA,sBAAuB,gDACvB,sBAAuB,OACvB,wBAAyB,wBACzB,0BAA2B,EAC3B,0BAA2B,MAC3B,wBAAyB,kBACzB,eAAgB,EAChB,eAAgB,SAAA,CAClB,EAGF,CAAE,SAAU,uBAAwB,MAAO,CAAE,mBAAoB,UAAW,eAAgB,UAAU,EACtG,CAAE,SAAU,sBAAuB,MAAO,CAAE,mBAAoB,UAAW,eAAgB,UAAU,EACrG,CAAE,SAAU,2BAA4B,MAAO,CAAE,mBAAoB,UAAW,eAAgB,UAAU,EAC1G,CAAE,SAAU,sBAAuB,MAAO,CAAE,mBAAoB,UAAW,eAAgB,UAAU,EACrG,CACE,SAAU,OACV,MAAO,CACL,MAAUF,GAAa,OACrB,MAAMG,EAAapB,GAAeqB,EAAAJ,EAAI,KAAK,YAAY,IAArB,YAAAI,EAAwB,UAAU,EACpE,OAAID,IAAe,GAAW,EACvB,KAAK,IAAI,EAAIA,EAAa,IAAO,CAAC,CAC3C,EACA,aAAeH,GAAa,OAE1B,OADmBjB,GAAeqB,EAAAJ,EAAI,KAAK,YAAY,IAArB,YAAAI,EAAwB,UAAU,IAC9C,GAAK,UAAY,SACzC,EACA,aAAeJ,GAAa,OAE1B,OADmBjB,GAAeqB,EAAAJ,EAAI,KAAK,YAAY,IAArB,YAAAI,EAAwB,UAAU,IAC9C,GAAK,SAAW,OACxC,EACA,qBAAsB,WACtB,qBAAuBJ,GAAa,OAElC,OADmBjB,GAAeqB,EAAAJ,EAAI,KAAK,YAAY,IAArB,YAAAI,EAAwB,UAAU,IAC9C,GAAK,UAAY,SACzC,EACA,cAAe,mBACf,0BAA2B,SAC3B,wBAAyB,YACzB,sBAAuB,wCACvB,sBAAuB,OACvB,QAAW,EAAA,CACb,EAEF,CAAE,SAAU,uBAAwB,MAAO,CAAE,aAAc,UAAW,qBAAsB,UAAW,aAAc,QAAQ,EAC7H,CAAE,SAAU,uBAAwB,MAAO,CAAE,aAAc,UAAW,qBAAsB,UAAW,aAAc,SAAS,EAC9H,CAAE,SAAU,2BAA4B,MAAO,CAAE,aAAc,UAAW,qBAAsB,UAAW,aAAc,SAAS,EAGlI,CAAE,SAAU,gBAAiB,MAAO,CAAE,eAAgB,EAAG,eAAgB,UAAW,UAAW,GAAG,EAClG,CAAE,SAAU,eAAgB,MAAO,CAAE,eAAgB,UAAW,eAAgB,EAAG,UAAW,GAAG,EACjG,CAAE,SAAU,SAAU,MAAO,CAAE,QAAW,IAAM,sBAAuB,OAAO,EAG9E,CAAE,SAAU,oBAAqB,MAAO,CAAE,QAAW,OAAO,EAE5D,CAAE,SAAU,uBAAwB,MAAO,CAAE,QAAW,OAAO,EAE/D,CAAE,SAAU,mBAAoB,MAAO,CACrC,eAAgB,EAChB,eAAgB,UAChB,eAAgB,QAChB,UAAW,EAAA,CACb,CACF,EAEMC,GAA0D,CAAC,CAAE,KAAAC,EAAM,YAAAC,EAAa,aAAAC,EAAc,OAAAC,EAAS,OAAQ,YAAAC,EAAa,gBAAAC,EAAkB,EAAG,mBAAAC,EAAqB,CAAE,iBAAkB,GAAM,gBAAiB,EAAA,EAAQ,aAAAC,EAAe,IAAI,IAAO,qBAAAC,KAA2B,CAClR,MAAMC,EAAeC,EAAAA,OAAuB,IAAI,EAC1CC,EAAQD,EAAAA,OAA8B,IAAI,EAC1C,CAACE,EAAeC,CAAgB,EAAIC,EAAAA,SAAiB,OAAO,EAC5D,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAA6E,IAAI,EAGzGG,GAAcb,GAAA,YAAAA,EAAa,QAAS,GAE1Cc,EAAAA,UAAU,IAAM,CACd,GAAI,GAACT,EAAa,SAAW,CAACT,EAAK,MAAM,QAEzC,GAAI,CAEF,MAAMmB,EAAanB,EAAK,MAAM,UAAYoB,EAAE,IAAMA,EAAE,IAAI,EAClDC,EAAarB,EAAK,MAAM,UAAYsB,EAAE,QAAUA,EAAE,MAAM,EAE9D,GAAIH,EAAW,SAAW,EACxB,OAIF,MAAMI,EAAkBJ,EAAW,IAAKC,IAAO,CAAE,GAAGA,EAAG,GAAI,OAAOA,EAAE,EAAE,GAAI,EACpEI,EAAkBH,EAAW,IAAKC,IAAO,CAC7C,OAAQ,OAAOA,EAAE,MAAM,EACvB,OAAQ,OAAOA,EAAE,MAAM,EACvB,KAAMA,EAAE,KACR,WAAYA,EAAE,UAAA,EACd,EAGIG,MAAgB,IACtBF,EAAgB,QAASjC,GAAS,CAChC,MAAMC,EAASiC,EAAgB,OAC5BF,GAAMA,EAAE,SAAWhC,EAAK,IAAMgC,EAAE,SAAWhC,EAAK,EAAA,EACjD,OACFmC,EAAU,IAAInC,EAAK,GAAIC,CAAM,CAC/B,CAAC,EAGD,MAAMmC,EAAQH,EAAgB,IAAKH,IAAO,CACxC,MAAO,QACP,KAAM,CACJ,GAAGA,EACH,GAAGA,EAAE,WACL,OAAQK,EAAU,IAAIL,EAAE,EAAE,GAAK,CAAA,CACjC,EACA,EAEIO,EAAQH,EAAgB,IAAI,CAACF,EAAGM,KAAS,CAC7C,MAAO,QACP,KAAM,CACJ,GAAI,GAAGN,EAAE,MAAM,KAAKA,EAAE,MAAM,IAAIM,CAAG,GACnC,OAAQN,EAAE,OACV,OAAQA,EAAE,OACV,KAAMA,EAAE,KACR,WAAYA,EAAE,UAAA,CAChB,EACA,EAEIO,EAAW,CAAC,GAAGH,EAAO,GAAGC,CAAK,EAG9BG,EAAejD,EAAe+B,CAA4C,GAAK/B,EAAe,MAEpG8B,EAAM,QAAUvC,EAAU,CACxB,UAAWqC,EAAa,QACxB,SAAAoB,EACA,MAAOpC,GACP,OAAQqC,CAAA,CAET,EAED,MAAMC,EAAKpB,EAAM,QAEjB,OAAAoB,EAAG,GAAG,MAAO,OAASC,GAAU,CAE9B,MAAMC,EADOD,EAAM,OACG,KAAA,EAEtBD,EAAG,SAAA,EAAW,YAAY,OAAO,EACjCA,EAAG,eAAeE,EAAS,EAAE,EAAE,aAAA,EAAe,SAAS,aAAa,EAChEhC,GAAeA,EAAYgC,CAAQ,CACzC,CAAC,EAEDF,EAAG,GAAG,YAAa,OAASC,GAAU,CACpC,MAAM1C,EAAO0C,EAAM,OACbhC,EAAOV,EAAK,KAAA,EACZ4C,EAAMF,EAAM,iBAEhB,SAAS,KAAK,MAAM,OAAS,UAE/B,MAAMG,EAAe7C,EAAK,aAAA,EAC1ByC,EAAG,SAAA,EAAW,SAAS,OAAO,EAC9BzC,EAAK,YAAY,OAAO,EAAE,SAAS,aAAa,EAChD6C,EAAa,YAAY,OAAO,EAEhC,MAAMC,EAAU;AAAA,gFACwDpC,EAAK,IAAI;AAAA,gDACzCA,EAAK,OAAS,KAAK;AAAA,+CACpBA,EAAK,MAAQ,KAAK;AAAA,UAEzDgB,EAAW,CAAE,QAAAoB,EAAS,EAAGF,EAAI,EAAG,EAAGA,EAAI,EAAG,QAAS,EAAA,CAAM,CAC3D,CAAC,EAEDH,EAAG,GAAG,YAAa,OAASC,GAAU,OAEpC,MAAMhC,EADOgC,EAAM,OACD,KAAA,EACZE,EAAMF,EAAM,iBAEZK,GAAgBrC,EAAAA,EAAK,aAALA,YAAAA,EAAiB,WACjCsC,EAAe7D,EAAe4D,CAAa,EAE3CD,EAAU;AAAA;AAAA,+CAEuBE,IAAiB,GAAK,OAASA,EAAa,QAAQ,CAAC,EAAI,GAAG;AAAA,YAC/FA,IAAiB,GAAK,uEAAuED,CAAa,UAAY,EAAE;AAAA,UAE5HrB,EAAW,CAAE,QAAAoB,EAAS,EAAGF,EAAI,EAAG,EAAGA,EAAI,EAAG,QAAS,EAAA,CAAM,CAC3D,CAAC,EAEDH,EAAG,GAAG,WAAY,aAAc,IAAM,CACpCA,EAAG,SAAA,EAAW,YAAY,mBAAmB,EAC7C,SAAS,KAAK,MAAM,OAAS,UAC7Bf,EAAWuB,GAAQA,EAAO,CAAE,GAAGA,EAAM,QAAS,EAAA,EAAU,IAAI,CAC9D,CAAC,EAEDR,EAAG,GAAG,WAAY,IAAM,CACtBf,EAAW,IAAI,CACjB,CAAC,EAEM,IAAM,CACX,GAAI,CACFe,GAAA,MAAAA,EAAI,SACN,MAAY,CAEZ,CACF,CACF,OAASS,EAAO,CACd,QAAQ,MAAM,gCAAiCA,CAAK,CACtD,CACF,EAAG,CAACxC,EAAMC,EAAaC,EAAcU,CAAa,CAAC,EAGnDM,EAAAA,UAAU,IAAM,CACd,MAAMa,EAAKpB,EAAM,QACjB,GAAI,GAACoB,GAAM,CAACA,EAAG,SAAA,EAAW,QAE1B,GAAI,CAKF,GAHAA,EAAG,SAAA,EAAW,YAAY,sCAAsC,EAG5D,CAACzB,EAAmB,kBAAoB,CAACA,EAAmB,gBAAiB,CAE/E,IAAImC,EAAgBV,EAAG,MAAA,EAAQ,OAAOzC,IACnBA,EAAK,KAAK,MAAM,GAAKA,EAAK,KAAK,MAAM,KAClC2B,CACrB,EAWD,GARIwB,EAAc,SAAW,GAAKxB,IAChCwB,EAAgBV,EAAG,MAAA,EAAQ,OAAOzC,GAAQ,CACxC,MAAMoD,EAAWpD,EAAK,KAAK,MAAM,GAAKA,EAAK,KAAK,MAAM,EACtD,OAAOoD,IAAaA,EAAS,SAASzB,CAAW,GAAKA,EAAY,SAASyB,CAAQ,EACrF,CAAC,GAICD,EAAc,SAAW,EAAG,CAC9B,IAAItD,EAAY,EACZwD,EAAa,KACjBZ,EAAG,MAAA,EAAQ,QAAQzC,GAAQ,CACzB,MAAMC,EAASD,EAAK,OAAA,EAChBC,EAASJ,IACXA,EAAYI,EACZoD,EAAarD,EAEjB,CAAC,EACGqD,IACFF,EAAgBV,EAAG,WAAW,CAACY,CAAU,CAAC,EAE9C,CAEA,GAAIF,EAAc,OAAS,EAAG,CAC5B,MAAME,EAAaF,EAAc,CAAC,EAC5BG,EAAeD,EAAW,GAAA,EAGhC,IAAIE,EAAiB,GAwBrB,GAvBAd,EAAG,MAAA,EAAQ,QAAQjD,GAAQ,CACzB,MAAMgE,EAAWhE,EAAK,OAAA,EAAS,GAAA,EACzBiE,EAAWjE,EAAK,OAAA,EAAS,GAAA,EAE/B,IAAIkE,EAAa,GAGb,CAAC1C,EAAmB,kBAAoByC,IAAaH,IACvDI,EAAa,IAIX,CAAC1C,EAAmB,iBAAmBwC,IAAaF,IACtDI,EAAa,IAGXA,IACFlE,EAAK,SAAS,qBAAqB,EACnC+D,EAAiB,GAErB,CAAC,EAGGA,EAAgB,CAElB,MAAMI,EAAe,IAAI,IAAI,CAACL,CAAY,CAAC,EACrCM,EAAQ,CAACP,CAAU,EAEzB,KAAOO,EAAM,OAAS,GAAG,CACvB,MAAMC,EAAcD,EAAM,MAAA,EACrBC,GAGLA,EAAY,eAAA,EAAiB,QAAQrE,GAAQ,CAC3C,GAAI,CAACA,EAAK,SAAS,qBAAqB,EAAG,CACzC,MAAMsE,EAAStE,EAAK,OAAA,EACduE,EAASvE,EAAK,OAAA,EACdwE,EAAYF,EAAO,GAAA,IAASD,EAAY,GAAA,EAAOE,EAASD,EACxDG,EAAcD,EAAU,GAAA,EAEzBL,EAAa,IAAIM,CAAW,IAC/BN,EAAa,IAAIM,CAAW,EAC5BL,EAAM,KAAKI,CAAgB,EAE/B,CACF,CAAC,CACH,CAGAvB,EAAG,MAAA,EAAQ,QAAQzC,GAAQ,CACrB,CAAC2D,EAAa,IAAI3D,EAAK,IAAI,GAAK,CAACA,EAAK,SAAS,qBAAqB,GACtEA,EAAK,SAAS,qBAAqB,CAEvC,CAAC,EAEDyC,EAAG,MAAA,EAAQ,QAAQjD,GAAQ,CACzB,GAAI,CAACA,EAAK,SAAS,qBAAqB,EAAG,CACzC,MAAMgE,EAAWhE,EAAK,OAAA,EAAS,GAAA,EACzBiE,EAAWjE,EAAK,OAAA,EAAS,GAAA,GAE3B,CAACmE,EAAa,IAAIH,CAAQ,GAAK,CAACG,EAAa,IAAIF,CAAQ,IAC3DjE,EAAK,SAAS,qBAAqB,CAEvC,CACF,CAAC,CACH,CACF,CACF,CAGIuB,EAAkB,GACpB0B,EAAG,MAAA,EAAQ,QAAQjD,GAAQ,OACzB,GAAI,CAACA,EAAK,SAAS,qBAAqB,EAAG,CACzC,MAAMe,EAAapB,GAAeqB,EAAAhB,EAAK,KAAK,YAAY,IAAtB,YAAAgB,EAAyB,UAAU,EACjED,IAAe,IAAMA,EAAaQ,GACpCvB,EAAK,SAAS,kBAAkB,CAEpC,CACF,CAAC,EAIHiD,EAAG,MAAA,EAAQ,QAAQzC,GAAQ,CACKA,EAAK,eAAA,EAAiB,OAAOR,GACzD,CAACA,EAAK,SAAS,kBAAkB,GAAK,CAACA,EAAK,SAAS,qBAAqB,CAAA,EAElD,SAAW,IACTQ,EAAK,KAAK,MAAM,IAAM2B,GACzB3B,EAAK,KAAK,MAAM,IAAM2B,GAE3C3B,EAAK,SAAS,kBAAkB,EAGtC,CAAC,EAGD,MAAMkE,EAAezB,EAAG,MAAA,EAAQ,OAAOzC,GACrC,CAACA,EAAK,SAAS,kBAAkB,GAAK,CAACA,EAAK,SAAS,qBAAqB,CAAA,EAExEkB,GACFA,EAAqBgD,EAAa,MAAM,CAE5C,OAAShB,EAAO,CACd,QAAQ,MAAM,UAAWA,CAAK,CAChC,CACF,EAAG,CAACnC,EAAiBC,EAAoBW,CAAW,CAAC,EAGrDC,EAAAA,UAAU,IAAM,CACd,MAAMa,EAAKpB,EAAM,QACjB,GAAI,GAACoB,GAAM,CAACA,EAAG,SAAA,EAAW,QAE1B,GAAI,CAEFA,EAAG,MAAA,EAAQ,YAAY,iBAAiB,EAEpCxB,EAAa,KAAO,GACtBwB,EAAG,MAAA,EAAQ,QAAQzC,GAAQ,OACzB,MAAMmE,EAAOnE,EAAK,KAAK,IAAI,KAAKQ,EAAAR,EAAK,KAAK,YAAY,IAAtB,YAAAQ,EAAyB,IACrD2D,GAAQ,OAAOA,GAAS,UACVA,EAAK,MAAM,GAAG,EAAE,IAAIC,GAAOA,EAAI,KAAA,CAAM,EAAE,UAAcA,CAAG,EACzC,QAAYnD,EAAa,IAAImD,CAAG,CAAC,GAE9DpE,EAAK,SAAS,iBAAiB,CAGrC,CAAC,CAEL,OAASkD,EAAO,CACd,QAAQ,MAAM,UAAWA,CAAK,CAChC,CACF,EAAG,CAACjC,CAAY,CAAC,EAEjBW,EAAAA,UAAU,IAAM,CACd,GAAIP,EAAM,QACR,GAAI,CACF,MAAMmB,EAAejD,EAAe+B,CAA4C,GAAK/B,EAAe,MACrF8B,EAAM,QAAQ,OAAOmB,CAAY,EACzC,IAAA,CACT,OAASU,EAAO,CACd,QAAQ,MAAM,0BAA2BA,CAAK,EAE9C,GAAI,CACoB7B,EAAM,QAAQ,OAAO9B,EAAe,KAAK,EACjD,IAAA,CAChB,OAAS8E,EAAe,CACtB,QAAQ,MAAM,+BAAgCA,CAAa,CAC7D,CACF,CAEJ,EAAG,CAAC/C,CAAa,CAAC,EAElB,MAAMgD,EAAsBC,GAAmB,CAAEhD,EAAiBgD,CAAM,CAAG,EAErEC,EAAsBC,GAAyC,CACnE,MAAMhC,EAAKpB,EAAM,QACZoB,IACDgC,IAAW,UAAUhC,EAAG,KAAK,CAAE,MAAOA,EAAG,KAAA,EAAS,IAAK,iBAAkB,CAAE,EAAGA,EAAG,MAAA,EAAU,EAAG,EAAGA,EAAG,OAAA,EAAW,CAAA,EAAK,EACpHgC,IAAW,WAAWhC,EAAG,KAAK,CAAE,MAAOA,EAAG,KAAA,EAAS,IAAK,iBAAkB,CAAE,EAAGA,EAAG,MAAA,EAAU,EAAG,EAAGA,EAAG,OAAA,EAAW,CAAA,EAAK,EACrHgC,IAAW,OAAOhC,EAAG,IAAI,OAAW,EAAE,EAC5C,EAEA,OACEhE,OAAC,OAAI,MAAO,CAAE,SAAU,WAAY,MAAO,OAAQ,OAAAoC,EAAQ,UAAW,IAAK,SAAU,SAAU,OAAQ,EAAG,QAAS,EAAG,QAAS,OAAQ,cAAe,QAAA,EACpJ,SAAA,CAAAzC,MAAC,OAAI,IAAK+C,EAAc,MAAO,CAAE,MAAO,OAAQ,OAAQ,OAAQ,WAAY,cAAe,OAAQ,EAAG,QAAS,EAAG,KAAM,GAAK,EAE5HM,GACCrD,EAAAA,IAAC,MAAA,CACG,wBAAyB,CAAE,OAAQqD,EAAQ,OAAA,EAC3C,MAAO,CACH,SAAU,WACV,KAAMA,EAAQ,EAAI,GAClB,IAAKA,EAAQ,EAAI,GACjB,WAAY,yBACZ,MAAO,QACP,QAAS,WACT,aAAc,MACd,OAAQ,qCACR,eAAgB,YAChB,OAAQ,IACR,cAAe,OACf,SAAU,OACV,SAAU,QACV,WAAY,+BACZ,QAASA,EAAQ,QAAU,EAAI,EAC/B,UAAWA,EAAQ,QAAU,gBAAkB,kBAAA,CACnD,CAAA,EAINhD,EAAAA,KAAC,MAAA,CAAI,MAAO,CAAE,SAAU,WAAY,IAAK,GAAI,KAAM,GAAI,MAAO,GAAI,OAAQ,IAExE,SAAA,CAAAL,MAAC,MAAA,CAAI,MAAO,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,OAAQ,aAAc,OAAQ,SAAU,QAC/F,YACCK,EAAAA,KAAAiG,WAAA,CACE,SAAA,CAAAtG,EAAAA,IAACuG,GAAA,CACC,MAAO,CAAE,MAAO,iCAAkC,SAAU,OAAA,EAC5D,QAAS7D,EAAY,QACrB,SAAUA,EAAY,SACtB,SAAUA,EAAY,SACtB,YAAY,YACZ,MAAOA,EAAY,KAAA,CAAA,EAErB1C,EAAAA,IAACwG,EAAA,CACC,KAAK,UACL,QAAS,IAAM9D,EAAY,SAASA,EAAY,KAAK,EACrD,QAASA,EAAY,QACrB,MAAO,CAAE,WAAY,CAAA,EACtB,SAAA,IAAA,CAAA,CAED,CAAA,CACF,CAAA,CAEJ,EAGA1C,EAAAA,IAAC,OAAI,MAAO,CAAE,QAAS,OAAQ,eAAgB,WAAY,WAAY,SAAU,IAAK,MAAO,SAAU,QACrG,SAAAK,OAACC,GAAM,KAAK,QAAQ,KAAI,GACtB,SAAA,CAAAN,EAAAA,IAACyG,EAAA,CAAQ,MAAM,KAAK,SAAAzG,EAAAA,IAACwG,GAAO,MAAK,GAAC,KAAK,QAAQ,WAAOE,EAAA,CAAO,KAAM,GAAI,EAAI,QAAS,IAAMN,EAAmB,QAAQ,EAAG,CAAA,CAAE,EAC1HpG,EAAAA,IAACyG,GAAQ,MAAM,KAAK,eAACD,EAAA,CAAO,MAAK,GAAC,KAAK,QAAQ,KAAMxG,MAAC2G,EAAA,CAAQ,KAAM,EAAA,CAAI,EAAI,QAAS,IAAMP,EAAmB,SAAS,CAAA,CAAG,CAAA,CAAE,EAC5HpG,EAAAA,IAACyG,GAAQ,MAAM,OAAO,eAACD,EAAA,CAAO,MAAK,GAAC,KAAK,QAAQ,KAAMxG,MAAC4G,EAAA,CAAU,KAAM,EAAA,CAAI,EAAI,QAAS,IAAMR,EAAmB,KAAK,CAAA,CAAG,CAAA,CAAE,EAC5HpG,EAAAA,IAAC6G,GAAA,CACC,MAAO3D,EACP,SAAUgD,EACV,KAAK,QACL,MAAO,CAAE,MAAO,kCAAmC,SAAU,OAAA,EAC7D,QAAS,CACP,CAAE,MAAO,QAAS,MAAO,OAAA,EACzB,CAAE,MAAO,aAAc,MAAO,OAAA,EAC9B,CAAE,MAAO,QAAS,MAAO,MAAA,EACzB,CAAE,MAAO,eAAgB,MAAO,OAAA,EAChC,CAAE,MAAO,QAAS,MAAO,MAAA,CAAO,CAClC,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAEAlG,EAAAA,IAAC,MAAA,CAAI,MAAO,CAAE,SAAU,WAAY,OAAQ,EAAG,KAAM,EAAG,OAAQ,EAAA,EAC9D,SAAAA,MAACG,KAAY,CAAA,CACf,CAAA,EACF,CAEJ"}