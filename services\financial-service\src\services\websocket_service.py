#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket服务
- 实现实时进度推送
- 管理客户端连接
- 支持任务状态广播
"""
import json
import asyncio
import logging
from typing import Dict, Set, Any, Optional
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime

class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 存储活跃连接: task_id -> Set[WebSocket]
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        # 存储WebSocket到任务ID的映射
        self.connection_tasks: Dict[WebSocket, str] = {}
        self.logger = logging.getLogger("websocket_manager")
    
    async def connect(self, websocket: WebSocket, task_id: str):
        """接受WebSocket连接"""
        await websocket.accept()
        
        # 添加到连接池
        if task_id not in self.active_connections:
            self.active_connections[task_id] = set()
        
        self.active_connections[task_id].add(websocket)
        self.connection_tasks[websocket] = task_id

        self.logger.info(f"WebSocket connected for task {task_id}")
        
        # 简单的连接确认
        self.logger.info(f"WebSocket ready for task {task_id}")
    
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        task_id = self.connection_tasks.get(websocket)
        if task_id:
            # 从连接池中移除
            if task_id in self.active_connections:
                self.active_connections[task_id].discard(websocket)
                if not self.active_connections[task_id]:
                    del self.active_connections[task_id]
            
            # 移除映射
            del self.connection_tasks[websocket]
            self.logger.info(f"WebSocket disconnected for task {task_id}")
    
    async def send_to_connection(self, websocket: WebSocket, data: Dict[str, Any]):
        """发送消息到特定连接"""
        try:
            await websocket.send_text(json.dumps(data, ensure_ascii=False))
        except Exception as e:
            self.logger.error(f"Failed to send message to WebSocket: {e}")
            self.disconnect(websocket)
    
    async def broadcast_to_task(self, task_id: str, data: Dict[str, Any]):
        """广播消息到任务的所有连接"""
        if task_id not in self.active_connections:
            return
        
        # 添加时间戳
        data["timestamp"] = datetime.now().isoformat()
        
        # 发送到所有连接
        disconnected_connections = []
        for websocket in self.active_connections[task_id]:
            try:
                await websocket.send_text(json.dumps(data, ensure_ascii=False))
            except Exception as e:
                self.logger.error(f"Failed to broadcast to WebSocket: {e}")
                disconnected_connections.append(websocket)
        
        # 清理断开的连接
        for websocket in disconnected_connections:
            self.disconnect(websocket)
    
    async def send_progress_update(self, task_id: str, progress_data: Dict[str, Any]):
        """发送进度更新"""
        message = {
            "type": "progress_update",
            **progress_data
        }
        await self.broadcast_to_task(task_id, message)
    
    async def send_log_message(self, task_id: str, log_data: Dict[str, Any]):
        """发送日志消息"""
        message = {
            "type": "log_message",
            **log_data
        }
        await self.broadcast_to_task(task_id, message)
    
    async def send_task_status_change(self, task_id: str, status: str, additional_data: Optional[Dict] = None):
        """发送任务状态变化"""
        message = {
            "type": "status_change",
            "task_id": task_id,
            "status": status
        }
        
        if additional_data:
            message.update(additional_data)
        
        await self.broadcast_to_task(task_id, message)
    
    async def send_error(self, task_id: str, error_message: str, error_code: Optional[str] = None):
        """发送错误消息"""
        message = {
            "type": "error",
            "task_id": task_id,
            "error_message": error_message
        }
        
        if error_code:
            message["error_code"] = error_code
        
        await self.broadcast_to_task(task_id, message)
    
    def get_connection_count(self, task_id: str) -> int:
        """获取任务的连接数量"""
        return len(self.active_connections.get(task_id, set()))
    
    def get_total_connections(self) -> int:
        """获取总连接数"""
        return sum(len(connections) for connections in self.active_connections.values())
    
    async def ping_all_connections(self):
        """向所有连接发送ping消息（保持连接活跃）"""
        ping_message = {
            "type": "ping",
            "timestamp": datetime.now().isoformat()
        }
        
        for task_id in list(self.active_connections.keys()):
            await self.broadcast_to_task(task_id, ping_message)

class CrawlProgressWebSocket:
    """爬虫进度WebSocket处理器"""
    
    def __init__(self, websocket_manager: WebSocketManager):
        self.websocket_manager = websocket_manager
        self.logger = logging.getLogger("crawl_progress_ws")
    
    async def handle_connection(self, websocket: WebSocket, task_id: str):
        """处理WebSocket连接"""
        await self.websocket_manager.connect(websocket, task_id)
        
        try:
            # 保持连接活跃
            while True:
                try:
                    # 等待客户端消息
                    data = await websocket.receive_text()
                    await self._handle_client_message(websocket, task_id, data)
                except WebSocketDisconnect:
                    break
                except Exception as e:
                    self.logger.error(f"Error in WebSocket loop: {e}")
                    break
                    
        except WebSocketDisconnect:
            pass
        except Exception as e:
            self.logger.error(f"WebSocket connection error: {e}")
        finally:
            self.websocket_manager.disconnect(websocket)
    
    async def _handle_client_message(self, websocket: WebSocket, task_id: str, message: str):
        """处理客户端消息"""
        try:
            data = json.loads(message)
            message_type = data.get("type")
            
            if message_type == "ping":
                # 响应ping请求
                await self.websocket_manager.send_to_connection(websocket, {
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                })
            
            elif message_type == "get_status":
                # 发送当前任务状态（简化版本）
                from ..crawlers.crawler_manager import task_manager
                try:
                    task_status = task_manager.get_task_status(task_id)
                    await self.websocket_manager.send_to_connection(websocket, {
                        "type": "current_status",
                        "task_id": task_id,
                        "status": task_status.get("status", "unknown") if task_status else "not_found"
                    })
                except Exception as e:
                    await self.websocket_manager.send_to_connection(websocket, {
                        "type": "error",
                        "message": f"无法获取任务状态: {str(e)}"
                    })
            
            elif message_type == "cancel_task":
                # 取消任务请求
                from ..crawlers.crawler_manager import task_manager
                success = task_manager.cancel_task(task_id)
                await self.websocket_manager.send_to_connection(websocket, {
                    "type": "cancel_response",
                    "success": success
                })
                
        except json.JSONDecodeError:
            self.logger.warning(f"Invalid JSON message from client: {message}")
        except Exception as e:
            self.logger.error(f"Error handling client message: {e}")

# 全局WebSocket管理器实例
websocket_manager = WebSocketManager()
crawl_progress_ws = CrawlProgressWebSocket(websocket_manager)