import os
import subprocess
import sys

def main():
    """
    一个包装器，用于在启动Uvicorn之前设置环境变量。
    这确保了在所有平台上日志输出都是无缓冲的。
    """
    # 设置环境变量以禁用Python的输出缓冲
    os.environ['PYTHONUNBUFFERED'] = '1'
    
    # 构建Uvicorn命令
    # 我们从sys.argv中获取额外的参数，
    # 这样我们就可以像 poetry run start --port 8003 这样传递参数了
    command = [
        "uvicorn",
        "main:app",
        "--host", "0.0.0.0"
    ] + sys.argv[1:]

    print(f"Starting server with command: {' '.join(command)}")

    # 使用subprocess执行命令
    # 这会将Uvicorn的控制权完全交给当前终端
    try:
        subprocess.run(command, check=True)
    except KeyboardInterrupt:
        print("\nServer stopped by user.")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    main() 