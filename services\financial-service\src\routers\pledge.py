from fastapi import APIRouter, HTTPException, Depends, Query
import logging
import time
from typing import Dict, Any, Optional
from datetime import datetime

from ..models.schemas import (
    PledgeAnalysisRequest, PledgeAnalysisResponse,
    AnalysisResponse
)
from ..services.pledge_service import PledgeService
from ..dependencies import get_pledge_service

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/analyze", response_model=PledgeAnalysisResponse)
async def analyze_company_pledge(
    request: PledgeAnalysisRequest,
    pledge_service: PledgeService = Depends(get_pledge_service)
):
    """分析公司股权质押情况"""
    start_time = time.time()
    
    try:
        logger.info(f"Starting pledge analysis for company: {request.company_code}")
        
        result = await pledge_service.analyze_company_pledge(
            company_code=request.company_code,
            include_subsidiaries=request.include_subsidiaries,
            date_range=request.date_range
        )
        
        processing_time = time.time() - start_time
        logger.info(f"Pledge analysis completed in {processing_time:.2f}s")
        
        return result
        
    except Exception as e:
        logger.error(f"Pledge analysis error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to analyze pledge: {str(e)}"
        )


@router.get("/market-profile")
async def get_market_profile(
    pledge_service: PledgeService = Depends(get_pledge_service)
):
    """获取股权质押市场概况"""
    try:
        logger.info("Getting pledge market profile")
        
        result = await pledge_service.get_market_profile()
        
        return AnalysisResponse(
            success=True,
            message="Market profile retrieved successfully",
            data=result
        )
        
    except Exception as e:
        logger.error(f"Get market profile error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get market profile: {str(e)}"
        )


@router.get("/company-ratio")
async def get_company_pledge_ratio(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(50, ge=1, le=100, description="每页数量"),
    pledge_service: PledgeService = Depends(get_pledge_service)
):
    """获取公司质押比例排行"""
    try:
        logger.info(f"Getting company pledge ratio, page: {page}, size: {size}")
        
        result = await pledge_service.get_company_pledge_ratio(page, size)
        
        return AnalysisResponse(
            success=True,
            message="Company pledge ratio retrieved successfully",
            data=result
        )
        
    except Exception as e:
        logger.error(f"Get company pledge ratio error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get company pledge ratio: {str(e)}"
        )


@router.get("/details")
async def get_pledge_details(
    company_code: Optional[str] = Query(None, description="公司代码"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(50, ge=1, le=100, description="每页数量"),
    pledge_service: PledgeService = Depends(get_pledge_service)
):
    """获取质押明细"""
    try:
        logger.info(f"Getting pledge details for company: {company_code}")
        
        result = await pledge_service.get_pledge_details(
            company_code=company_code,
            start_date=start_date,
            end_date=end_date,
            page=page,
            size=size
        )
        
        return AnalysisResponse(
            success=True,
            message="Pledge details retrieved successfully",
            data=result
        )
        
    except Exception as e:
        logger.error(f"Get pledge details error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get pledge details: {str(e)}"
        )


@router.get("/institution-stats/{institution_type}")
async def get_institution_stats(
    institution_type: str,
    pledge_service: PledgeService = Depends(get_pledge_service)
):
    """获取质押机构统计"""
    if institution_type not in ["securities", "bank"]:
        raise HTTPException(status_code=400, detail="Invalid institution type")
    
    try:
        logger.info(f"Getting institution stats for: {institution_type}")
        
        result = await pledge_service.get_institution_stats(institution_type)
        
        return AnalysisResponse(
            success=True,
            message="Institution stats retrieved successfully",
            data=result
        )
        
    except Exception as e:
        logger.error(f"Get institution stats error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get institution stats: {str(e)}"
        )


@router.get("/search-companies")
async def search_pledge_companies(
    keyword: str = Query(..., description="搜索关键词"),
    pledge_service: PledgeService = Depends(get_pledge_service)
):
    """搜索质押公司"""
    try:
        logger.info(f"Searching pledge companies with keyword: {keyword}")
        
        result = await pledge_service.search_companies(keyword)
        
        return AnalysisResponse(
            success=True,
            message="Companies searched successfully",
            data={"suggestions": result}
        )
        
    except Exception as e:
        logger.error(f"Search companies error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to search companies: {str(e)}"
        )


@router.get("/risk-analysis/{company_code}")
async def get_pledge_risk_analysis(
    company_code: str,
    pledge_service: PledgeService = Depends(get_pledge_service)
):
    """获取公司质押风险分析"""
    try:
        logger.info(f"Getting pledge risk analysis for: {company_code}")
        
        result = await pledge_service.get_risk_analysis(company_code)
        
        return AnalysisResponse(
            success=True,
            message="Risk analysis retrieved successfully",
            data=result
        )
        
    except Exception as e:
        logger.error(f"Get risk analysis error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get risk analysis: {str(e)}"
        )
