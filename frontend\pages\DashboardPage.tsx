import React, { useState, useMemo } from 'react';
import { Card, Row, Col, Typography, Input, Button, Badge } from 'antd';
import { useNavigate } from 'react-router-dom';
import { 
  Search, 
  Sparkles
} from 'lucide-react';
import useAuthStore from '@/store/auth';
import { TOOLS_DATA } from '@/utils/constants';
import { Tool } from '@/types';
import { MolecularBackground } from '@/components/MolecularBackground';
import { ToolCard } from '@/components/ToolCard';

const { Title, Text } = Typography;
const { Search: SearchInput } = Input;

// 随机欢迎用语
const welcomeMessages = [
  "在数据的海洋中探索无限可能，让洞察点亮前行的路",
  "每一次分析都是一场发现之旅，愿智慧伴您同行",
  "智能时代的探索者，让我们一起解锁数据的奥秘",
  "在这里，复杂的数据变成简单的答案",
  "用数据的语言诉说故事，让每个决策都闪闪发光",
  "欢迎来到智能分析的世界，每一个工具都是您的得力助手",
  "数据驱动未来，洞察创造价值，期待与您共同书写精彩",
  "在算法与直觉的交响中，发现属于您的灵感",
  "让数据成为您最好的朋友，在这里开启智慧之门"
];

export const DashboardPage: React.FC = () => {
    const { user } = useAuthStore();
    const navigate = useNavigate();
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('all');
  
    // 随机选择欢迎用语（每次渲染保持一致）
    const welcomeMessage = useMemo(() => {
      return welcomeMessages[Math.floor(Math.random() * welcomeMessages.length)];
    }, []);
  
    const handleToolClick = (tool: Tool) => {
      navigate(tool.path);
    };

    // 获取工具分类
    const categories = ['all', ...Array.from(new Set(TOOLS_DATA.map(tool => tool.category)))];
  
    const filteredTools = TOOLS_DATA.filter(tool => {
      const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          tool.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || tool.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });
  
    return (
      <div style={{ position: 'relative', minHeight: '100%', height: '100%' }}>
        <MolecularBackground />
        <div style={{ position: 'relative', zIndex: 1, padding: '32px', height: '100%' }}>
          {/* 主要内容区域 - 两栏布局 */}
          <Row gutter={[40, 32]} style={{ height: '100%', minHeight: '600px' }}>
            {/* 左侧：欢迎语 + 系统公告模块 */}
            <Col xs={24} lg={10} xl={8}>
              <div style={{ 
                display: 'flex', 
                flexDirection: 'column', 
                height: '100%'
              }}>
                {/* 欢迎区域（包含系统公告） */}
                <Card
            style={{ 
                    background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%)',
                    backdropFilter: 'blur(25px)',
                    border: '1px solid rgba(255, 255, 255, 0.15)',
                    borderRadius: '24px',
              position: 'relative',
                    overflow: 'hidden',
                    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)',
                    height: '100%'
            }}
                  bodyStyle={{ padding: '32px', height: '100%', display: 'flex', flexDirection: 'column' }}
          >
            {/* 装饰渐变 */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
                    height: '4px',
              background: 'linear-gradient(90deg, #2563eb 0%, #0891b2 50%, #22d3ee 100%)'
            }} />

                  {/* 装饰光点 */}
                  <div style={{
                    position: 'absolute',
                    top: '20px',
                    right: '20px',
                    width: '60px',
                    height: '60px',
                    borderRadius: '50%',
                    background: 'radial-gradient(circle, rgba(34, 211, 238, 0.1) 0%, transparent 70%)',
                    pointerEvents: 'none'
                  }} />

                  {/* 欢迎内容 */}
                  <div style={{ marginBottom: '32px' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
                      <Sparkles size={24} style={{ color: '#22d3ee' }} />
                      <Title level={3} style={{ 
                     color: 'white', 
                     margin: 0, 
                        fontSize: '24px',
                        fontWeight: 700,
                        background: 'linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%)',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent'
                      }}>
                        欢迎回来
                   </Title>
                    </div>
                    
                    <Text style={{ 
                      fontSize: '18px', 
                      color: 'white',
                      display: 'block',
                      marginBottom: '12px',
                      fontWeight: 600
                    }}>
                      {user?.username} 👋
                    </Text>
                    
                   <Text style={{ 
                      fontSize: '14px', 
                     color: 'rgba(255, 255, 255, 0.7)',
                     display: 'block',
                      lineHeight: 1.6,
                      fontStyle: 'italic'
                   }}>
                      {welcomeMessage}
                   </Text>
                 </div>

                 {/* 嵌入的系统公告 */}
                  <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                 <div style={{
                   background: 'rgba(255, 255, 255, 0.08)',
                      border: '1px solid rgba(255, 255, 255, 0.15)',
                      borderRadius: '16px',
                      padding: '24px',
                      flex: 1,
                      display: 'flex',
                      flexDirection: 'column'
                    }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '20px' }}>
                        <Title level={5} style={{ 
                       color: 'white', 
                          margin: 0,
                          fontSize: '16px',
                          fontWeight: 600
                     }}>
                       系统公告
                        </Title>
                     <Badge count="3" size="small" style={{ marginLeft: 'auto' }} />
                   </div>

                      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '14px' }}>
                        {/* 公告项 */}
                   <div style={{
                     background: 'rgba(16, 185, 129, 0.1)',
                          border: '1px solid rgba(16, 185, 129, 0.25)',
                          borderRadius: '10px',
                          padding: '14px',
                          position: 'relative'
                        }}>
                          <div style={{
                            position: 'absolute',
                            top: '10px',
                            right: '10px',
                            width: '6px',
                            height: '6px',
                            borderRadius: '50%',
                            background: '#10b981'
                          }} />
                     <Text style={{ 
                       color: '#10b981', 
                       fontSize: '12px',
                            fontWeight: 600,
                       margin: 0,
                            display: 'block',
                            marginBottom: '6px'
                     }}>
                            [新功能] 股权穿透分析工具上线，点击查看
                     </Text>
                   <Text style={{ 
                            color: 'rgba(255, 255, 255, 0.7)', 
                     fontSize: '11px',
                     lineHeight: 1.4,
                     margin: 0
                   }}>
                     深度挖掘公司股权结构，一键生成关系网络图
                   </Text>
                        </div>

                        <div style={{
                          background: 'rgba(59, 130, 246, 0.1)',
                          border: '1px solid rgba(59, 130, 246, 0.25)',
                          borderRadius: '10px',
                          padding: '14px'
                        }}>
                          <Text style={{ 
                       color: '#3b82f6',
                            fontSize: '12px',
                            fontWeight: 600,
                            margin: 0,
                            display: 'block',
                            marginBottom: '6px'
                          }}>
                            [系统维护] 平台将于今晚进行例行维护
                          </Text>
                          <Text style={{ 
                            color: 'rgba(255, 255, 255, 0.7)', 
                       fontSize: '11px',
                            lineHeight: 1.4,
                            margin: 0
                          }}>
                            维护时间：凌晨2点至4点，服务可能短暂中断
                          </Text>
                 </div>
          </div>

                      <Button
                        type="text"
                        style={{
                          color: '#22d3ee',
                          fontSize: '12px',
                          padding: '8px 0',
                          height: 'auto',
                          marginTop: '16px',
                          alignSelf: 'flex-start'
                        }}
                      >
                        查看全部公告 →
                      </Button>
                    </div>
                  </div>
                </Card>
              </div>
            </Col>

            {/* 右侧：工具矩阵 */}
            <Col xs={24} lg={14} xl={16}>
              <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                {/* 工具矩阵头部 */}
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center', 
                  marginBottom: '24px',
                  flexWrap: 'wrap',
                  gap: '16px'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <Title level={3} style={{ 
                    color: 'white', 
                    margin: 0,
                      fontSize: '24px',
                      fontWeight: 700
                  }}>
                    工具矩阵
                    </Title>
                    <Badge count={filteredTools.length} 
                           style={{ backgroundColor: '#2563eb' }} 
                           showZero />
                  </div>
                  
                  <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
                    <SearchInput
                      placeholder="搜索工具..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      style={{ width: 280 }}
                      allowClear
                      prefix={<Search size={16} />}
                    />
                  </div>
                </div>

                {/* 分类筛选 */}
                <div style={{ marginBottom: '24px' }}>
                  <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                    {categories.map(category => (
                      <Button
                        key={category}
                        size="middle"
                        type={selectedCategory === category ? 'primary' : 'default'}
                        onClick={() => setSelectedCategory(category)}
                        style={{
                          borderRadius: '20px',
                          background: selectedCategory === category 
                            ? 'linear-gradient(135deg, #2563eb 0%, #0891b2 100%)'
                            : 'rgba(255, 255, 255, 0.06)',
                          border: selectedCategory === category 
                            ? 'none'
                            : '1px solid rgba(255, 255, 255, 0.12)',
                          color: selectedCategory === category ? 'white' : 'rgba(255, 255, 255, 0.8)',
                          fontWeight: selectedCategory === category ? 600 : 400,
                          height: '36px',
                          paddingLeft: '20px',
                          paddingRight: '20px'
                        }}
                      >
                        {category === 'all' ? '全部' : category}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* 工具卡片容器 */}
                <Card
                  style={{
                    background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.02) 100%)',
                    backdropFilter: 'blur(25px) saturate(1.3)',
                    border: '1px solid rgba(255, 255, 255, 0.12)',
                    borderRadius: '24px',
                    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)',
                    position: 'relative',
                    overflow: 'hidden',
                    flex: 1
                  }}
                  bodyStyle={{ padding: '32px', height: '100%' }}
                >
                  {/* 装饰光点 */}
                  <div style={{
                    position: 'absolute',
                    top: '30px',
                    right: '30px',
                    width: '80px',
                    height: '80px',
                    borderRadius: '50%',
                    background: 'radial-gradient(circle, rgba(37, 99, 235, 0.05) 0%, transparent 70%)',
                    pointerEvents: 'none'
                  }} />

                  {filteredTools.length > 0 ? (
                    <div style={{
                      height: '100%',
                      overflow: 'auto',
                      paddingRight: '8px',
                      minHeight: '400px'
                    }}>
                      <Row gutter={[24, 24]}>
                      {filteredTools.map(tool => (
                          <Col key={tool.id} xs={12} sm={12} md={8} lg={6} xl={4} xxl={3}>
                          <ToolCard tool={tool} onClick={() => handleToolClick(tool)} />
                        </Col>
                      ))}
                    </Row>
                    </div>
                  ) : (
                    <div style={{ 
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'rgba(255, 255, 255, 0.6)'
                    }}>
                      <Search size={48} style={{ marginBottom: '16px', opacity: 0.3 }} />
                      <div style={{ fontSize: '16px', marginBottom: '8px' }}>未找到匹配的工具</div>
                      <div style={{ fontSize: '14px' }}>请尝试其他搜索关键词</div>
                    </div>
                  )}
                </Card>
              </div>
            </Col>
          </Row>
        </div>
      </div>
    );
};