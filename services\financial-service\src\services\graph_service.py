from typing import Dict, List, Any, Optional
from ..graph.neo4j_client import Neo4<PERSON><PERSON>lient
from ..config.settings import settings
import logging

logger = logging.getLogger(__name__)


class GraphService:
    def __init__(self):
        self.neo4j_client = None

    async def initialize(self):
        """初始化图数据服务"""
        self.neo4j_client = Neo4jClient(
            uri=settings.NEO4J_URI,
            username=settings.NEO4J_USER,
            password=settings.NEO4J_PASSWORD
        )
        await self.neo4j_client.connect()
        await self.neo4j_client.create_indexes()
        logger.info("GraphService初始化完成")

    async def close(self):
        """关闭服务"""
        if self.neo4j_client:
            await self.neo4j_client.close()

    async def create_company_node(self, company_data: Dict[str, Any]) -> Dict:
        """创建公司节点"""
        query = """
        MERGE (c:Company {unified_code: $unified_code})
        SET c.name = $name,
            c.legal_representative = $legal_representative,
            c.registered_capital = $registered_capital,
            c.establishment_date = $establishment_date,
            c.business_status = $business_status,
            c.company_type = $company_type,
            c.industry = $industry,
            c.address = $address,
            c.updated_at = datetime()
        RETURN c
        """
        result = await self.neo4j_client.execute_write_query(query, company_data)
        return result

    async def create_person_node(self, person_data: Dict[str, Any]) -> Dict:
        """创建个人节点"""
        query = """
        MERGE (p:Person {id_number: $id_number})
        SET p.name = $name,
            p.gender = $gender,
            p.nationality = $nationality,
            p.address = $address,
            p.updated_at = datetime()
        RETURN p
        """
        result = await self.neo4j_client.execute_write_query(query, person_data)
        return result

    async def create_investment_relationship(self, investor_id: str, company_id: str, 
                                          investment_data: Dict[str, Any]) -> Dict:
        """创建投资关系"""
        query = """
        MATCH (investor), (company:Company {unified_code: $company_id})
        WHERE (investor:Company {unified_code: $investor_id}) OR (investor:Person {id_number: $investor_id})
        MERGE (investor)-[r:INVESTS_IN]->(company)
        SET r.investment_amount = $investment_amount,
            r.investment_date = $investment_date,
            r.investment_type = $investment_type,
            r.investment_ratio = $investment_ratio,
            r.updated_at = datetime()
        RETURN r
        """
        params = {
            "investor_id": investor_id,
            "company_id": company_id,
            **investment_data
        }
        result = await self.neo4j_client.execute_write_query(query, params)
        return result

    async def create_shareholding_relationship(self, shareholder_id: str, company_id: str,
                                            shareholding_data: Dict[str, Any]) -> Dict:
        """创建持股关系"""
        query = """
        MATCH (shareholder), (company:Company {unified_code: $company_id})
        WHERE (shareholder:Company {unified_code: $shareholder_id}) OR (shareholder:Person {id_number: $shareholder_id})
        MERGE (shareholder)-[r:HOLDS_SHARES]->(company)
        SET r.shareholding_ratio = $shareholding_ratio,
            r.shareholding_amount = $shareholding_amount,
            r.shareholding_type = $shareholding_type,
            r.is_controlling = $is_controlling,
            r.updated_at = datetime()
        RETURN r
        """
        params = {
            "shareholder_id": shareholder_id,
            "company_id": company_id,
            **shareholding_data
        }
        result = await self.neo4j_client.execute_write_query(query, params)
        return result

    async def get_equity_penetration(self, company_name: str, max_depth: int = 10) -> List[Dict]:
        """获取股权穿透路径"""
        # Ensure max_depth is an integer to prevent injection
        safe_max_depth = int(max_depth)

        query = f"""
        MATCH path = (start:Company {{企业名称: $company_name}})<-[r:HOLDS_SHARE*1..{safe_max_depth}]-(shareholder)
        WHERE NOT (shareholder)<-[:HOLDS_SHARE]-()
        WITH path,
             [rel in relationships(path) | toFloat(replace(rel.percentage, '%', ''))] as percentages,
             [node in nodes(path) | {{
                 id: coalesce(node.tianyancha_id, node.name),
                 name: node.企业名称,
                 type: labels(node)[0],
                 注册资本: node.注册资本,
                 组织机构代码: node.组织机构代码,
                 成立日期: node.成立日期,
                 法定代表人: node.法定代表人,
                 天眼评分: node.天眼评分,
                 标签: node.标签,
                 行业: node.行业,
                 经营范围: node.经营范围,
                 status: node.status
             }}] as entities,
             reduce(total_percentage = 100.0, p in [rel in relationships(path) | toFloat(replace(rel.percentage, '%', ''))] | total_percentage * p / 100.0) as final_percentage
        RETURN entities,
               final_percentage,
               length(path) as depth
        ORDER BY final_percentage DESC
        LIMIT 50
        """
        params = {"company_name": company_name}
        result = await self.neo4j_client.execute_query(query, params)
        return result

    async def get_company_shareholders(self, company_id: str) -> List[Dict]:
        """获取公司股东信息"""
        query = """
        MATCH (shareholder)-[r:HOLDS_SHARES]->(company:Company {unified_code: $company_id})
        RETURN shareholder {
            .*, 
            id: CASE 
                WHEN 'Company' IN labels(shareholder) THEN shareholder.unified_code 
                ELSE shareholder.id_number 
            END,
            type: CASE 
                WHEN 'Company' IN labels(shareholder) THEN 'company' 
                ELSE 'person' 
            END
        } as shareholder,
        r {.*} as shareholding
        ORDER BY r.shareholding_ratio DESC
        """
        result = await self.neo4j_client.execute_query(query, {"company_id": company_id})
        return result

    async def get_investment_portfolio(self, investor_id: str) -> List[Dict]:
        """获取投资组合"""
        query = """
        MATCH (investor)-[r:INVESTS_IN]->(company:Company)
        WHERE (investor:Company {unified_code: $investor_id}) OR (investor:Person {id_number: $investor_id})
        RETURN company {.*} as company,
               r {.*} as investment
        ORDER BY r.investment_amount DESC
        """
        result = await self.neo4j_client.execute_query(query, {"investor_id": investor_id})
        return result

    async def get_bidirectional_equity_graph(self, company_name: str, up_depth: int, down_depth: int) -> Dict[str, List[Dict]]:
        """
        获取一个公司的双向股权图,返回扁平化的节点和边列表.
        -向上(股东)追溯 up_depth 层.
        -向下(投资)追溯 down_depth 层.
        -为边界节点添加扩展标记.
        """
        query = f"""
        MATCH (start:Company {{企业名称: $company_name}})
        
        // 双向遍历，收集节点和关系
        CALL {{
            WITH start
            // 向下遍历 (对外投资)
            MATCH path_down = (start)-[r:HOLDS_SHARE*0..{down_depth}]->(n)
            UNWIND nodes(path_down) as node
            UNWIND relationships(path_down) as rel
            RETURN node, rel
            
            UNION
            
            WITH start
            // 向上遍历 (股东)
            MATCH path_up = (start)<-[r:HOLDS_SHARE*0..{up_depth}]-(n)
            UNWIND nodes(path_up) as node
            UNWIND relationships(path_up) as rel
            RETURN node, rel
        }}
        
        WITH collect(DISTINCT node) as nodes, collect(DISTINCT rel) as rels
        
        // 对每个节点，检查是否有更多的关系（用于前端展开）
        UNWIND nodes as n
        
        // 检查是否有更多未被包含的子节点
        OPTIONAL MATCH (n)-[:HOLDS_SHARE]->(child)
        WHERE NOT child IN nodes
        WITH n, nodes, rels, count(DISTINCT child) > 0 as has_more_children
        
        // 检查是否有更多未被包含的父节点
        OPTIONAL MATCH (n)<-[:HOLDS_SHARE]-(parent)
        WHERE NOT parent IN nodes
        WITH n, nodes, rels, has_more_children, count(DISTINCT parent) > 0 as has_more_parents
        
        // 提前处理并收集节点信息，以优化性能
        WITH rels, collect(n {{
            id: coalesce(n.tianyancha_id, n.id_number, elementId(n)),
            name: coalesce(n.企业名称, n.name),
            type: CASE
                WHEN 'Partnership' IN labels(n) THEN 'partnership'
                WHEN 'Person' IN labels(n) THEN 'person'
                WHEN 'Company' IN labels(n) THEN 'company'
                ELSE 'unknown'
            END,
                properties: {{
                注册资本: n.注册资本,
                法定代表人: n.法定代表人,
                成立日期: n.成立日期,
                组织机构代码: n.组织机构代码,
                天眼评分: n.天眼评分,
                标签: n.标签,
                行业: n.行业,
                经营范围: n.经营范围,
                status: n.status
                }},
                expandable: {{
                    up: has_more_parents,
                    down: has_more_children
                }}
        }}) AS processed_nodes,
        
        // 处理边信息
            [r IN rels | {{
                source: coalesce(startNode(r).tianyancha_id, startNode(r).id_number, elementId(startNode(r))),
                target: coalesce(endNode(r).tianyancha_id, endNode(r).id_number, elementId(endNode(r))),
                type: type(r),
                properties: r {{ .percentage }}
            }}] AS edges

        RETURN processed_nodes as nodes, edges
        """

        params = {
            "company_name": company_name,
            "up_depth": up_depth,
            "down_depth": down_depth
        }
        
        result = await self.neo4j_client.execute_query(query, params)
        
        if not result:
            return {"nodes": [], "edges": []}
            
        return result[0]


    async def get_node_expansions(self, node_id: str, direction: str, depth: int = 1) -> Dict[str, List[Dict]]:
        """获取指定节点一个方向上的扩展图"""
        if direction == "down":
            path_match = f"(start)-[r:HOLDS_SHARE*1..{depth}]->(n)"
        elif direction == "up":
            path_match = f"(start)<-[r:HOLDS_SHARE*1..{depth}]-(n)"
        else:
            return {"nodes": [], "edges": []}

        query = f"""
        MATCH (start) WHERE start.tianyancha_id = $node_id OR start.id_number = $node_id
        
        MATCH path = {path_match}
        
        WITH collect(DISTINCT nodes(path)) as node_collections, collect(DISTINCT relationships(path)) as rel_collections
        
        UNWIND node_collections as collection
        UNWIND collection as node
        WITH collect(DISTINCT node) as nodes, rel_collections
        
        UNWIND rel_collections as collection
        UNWIND collection as rel
        WITH nodes, collect(DISTINCT rel) as rels

        // Similar expansion check as the main query
        UNWIND nodes as n
        OPTIONAL MATCH (n)-[:HOLDS_SHARE]->(child) WHERE NOT child IN nodes
        WITH n, nodes, rels, count(DISTINCT child) > 0 as has_more_children
        OPTIONAL MATCH (n)<-[:HOLDS_SHARE]-(parent) WHERE NOT parent IN nodes
        WITH n, nodes, rels, has_more_children, count(DISTINCT parent) > 0 as has_more_parents

        // 提前处理并收集节点信息
        WITH rels, collect(n {{
            id: coalesce(n.tianyancha_id, n.id_number, elementId(n)),
            name: coalesce(n.企业名称, n.name),
            type: CASE
                WHEN 'Partnership' IN labels(n) THEN 'partnership'
                WHEN 'Person' IN labels(n) THEN 'person'
                WHEN 'Company' IN labels(n) THEN 'company'
                ELSE 'unknown'
            END,
            properties: n {{
                    .注册资本, .法定代表人, .成立日期, .组织机构代码, .天眼评分, .标签, .行业, .经营范围, .status
            }},
            expandable: {{
                up: has_more_parents,
                down: has_more_children
            }}
        }}) AS processed_nodes,

        // 处理边信息
        [r IN rels | {{
                source: coalesce(startNode(r).tianyancha_id, startNode(r).id_number, elementId(startNode(r))),
                target: coalesce(endNode(r).tianyancha_id, endNode(r).id_number, elementId(endNode(r))),
                type: type(r),
            properties: r {{ .percentage }}
        }}] AS edges
        
        RETURN processed_nodes AS nodes, edges
        """

        params = {"node_id": node_id, "depth": depth}
        result = await self.neo4j_client.execute_query(query, params)
        
        if not result or not result[0]['nodes']:
            return {"nodes": [], "edges": []}
            
        return result[0]

    async def search_companies(self, keyword: str, limit: int = 20) -> List[Dict]:
        """
        通过多种策略搜索公司，支持中文和模糊搜索。
        注意：此功能依赖于已在Neo4j中创建并填充的名为'company_name_index'的全文本索引。
        """
        if not keyword or not keyword.strip():
            return []

        keyword = keyword.strip()
        results = []
        seen_ids = set()

        try:
            # 策略1: 精确匹配（最高优先级）
            exact_query = """
            MATCH (c:Company)
            WHERE c.企业名称 = $keyword OR c.name = $keyword
            RETURN c { name: c.企业名称, id: c.tianyancha_id } as company, 100 as score
            LIMIT 5
            """
            exact_results = await self.neo4j_client.execute_query(exact_query, {"keyword": keyword})
            for item in exact_results:
                if item['company']['id'] not in seen_ids:
                    results.append(item)
                    seen_ids.add(item['company']['id'])

            # 策略2: 前缀匹配（高优先级）
            if len(results) < limit:
                prefix_query = """
                MATCH (c:Company)
                WHERE c.企业名称 STARTS WITH $keyword OR c.name STARTS WITH $keyword
                RETURN c { name: c.企业名称, id: c.tianyancha_id } as company, 90 as score
                LIMIT $remaining_limit
                """
                remaining = limit - len(results)
                prefix_results = await self.neo4j_client.execute_query(
                    prefix_query,
                    {"keyword": keyword, "remaining_limit": remaining}
                )
                for item in prefix_results:
                    if item['company']['id'] not in seen_ids:
                        results.append(item)
                        seen_ids.add(item['company']['id'])

            # 策略3: 包含匹配（中等优先级）
            if len(results) < limit:
                contains_query = """
                MATCH (c:Company)
                WHERE c.企业名称 CONTAINS $keyword OR c.name CONTAINS $keyword
                RETURN c { name: c.企业名称, id: c.tianyancha_id } as company, 80 as score
                LIMIT $remaining_limit
                """
                remaining = limit - len(results)
                contains_results = await self.neo4j_client.execute_query(
                    contains_query,
                    {"keyword": keyword, "remaining_limit": remaining}
                )
                for item in contains_results:
                    if item['company']['id'] not in seen_ids:
                        results.append(item)
                        seen_ids.add(item['company']['id'])

            # 策略4: 全文索引模糊搜索（较低优先级）
            if len(results) < limit:
                try:
                    # 构建更智能的搜索查询
                    search_terms = []

                    # 原始关键词
                    search_terms.append(f'"{keyword}"')  # 精确短语
                    search_terms.append(keyword)  # 普通搜索

                    # 如果是中文，尝试拆分字符
                    if any('\u4e00' <= char <= '\u9fff' for char in keyword):
                        # 中文字符，尝试每个字符的搜索
                        if len(keyword) > 1:
                            char_terms = ' OR '.join([f'"{char}"' for char in keyword])
                            search_terms.append(f'({char_terms})')

                    # 模糊搜索
                    search_terms.append(f'{keyword}~')

                    # 组合搜索查询
                    search_query = ' OR '.join(search_terms)

                    fulltext_query = """
                    CALL db.index.fulltext.queryNodes("company_name_index", $search_query) YIELD node, score
                    WHERE score > 0.1
                    RETURN node { name: node.企业名称, id: node.tianyancha_id } as company, score
                    ORDER BY score DESC
                    LIMIT $remaining_limit
                    """
                    remaining = limit - len(results)
                    fulltext_results = await self.neo4j_client.execute_query(
                        fulltext_query,
                        {"search_query": search_query, "remaining_limit": remaining}
                    )
                    for item in fulltext_results:
                        if item['company']['id'] not in seen_ids:
                            results.append(item)
                            seen_ids.add(item['company']['id'])
                except Exception as e:
                    # 如果全文索引失败，记录错误但不影响其他搜索结果
                    print(f"全文索引搜索失败: {e}")

            # 按分数排序并返回
            results.sort(key=lambda x: x['score'], reverse=True)
            return [item['company'] for item in results[:limit]]

        except Exception as e:
            print(f"搜索公司时发生错误: {e}")
            # 如果所有策略都失败，返回空列表
            return []


    async def get_related_companies(self, company_id: str, limit: int = 20) -> List[Dict]:
        """获取关联公司"""
        relationship_types = ["HOLDS_SHARES", "INVESTS_IN"]
        
        query = f"""
        MATCH (c1:Company {{unified_code: $company_id}})-[r:{"|".join(relationship_types)}]-(c2:Company)
        RETURN DISTINCT c2 {{.*}} as company,
               type(r) as relationship_type,
               r {{.*}} as relationship
        ORDER BY c2.name
        LIMIT $limit
        """
        result = await self.neo4j_client.execute_query(query, {"company_id": company_id, "limit": limit})
        return result

    async def get_control_chain(self, company_id: str) -> List[Dict]:
        """获取控制链"""
        # Implementation of get_control_chain function goes here
        pass