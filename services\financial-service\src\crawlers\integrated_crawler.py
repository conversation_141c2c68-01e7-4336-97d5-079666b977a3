#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成爬虫模块
- 整合现有的tianyancha_scraper和tree_crawler
- 添加进度回调支持
- 优化用户体验和错误处理
"""
import asyncio
import time
import random
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
import logging
import re
from urllib.parse import urljoin

from .progress_logger import ProgressLogger
from .tree_crawler import TreeCrawlerAsync
from ..graph.neo4j_client import Neo4jClient
from ..config.settings import get_settings

class IntegratedCrawler:
    """集成的股权穿透爬虫"""
    
    def __init__(self, neo4j_client: Neo4jClient):
        self.neo4j_client = neo4j_client
        self.settings = get_settings()
        self.logger = logging.getLogger("integrated_crawler")
        
    async def crawl_company_equity(self, company_name: str, depth: int = 2, 
                                 direction: str = "both", 
                                 progress_logger: Optional[ProgressLogger] = None) -> Dict[str, Any]:
        """
        爬取公司股权关系
        
        Args:
            company_name: 公司名称
            depth: 穿透深度
            direction: 方向 ('up', 'down', 'both')
            progress_logger: 进度日志记录器
        
        Returns:
            包含节点和边的图数据
        """
        # 设置开始时间
        start_time = time.time()
        if progress_logger:
            progress_logger.start_time = start_time
            progress_logger.set_current_phase("初始化爬虫")
        
        try:
            # 使用真实的TreeCrawler进行爬取
            tree_crawler = TreeCrawlerAsync(
                neo4j_client=self.neo4j_client,
                progress_logger=progress_logger
            )
            
            # 执行爬取
            result = await tree_crawler.crawl_equity_tree(
                company_name=company_name,
                depth=depth,
                direction=direction
            )
            
            if progress_logger:
                total_duration = time.time() - start_time
                progress_logger.log_final_stats(
                    total_companies=len(result.get('nodes', [])),
                    total_relationships=len(result.get('edges', [])),
                    duration=total_duration
                )
            
            return result
            
        except Exception as e:
            if progress_logger:
                progress_logger.log_error("系统", str(e))
            self.logger.error(f"Crawl failed for {company_name}: {e}")
            raise

# 为任务管理器提供的包装函数
async def crawl_company_equity_task(company_name: str, depth: int = 2,
                                   direction: str = "both",
                                   progress_logger: Optional[ProgressLogger] = None,
                                   neo4j_client: Optional[Neo4jClient] = None) -> Dict[str, Any]:
    """
    任务管理器调用的爬虫函数
    """
    # 如果没有提供neo4j_client，则创建一个新的连接
    if not neo4j_client:
        from ..graph.neo4j_client import Neo4jClient
        from ..config.settings import get_settings

        settings = get_settings()
        neo4j_client = Neo4jClient(
            uri=settings.NEO4J_URI,
            username=settings.NEO4J_USER,
            password=settings.NEO4J_PASSWORD
        )
        await neo4j_client.connect()

    crawler = IntegratedCrawler(neo4j_client=neo4j_client)

    return await crawler.crawl_company_equity(
        company_name=company_name,
        depth=depth,
        direction=direction,
        progress_logger=progress_logger
    )