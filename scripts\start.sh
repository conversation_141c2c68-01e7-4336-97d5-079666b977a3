#!/bin/bash

# IDEALAB微服务快速启动脚本
# 使用方法: ./scripts/start.sh [service_name]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
}

# 检查环境变量文件
check_env() {
    if [ ! -f .env ]; then
        log_warning ".env文件不存在，正在从.env.example创建..."
        cp .env.example .env
        log_warning "请编辑.env文件，填入正确的环境变量值"
        log_warning "然后重新运行此脚本"
        exit 1
    fi
}

# 启动所有服务
start_all() {
    log_info "启动所有微服务..."
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动完成..."
    sleep 10
    
    # 检查服务状态
    check_services
}

# 启动特定服务
start_service() {
    local service=$1
    log_info "启动服务: $service"
    docker-compose up -d $service
    
    # 等待服务启动
    sleep 5
    
    # 检查特定服务状态
    check_service_health $service
}

# 检查服务健康状态
check_services() {
    local services=("gateway" "auth" "nlp-service" "financial-service" "document-service" "frontend")
    local ports=("8000" "8001" "8002" "8003" "8004" "3000")
    
    log_info "检查服务健康状态..."
    
    for i in "${!services[@]}"; do
        local service=${services[$i]}
        local port=${ports[$i]}
        check_service_health $service $port
    done
}

# 检查单个服务健康状态
check_service_health() {
    local service=$1
    local port=${2:-""}
    
    if [ -n "$port" ]; then
        if curl -f http://localhost:$port/health &> /dev/null; then
            log_success "$service 服务运行正常 (端口: $port)"
        else
            log_warning "$service 服务可能未完全启动 (端口: $port)"
        fi
    else
        if docker-compose ps $service | grep -q "Up"; then
            log_success "$service 容器运行正常"
        else
            log_error "$service 容器启动失败"
        fi
    fi
}

# 显示服务状态
show_status() {
    log_info "显示所有服务状态..."
    docker-compose ps
    
    echo ""
    log_info "服务访问地址:"
    echo "  🌐 前端应用: http://localhost:3000"
    echo "  🚪 API网关: http://localhost:8000"
    echo "  🔐 认证服务: http://localhost:8001/health"
    echo "  🤖 NLP服务: http://localhost:8002/health"
    echo "  📊 金融服务: http://localhost:8003/health"
    echo "  📄 文档服务: http://localhost:8004/health"
}

# 停止所有服务
stop_all() {
    log_info "停止所有服务..."
    docker-compose down
    log_success "所有服务已停止"
}

# 重启所有服务
restart_all() {
    log_info "重启所有服务..."
    docker-compose restart
    sleep 10
    check_services
}

# 查看日志
show_logs() {
    local service=${1:-""}
    if [ -n "$service" ]; then
        log_info "显示 $service 服务日志..."
        docker-compose logs -f $service
    else
        log_info "显示所有服务日志..."
        docker-compose logs -f
    fi
}

# 清理资源
cleanup() {
    log_warning "这将删除所有容器、镜像和数据卷，确定要继续吗? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "清理Docker资源..."
        docker-compose down -v --rmi all
        log_success "清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 主函数
main() {
    local command=${1:-"start"}
    local service=${2:-""}
    
    # 检查依赖
    check_docker
    check_env
    
    case $command in
        "start")
            if [ -n "$service" ]; then
                start_service $service
            else
                start_all
            fi
            ;;
        "stop")
            stop_all
            ;;
        "restart")
            restart_all
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs $service
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"-h"|"--help")
            echo "IDEALAB微服务管理脚本"
            echo ""
            echo "用法:"
            echo "  $0 [command] [service]"
            echo ""
            echo "命令:"
            echo "  start [service]  启动所有服务或指定服务"
            echo "  stop             停止所有服务"
            echo "  restart          重启所有服务"
            echo "  status           显示服务状态"
            echo "  logs [service]   查看日志"
            echo "  cleanup          清理所有资源"
            echo "  help             显示帮助信息"
            echo ""
            echo "服务名:"
            echo "  gateway, auth, nlp-service, financial-service, document-service, frontend"
            ;;
        *)
            log_error "未知命令: $command"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"