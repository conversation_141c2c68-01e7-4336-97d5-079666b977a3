import React from 'react';
import { Card, CardProps } from 'antd';

export const GlassmorphicCard: React.FC<CardProps> = ({ children, className, ...props }) => (
    <Card
        {...props}
        className={`glassmorphic-card ${className}`}
        style={{
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.06) 100%)',
            backdropFilter: 'blur(14px) saturate(1.2)',
            border: '1px solid var(--border-primary)',
            borderRadius: '12px',
            boxShadow: '0 8px 32px rgba(0,0,0,0.2), inset 0 1px 0 rgba(255,255,255,0.12)',
            height: '100%',
            ...props.style,
        }}
    >
        {children}
    </Card>
); 