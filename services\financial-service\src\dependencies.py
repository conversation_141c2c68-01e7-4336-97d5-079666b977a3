from fastapi import Depends, HTTPException
from starlette.requests import Request

from .services.graph_service import GraphService
from .services.equity_service import EquityService


def get_graph_service(request: Request) -> GraphService:
    """依赖注入：从应用状态获取图数据库服务实例"""
    graph_service = request.app.state.graph_service
    if not graph_service:
        raise HTTPException(status_code=500, detail="Graph service is not initialized")
    return graph_service


def get_equity_service(
    graph_service: GraphService = Depends(get_graph_service)
) -> EquityService:
    """依赖注入：获取股权分析服务实例"""
    return EquityService(graph_service=graph_service) 