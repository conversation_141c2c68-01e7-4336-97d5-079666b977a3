from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import logging
from contextlib import asynccontextmanager

from src.routers import equity, bond, analytics, realtime_equity
from src.config.settings import settings
from src.services.graph_service import GraphService
from src.services.data_service import DataService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("📊 Initializing Financial Data Service...")
    
    # 初始化图数据库服务
    graph_service = GraphService()
    await graph_service.initialize()
    app.state.graph_service = graph_service
    
    # 初始化数据服务
    data_service = DataService()
    await data_service.initialize()
    app.state.data_service = data_service
    
    logger.info("✅ Financial Data Service initialized successfully")
    
    yield
    
    # 关闭时清理
    logger.info("🛑 Shutting down Financial Data Service...")
    await graph_service.close()


# 创建FastAPI应用
app = FastAPI(
    title="IDEALAB Financial Data Service",
    description="Financial data analysis service with graph database support",
    version="1.0.0",
    lifespan=lifespan
)

# 中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "service": "financial-service",
        "status": "healthy",
        "version": "1.0.0",
        "components": {
            "graph_database": "connected",
            "data_service": "ready",
            "analytics": "available"
        }
    }


@app.get("/")
async def root():
    """根端点"""
    return {
        "message": "IDEALAB Financial Data Service",
        "version": "1.0.0",
        "capabilities": [
            "股权穿透分析",
            "股权质押分析", 
            "债券市场分析",
            "企业关系图谱",
            "风险评估"
        ],
        "endpoints": {
            "equity": "/api/equity/*",
            "bond": "/api/bond/*",
            "analytics": "/api/analytics/*",
            "health": "/health"
        }
    }


# 注册路由
app.include_router(equity.router, prefix="/financial", tags=["Equity Analysis"])
app.include_router(bond.router, prefix="/financial", tags=["Bond Analysis"])
app.include_router(analytics.router, prefix="/financial", tags=["Financial Analytics"])
app.include_router(realtime_equity.router, tags=["Realtime Equity Analysis"])


# 错误处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"Global exception: {exc}")
    return HTTPException(
        status_code=500,
        detail="Internal Financial Service Error"
    )


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )