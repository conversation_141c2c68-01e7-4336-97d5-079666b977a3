#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时股权穿透API路由
"""
from fastapi import APIRouter, HTTPException, Depends, WebSocket, WebSocketDisconnect, Query
from fastapi.security import HTT<PERSON><PERSON>earer
import logging
import json
import time
import asyncio
from typing import Optional, List

from ..models.crawl_schemas import (
    RealtimeCrawlRequest, StartCrawlResponse, TaskStatusResponse,
    CrawlCompleteResponse, TaskListResponse
)
from ..services.realtime_crawl_service import realtime_crawl_service
from ..services.websocket_service import crawl_progress_ws
from ..crawlers.crawler_manager import task_manager

# 注意：网关会将外部 '/api' 前缀去除，服务内部应使用不含 '/api' 的前缀
router = APIRouter(prefix="/equity", tags=["实时股权分析"])
security = HTTPBearer()
logger = logging.getLogger("realtime_equity_api")

# 临时用户ID获取函数（实际应用中应该从JWT token中解析）
def get_current_user_id(token: str = Depends(security)) -> str:
    """获取当前用户ID"""
    # TODO: 实际应用中需要验证JWT token并提取user_id
    return "user_123"  # 临时返回固定用户ID

@router.post("/start-realtime-crawl", response_model=StartCrawlResponse)
async def start_realtime_crawl(
    request: RealtimeCrawlRequest,
    user_id: str = Depends(get_current_user_id)
):
    """
    启动实时股权穿透分析
    
    - **company_name**: 公司名称（必填）
    - **depth**: 穿透深度，1-5层（默认2层）
    - **direction**: 分析方向，up（股东）/down（投资）/both（双向，默认）
    - **force_refresh**: 是否强制刷新缓存（默认false）
    
    返回任务ID和WebSocket连接地址，用于实时获取分析进度
    """
    try:
        logger.info(f"User {user_id} requested crawl for {request.company_name}")
        
        response = await realtime_crawl_service.start_crawl(request, user_id)
        
        logger.info(f"Crawl task {response.task_id} started successfully")
        return response
        
    except Exception as e:
        logger.error(f"Failed to start crawl: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/task-status/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(
    task_id: str,
    user_id: str = Depends(get_current_user_id)
):
    """
    获取任务状态
    
    返回指定任务的当前状态、进度和其他详细信息
    """
    try:
        task_status = await realtime_crawl_service.get_task_status(task_id)
        
        if not task_status:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return task_status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get task status for {task_id}: {e}")
        raise HTTPException(status_code=500, detail="获取任务状态失败")

@router.post("/cancel-task/{task_id}")
async def cancel_task(
    task_id: str,
    user_id: str = Depends(get_current_user_id)
):
    """
    取消正在进行的任务
    
    只有任务创建者可以取消自己的任务
    """
    try:
        success = await realtime_crawl_service.cancel_task(task_id, user_id)
        
        if not success:
            raise HTTPException(status_code=400, detail="无法取消任务，可能任务不存在或已完成")
        
        return {"message": "任务已取消", "task_id": task_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel task {task_id}: {e}")
        raise HTTPException(status_code=500, detail="取消任务失败")

@router.get("/my-tasks", response_model=TaskListResponse)
async def get_my_tasks(
    user_id: str = Depends(get_current_user_id),
    status: Optional[str] = Query(None, description="按状态过滤"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制")
):
    """
    获取我的任务列表
    
    返回当前用户的所有爬取任务，支持状态过滤
    """
    try:
        tasks = await realtime_crawl_service.get_user_tasks(user_id)
        
        # 状态过滤
        if status:
            tasks = [task for task in tasks if task.status == status]
        
        # 限制数量
        tasks = tasks[:limit]
        
        return TaskListResponse(
            tasks=tasks,
            total=len(tasks)
        )
        
    except Exception as e:
        logger.error(f"Failed to get tasks for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="获取任务列表失败")

@router.get("/crawl-result/{task_id}", response_model=CrawlCompleteResponse)
async def get_crawl_result(
    task_id: str,
    user_id: str = Depends(get_current_user_id)
):
    """
    获取爬取结果
    
    返回已完成任务的详细分析结果，包括节点和边数据
    """
    try:
        result = await realtime_crawl_service.get_crawl_result(task_id)
        
        if not result:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get crawl result for {task_id}: {e}")
        raise HTTPException(status_code=500, detail="获取分析结果失败")

@router.get("/sse/crawl-progress/{task_id}")
async def crawl_progress_sse(task_id: str, token: str = Query(None)):
    """Server-Sent Events 实时进度推送"""
    from fastapi.responses import StreamingResponse

    async def event_stream():
        """SSE事件流生成器"""
        try:
            # 发送连接确认
            yield f"data: {json.dumps({'type': 'connection_established', 'task_id': task_id, 'message': '已连接到实时进度服务'}, ensure_ascii=False)}\n\n"

            # 获取任务管理器
            from ..crawlers.crawler_manager import task_manager

            start_time = time.time()
            last_status = None

            while True:
                # 检查任务是否存在
                if task_id not in task_manager.tasks:
                    yield f"data: {json.dumps({'type': 'error', 'message': '任务不存在'}, ensure_ascii=False)}\n\n"
                    break

                task = task_manager.tasks[task_id]

                # 获取实时消息（非阻塞）
                messages = task_manager.get_task_messages_nowait(task_id)
                for message in messages:
                    yield f"data: {json.dumps(message, ensure_ascii=False)}\n\n"

                # 检查任务状态变化
                current_status = str(task.status)
                if last_status != current_status:
                    status_data = {
                        "type": "status_change",
                        "task_id": task_id,
                        "status": current_status,
                        "progress": task.progress,
                        "timestamp": time.time()
                    }
                    yield f"data: {json.dumps(status_data, ensure_ascii=False)}\n\n"
                    last_status = current_status

                # 如果任务完成，发送最终状态并退出
                if task.status.value in ['completed', 'failed']:
                    final_data = {
                        "type": "status_change",
                        "task_id": task_id,
                        "status": task.status.value,
                        "progress": 100 if task.status.value == 'completed' else task.progress
                    }
                    if task.status.value == 'completed' and task.result:
                        final_data["result"] = task.result
                    elif task.status.value == 'failed' and task.error_message:
                        final_data["error"] = task.error_message

                    yield f"data: {json.dumps(final_data, ensure_ascii=False)}\n\n"
                    break

                # 短暂等待，避免过度占用CPU
                await asyncio.sleep(0.5)

                # 超时保护（5分钟）
                if time.time() - start_time > 300:
                    yield f"data: {json.dumps({'type': 'timeout', 'message': '连接超时'}, ensure_ascii=False)}\n\n"
                    break

        except Exception as e:
            logger.error(f"SSE stream error for task {task_id}: {e}")
            yield f"data: {json.dumps({'type': 'error', 'message': str(e)}, ensure_ascii=False)}\n\n"

    return StreamingResponse(
        event_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*"
        }
    )

@router.get("/system-status")
async def get_system_status(user_id: str = Depends(get_current_user_id)):
    """
    获取系统状态
    
    返回爬虫系统的整体运行状态和统计信息
    """
    try:
        from ..services.websocket_service import websocket_manager
        from datetime import datetime
        
        running_tasks = task_manager.get_running_tasks_count()
        total_connections = websocket_manager.get_total_connections()
        
        return {
            "crawl_stats": {
                "running_tasks": running_tasks,
                "total_connections": total_connections,
                "max_concurrent_tasks": 3
            },
            "server_time": datetime.now().isoformat(),
            "status": "healthy" if running_tasks < 3 else "busy"
        }
        
    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        raise HTTPException(status_code=500, detail="获取系统状态失败")

# 健康检查端点
@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": logger.info("Health check requested"),
        "service": "realtime-equity-crawler"
    }