/* 股权质押工具样式 */

/* 表格暗色主题优化 */
.ant-table-thead > tr > th {
  background: rgba(255, 255, 255, 0.08) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.ant-table-tbody > tr > td {
  background: transparent !important;
  color: rgba(255, 255, 255, 0.8) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

.ant-table-tbody > tr:hover > td {
  background: rgba(255, 255, 255, 0.05) !important;
}

.ant-table-wrapper {
  background: transparent !important;
}

.ant-table {
  background: transparent !important;
}

.ant-table-container {
  background: transparent !important;
}

/* 分页器暗色主题 */
.ant-pagination .ant-pagination-item {
  background: rgba(255, 255, 255, 0.08) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.ant-pagination .ant-pagination-item a {
  color: rgba(255, 255, 255, 0.8) !important;
}

.ant-pagination .ant-pagination-item-active {
  background: rgba(24, 144, 255, 0.2) !important;
  border-color: #1890ff !important;
}

.ant-pagination .ant-pagination-item-active a {
  color: #1890ff !important;
}

/* 标签页暗色主题 */
.ant-tabs-tab {
  color: rgba(255, 255, 255, 0.6) !important;
}

.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: rgba(255, 255, 255, 0.9) !important;
}

.ant-tabs-ink-bar {
  background: #1890ff !important;
}

.ant-tabs-content-holder {
  background: transparent !important;
}

/* 进度条暗色主题 */
.ant-progress-text {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* 选择器暗色主题 */
.ant-select-dropdown {
  background: rgba(0, 0, 0, 0.8) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.ant-select-item {
  color: rgba(255, 255, 255, 0.8) !important;
}

.ant-select-item-option-selected {
  background: rgba(24, 144, 255, 0.2) !important;
  color: #1890ff !important;
}

.ant-select-item-option-active {
  background: rgba(255, 255, 255, 0.05) !important;
}

/* 日期选择器暗色主题 */
.ant-picker-dropdown {
  background: rgba(0, 0, 0, 0.8) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.ant-picker-panel {
  background: transparent !important;
  border: none !important;
}

.ant-picker-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.ant-picker-header button {
  color: rgba(255, 255, 255, 0.8) !important;
}

.ant-picker-content th,
.ant-picker-content td {
  color: rgba(255, 255, 255, 0.8) !important;
}

.ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner {
  background: #1890ff !important;
  color: #fff !important;
}

.ant-picker-cell:hover:not(.ant-picker-cell-in-view) .ant-picker-cell-inner,
.ant-picker-cell:hover:not(.ant-picker-cell-selected):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end):not(.ant-picker-cell-range-hover-start):not(.ant-picker-cell-range-hover-end) .ant-picker-cell-inner {
  background: rgba(255, 255, 255, 0.1) !important;
}

/* 徽章暗色主题 */
.ant-badge-count {
  background: #ff4d4f !important;
  color: #fff !important;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
}

/* 空状态暗色主题 */
.ant-empty-description {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* 加载状态暗色主题 */
.ant-spin-dot-item {
  background-color: #1890ff !important;
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .ant-layout-sider {
    width: 280px !important;
  }
}

@media (max-width: 768px) {
  .ant-layout-sider {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 16px !important;
  }
  
  .ant-layout {
    flex-direction: column !important;
  }
}

/* 滚动条优化 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
