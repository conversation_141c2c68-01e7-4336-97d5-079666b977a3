const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/AdvancedEquityGraph-B6dIjYW0.js","assets/react-vendor-DnpxqCDv.js","assets/antd-DnRyuF5C.js","assets/cytoscape-DSQcBgoQ.js","assets/RealtimeCrawlModal-CdWpPaus.js"])))=>i.map(i=>d[i]);
import{r as x,j as r,C as _o,W as Go,D as Ct,S as zs,h as rt,Z as qo,T as Oe,U as nr,L as ke,A as Xo,B as Je,k as fs,l as ir,F as Dt,m as ps,n as ms,u as rr,o as Yo,p as Mn,R as Os,q as or,s as Zo,E as Pt,t as $s,I as ar,v as lr,M as Jo,w as Qo,x as Ut,y as ea,z as Rn,G as ta,H as sa,J as na,K as ia,N as ae,O as gs,P as ra}from"./react-vendor-DnpxqCDv.js";import{T as te,F as U,I as le,C as oa,B as V,s as k,a as Vt,R as kn,b as Ht,d as xs,e as ys,L as K,S as q,f as aa,h as la,D as ot,i as at,j as ca,k as ua,M as cr,l as da,m as Dn,n as ha,o as fa,p as pa,q as ma,r as Vn,E as ga,t as xa,u as ya,A as ur,v as va,w as ba,x as wa,z as ja}from"./antd-DnRyuF5C.js";import{c as Sa}from"./zustand-o7VuMf4o.js";import{a as Ta,A as Ca}from"./axios-DoAouP2O.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))n(i);new MutationObserver(i=>{for(const a of i)if(a.type==="childList")for(const o of a.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&n(o)}).observe(document,{childList:!0,subtree:!0});function s(i){const a={};return i.integrity&&(a.integrity=i.integrity),i.referrerPolicy&&(a.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?a.credentials="include":i.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function n(i){if(i.ep)return;i.ep=!0;const a=s(i);fetch(i.href,a)}})();const Pa="http://222.186.150.30:62266/api",Ae=Ta.create({baseURL:Pa,timeout:3e4,headers:{"Content-Type":"application/json"}});Ae.interceptors.request.use(e=>{var s;e.headers||(e.headers=new Ca);const t=me.getState().token;return t&&(e.headers.Authorization=`Bearer ${t}`),e.headers["X-Request-ID"]=Date.now().toString(),console.log(`[API Request] ${(s=e.method)==null?void 0:s.toUpperCase()} ${e.url}`),e},e=>(console.error("[API Request Error]",e),Promise.reject(e)));Ae.interceptors.response.use(e=>(console.log(`[API Response] ${e.status} ${e.config.url}`),e),e=>{var t,s,n;return console.error("[API Response Error]",e),((t=e.response)==null?void 0:t.status)===401&&window.location.pathname!=="/login"&&me.getState().logout(),((s=e.response)==null?void 0:s.status)===403&&console.error("权限不足"),((n=e.response)==null?void 0:n.status)>=500&&console.error("服务器错误，请稍后重试"),Promise.reject(e)});const X={get:(e,t)=>Ae.get(e,t).then(s=>s.data),post:(e,t,s)=>Ae.post(e,t,s).then(n=>n.data),put:(e,t,s)=>Ae.put(e,t,s).then(n=>n.data),delete:(e,t)=>Ae.delete(e,t).then(s=>s.data),upload:(e,t,s)=>Ae.post(e,t,{...s,headers:{"Content-Type":"multipart/form-data",...s==null?void 0:s.headers}}).then(n=>n.data)},me=Sa(e=>({user:null,token:null,isAuthenticated:!1,passwordChangeRequired:!1,login:async(t,s,n=!1)=>{try{const i=await X.post("/auth/login",{username:t,password:s});if(i.success&&i.data){const{user:a,tokens:o}=i.data;return e({user:a,token:o.accessToken,isAuthenticated:!0,passwordChangeRequired:a.passwordChangeRequired??!1}),n?(localStorage.setItem("user",JSON.stringify(a)),localStorage.setItem("token",o.accessToken)):(sessionStorage.setItem("user",JSON.stringify(a)),sessionStorage.setItem("token",o.accessToken)),!0}return!1}catch(i){return console.error("Login error:",i),!1}},logout:()=>{e({user:null,token:null,isAuthenticated:!1,passwordChangeRequired:!1}),localStorage.removeItem("user"),localStorage.removeItem("token"),sessionStorage.removeItem("user"),sessionStorage.removeItem("token"),window.location.href="/login"},checkAuth:()=>{let t=localStorage.getItem("user"),s=localStorage.getItem("token");if((!t||!s)&&(t=sessionStorage.getItem("user"),s=sessionStorage.getItem("token")),t&&s){const n=JSON.parse(t);e({user:n,token:s,isAuthenticated:!0,passwordChangeRequired:n.passwordChangeRequired})}},passwordChanged:()=>{e(t=>{const s=t.user?{...t.user,passwordChangeRequired:!1}:null;return s&&localStorage.setItem("user",JSON.stringify(s)),{user:s,passwordChangeRequired:!1}})}})),Ws=({nodeCount:e=35,particleCount:t=20,maxConnections:s=4,animationSpeed:n=.3,className:i=""})=>{const a=x.useRef(null),o=x.useRef([]),l=x.useRef([]),d=x.useRef(),c=x.useRef(0),u=x.useCallback(()=>{const m=a.current;if(!m)return;const p=m.getBoundingClientRect(),y=window.devicePixelRatio||1;m.width=p.width*y,m.height=p.height*y,m.style.width=p.width+"px",m.style.height=p.height+"px";const j=m.getContext("2d");j&&j.scale(y,y);const b=[];for(let w=0;w<e;w++)b.push({x:Math.random()*p.width,y:Math.random()*p.height,vx:(Math.random()-.5)*n,vy:(Math.random()-.5)*n,radius:Math.random()*5+3,opacity:Math.random()*.6+.2,hue:Math.random()*60+200,pulsePhase:Math.random()*Math.PI*2});o.current=b;const v=[];for(let w=0;w<t;w++){const C=Math.random()*200+100;v.push({x:Math.random()*p.width,y:Math.random()*p.height,vx:(Math.random()-.5)*n*.5,vy:(Math.random()-.5)*n*.5,radius:Math.random()*3+1,opacity:Math.random()*.4+.1,hue:Math.random()*40+180,life:C,maxLife:C})}l.current=v},[e,t,n]),h=x.useCallback(()=>{const m=a.current,p=m==null?void 0:m.getContext("2d");if(!m||!p)return;const y=m.getBoundingClientRect();c.current+=.016;const j=p.createRadialGradient(y.width/2,y.height/2,0,y.width/2,y.height/2,Math.max(y.width,y.height)/2);j.addColorStop(0,"#0f172a"),j.addColorStop(.5,"#1e293b"),j.addColorStop(1,"#0f172a"),p.fillStyle=j,p.fillRect(0,0,y.width,y.height);const b=p.createRadialGradient(y.width/2,y.height/2,0,y.width/2,y.height/2,Math.max(y.width,y.height)/1.5);b.addColorStop(0,"rgba(37, 99, 235, 0.03)"),b.addColorStop(.7,"rgba(8, 145, 178, 0.02)"),b.addColorStop(1,"transparent"),p.fillStyle=b,p.fillRect(0,0,y.width,y.height);const v=o.current,w=l.current;v.forEach(g=>{g.x+=g.vx,g.y+=g.vy,g.pulsePhase+=.02,(g.x<=0||g.x>=y.width)&&(g.vx*=-1,g.x=Math.max(0,Math.min(y.width,g.x))),(g.y<=0||g.y>=y.height)&&(g.vy*=-1,g.y=Math.max(0,Math.min(y.height,g.y))),g.opacity=.3+Math.sin(g.pulsePhase)*.3}),w.forEach(g=>{if(g.x+=g.vx,g.y+=g.vy,g.life--,g.life<=0){const S=Math.random()*200+100;g.life=S,g.maxLife=S,g.x=Math.random()*y.width,g.y=Math.random()*y.height,g.vx=(Math.random()-.5)*n*.5,g.vy=(Math.random()-.5)*n*.5,g.hue=Math.random()*40+180}(g.x<=0||g.x>=y.width)&&(g.vx*=-1),(g.y<=0||g.y>=y.height)&&(g.vy*=-1),g.x=Math.max(0,Math.min(y.width,g.x)),g.y=Math.max(0,Math.min(y.height,g.y)),g.opacity=g.life/g.maxLife*.4+.1}),p.lineCap="round";for(let g=0;g<v.length;g++){let S=0;for(let R=g+1;R<v.length&&S<s;R++){const D=v[g].x-v[R].x,O=v[g].y-v[R].y,Z=Math.sqrt(D*D+O*O),ge=150;if(Z<ge){const he=(1-Z/ge)*.2,_e=(v[g].hue+v[R].hue)/2;p.strokeStyle=`hsla(${_e}, 60%, 60%, ${he})`,p.lineWidth=Math.max(.5,2-Z/100),p.shadowColor=`hsla(${_e}, 60%, 60%, ${he*.3})`,p.shadowBlur=2,p.beginPath(),p.moveTo(v[g].x,v[g].y),p.lineTo(v[R].x,v[R].y),p.stroke(),p.shadowBlur=0,S++}}}for(let g=0;g<w.length;g++)for(let S=0;S<v.length;S++){const R=w[g].x-v[S].x,D=w[g].y-v[S].y,O=Math.sqrt(R*R+D*D),Z=80;if(O<Z){const ge=(1-O/Z)*.1,he=(w[g].hue+v[S].hue)/2;p.strokeStyle=`hsla(${he}, 50%, 50%, ${ge})`,p.lineWidth=.5,p.setLineDash([2,4]),p.beginPath(),p.moveTo(w[g].x,w[g].y),p.lineTo(v[S].x,v[S].y),p.stroke(),p.setLineDash([])}}v.forEach(g=>{const S=g.radius+Math.sin(g.pulsePhase)*.5;p.beginPath(),p.arc(g.x,g.y,S+3,0,Math.PI*2);const R=p.createRadialGradient(g.x,g.y,0,g.x,g.y,S+3);R.addColorStop(0,`hsla(${g.hue}, 70%, 60%, ${g.opacity*.3})`),R.addColorStop(1,"transparent"),p.fillStyle=R,p.fill(),p.beginPath(),p.arc(g.x,g.y,S,0,Math.PI*2);const D=p.createRadialGradient(g.x,g.y,0,g.x,g.y,S);D.addColorStop(0,`hsla(${g.hue}, 80%, 70%, ${g.opacity})`),D.addColorStop(1,`hsla(${g.hue}, 60%, 50%, ${g.opacity*.3})`),p.fillStyle=D,p.fill(),p.beginPath(),p.arc(g.x-S*.3,g.y-S*.3,S*.3,0,Math.PI*2),p.fillStyle=`hsla(${g.hue}, 90%, 80%, ${g.opacity*.6})`,p.fill()}),w.forEach(g=>{const S=Math.sin(c.current*2+g.x*.01)*.3+.7;p.beginPath(),p.arc(g.x,g.y,g.radius,0,Math.PI*2);const R=p.createRadialGradient(g.x,g.y,0,g.x,g.y,g.radius);R.addColorStop(0,`hsla(${g.hue}, 60%, 60%, ${g.opacity*S})`),R.addColorStop(1,"transparent"),p.fillStyle=R,p.fill()});const C=y.width/2+Math.sin(c.current*.5)*y.width*.3,T=y.height/2+Math.cos(c.current*.3)*y.height*.2,A=p.createRadialGradient(C,T,0,C,T,200);A.addColorStop(0,"rgba(37, 99, 235, 0.05)"),A.addColorStop(.5,"rgba(8, 145, 178, 0.03)"),A.addColorStop(1,"transparent"),p.fillStyle=A,p.fillRect(0,0,y.width,y.height),d.current=requestAnimationFrame(h)},[s,n]),f=x.useCallback(()=>{u()},[u]);return x.useEffect(()=>(u(),window.addEventListener("resize",f),h(),()=>{window.removeEventListener("resize",f),d.current&&cancelAnimationFrame(d.current)}),[u,h,f]),r.jsx("canvas",{ref:a,className:`fixed inset-0 pointer-events-none ${i}`,style:{width:"100vw",height:"100vh",zIndex:-1,position:"fixed",top:0,left:0}})},{Title:En,Text:Xe}=te,Aa=()=>{const[e,t]=x.useState(!1),[s]=U.useForm(),[n,i]=x.useState(new Date),[a,o]=x.useState(!1),l=me(c=>c.login);x.useEffect(()=>{const c=setInterval(()=>{i(new Date)},1e3);return()=>clearInterval(c)},[]);const d=async c=>{t(!0);try{await l(c.username,c.password,a)?(a?(localStorage.setItem("rememberMe","true"),localStorage.setItem("rememberedUsername",c.username)):(localStorage.removeItem("rememberMe"),localStorage.removeItem("rememberedUsername")),k.success("登录成功！欢迎进入IDEALAB")):k.error("用户名或密码错误，请重试")}catch{k.error("登录失败，请稍后重试")}finally{t(!1)}};return x.useEffect(()=>{const c=localStorage.getItem("rememberMe"),u=localStorage.getItem("rememberedUsername");c==="true"&&u&&(o(!0),s.setFieldsValue({username:u}))},[s]),r.jsxs("div",{style:{minHeight:"100vh",position:"relative",overflow:"hidden"},children:[r.jsx(Ws,{}),r.jsx("div",{style:{position:"relative",zIndex:20,padding:"16px 32px",background:"linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)",backdropFilter:"blur(20px)",borderBottom:"1px solid rgba(255, 255, 255, 0.1)"},children:r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",maxWidth:"1200px",margin:"0 auto"},children:[r.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[r.jsx(_o,{size:20,style:{color:"#3b82f6"}}),r.jsxs("div",{children:[r.jsx("div",{style:{color:"white",fontSize:"16px",fontWeight:600},children:n.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",second:"2-digit"})}),r.jsx("div",{style:{color:"rgba(255, 255, 255, 0.7)",fontSize:"12px"},children:n.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",weekday:"long"})})]})]}),r.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[r.jsx("div",{style:{width:"8px",height:"8px",borderRadius:"50%",background:"#10b981",animation:"pulse 2s ease-in-out infinite"}}),r.jsx(Go,{size:16,style:{color:"rgba(255, 255, 255, 0.7)"}}),r.jsx("span",{style:{color:"rgba(255, 255, 255, 0.7)",fontSize:"14px"},children:"系统在线"})]})]})}),r.jsx("div",{style:{position:"relative",zIndex:10,display:"flex",alignItems:"center",justifyContent:"center",minHeight:"calc(100vh - 80px)",padding:"40px 24px"},children:r.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"80px",maxWidth:"1200px",width:"100%",flexWrap:"wrap"},children:[r.jsxs("div",{style:{flex:"1 1 500px",textAlign:"center",minWidth:"400px"},children:[r.jsxs("div",{style:{position:"relative",display:"inline-block",marginBottom:"40px"},children:[r.jsx("div",{style:{width:"120px",height:"120px",background:"linear-gradient(135deg, #2563eb 0%, #0891b2 100%)",borderRadius:"28px",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto",boxShadow:"0 20px 60px rgba(37, 99, 235, 0.3)",animation:"logoFloat 6s ease-in-out infinite"},children:r.jsx(Ct,{size:48,style:{color:"white"}})}),r.jsx("div",{style:{position:"absolute",top:"-8px",right:"-8px",width:"40px",height:"40px",background:"linear-gradient(135deg, #0891b2 0%, #22d3ee 100%)",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",animation:"sparkle 3s ease-in-out infinite"},children:r.jsx(zs,{size:20,style:{color:"white"}})})]}),r.jsx(En,{level:1,style:{fontSize:"64px",fontWeight:800,background:"linear-gradient(135deg, #2563eb 0%, #0891b2 50%, #22d3ee 100%)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text",margin:"0 0 16px 0",lineHeight:1.1},children:"IDEALAB"}),r.jsx(Xe,{style:{fontSize:"22px",color:"rgba(255, 255, 255, 0.8)",display:"block",marginBottom:"48px",fontWeight:500},children:"业务创新实验平台"}),r.jsx(Xe,{style:{fontSize:"18px",color:"rgba(255, 255, 255, 0.6)",display:"block",marginBottom:"40px",maxWidth:"480px",margin:"0 auto 40px",lineHeight:1.6},children:"提供智能化分析工具与业务解决方案，助力数据驱动的科学决策"}),r.jsx("div",{style:{display:"flex",justifyContent:"center",gap:"12px",flexWrap:"wrap",marginBottom:"32px"},children:[{text:"企业级安全",icon:rt,color:"#3b82f6"},{text:"AI智能分析",icon:qo,color:"#0891b2"},{text:"数据驱动",icon:Oe,color:"#22d3ee"}].map((c,u)=>{const h=c.icon;return r.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",padding:"10px 14px",background:"linear-gradient(135deg, rgba(37, 99, 235, 0.15) 0%, rgba(8, 145, 178, 0.1) 100%)",border:"1px solid rgba(37, 99, 235, 0.2)",borderRadius:"20px",backdropFilter:"blur(10px)",color:"rgba(255, 255, 255, 0.8)",fontSize:"13px",fontWeight:500,transition:"all 0.3s ease",cursor:"pointer"},onMouseEnter:f=>{f.currentTarget.style.background="linear-gradient(135deg, rgba(37, 99, 235, 0.2) 0%, rgba(8, 145, 178, 0.15) 100%)",f.currentTarget.style.transform="translateY(-2px)",f.currentTarget.style.color="white"},onMouseLeave:f=>{f.currentTarget.style.background="linear-gradient(135deg, rgba(37, 99, 235, 0.15) 0%, rgba(8, 145, 178, 0.1) 100%)",f.currentTarget.style.transform="translateY(0)",f.currentTarget.style.color="rgba(255, 255, 255, 0.8)"},children:[r.jsx(h,{size:16,style:{color:c.color,flexShrink:0}}),r.jsx("span",{children:c.text})]},u)})})]}),r.jsx("div",{style:{flex:"1 1 400px",maxWidth:"450px",margin:"0 auto"},children:r.jsxs("div",{style:{background:"linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)",border:"1px solid rgba(255, 255, 255, 0.15)",borderRadius:"24px",backdropFilter:"blur(24px)",boxShadow:"0 20px 60px rgba(0, 0, 0, 0.3)",padding:"48px 40px",animation:"slideInRight 0.8s ease-out"},children:[r.jsxs("div",{style:{textAlign:"center",marginBottom:"40px"},children:[r.jsx(En,{level:2,style:{color:"white",fontSize:"32px",fontWeight:700,margin:"0 0 8px 0"},children:"系统登录"}),r.jsx(Xe,{style:{color:"rgba(255, 255, 255, 0.7)",fontSize:"16px"},children:"请输入您的账户信息以访问系统"})]}),r.jsxs(U,{form:s,name:"login",onFinish:d,layout:"vertical",requiredMark:!1,autoComplete:"off",style:{marginBottom:"32px"},children:[r.jsx(U.Item,{name:"username",rules:[{required:!0,message:"请输入用户名"},{min:3,message:"用户名至少3个字符"}],children:r.jsx(le,{prefix:r.jsx(nr,{size:20,style:{color:"#3b82f6"}}),placeholder:"请输入用户名",size:"large",autoComplete:"username",style:{height:"56px",borderRadius:"12px",border:"1px solid rgba(255, 255, 255, 0.15)",background:"linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%)",backdropFilter:"blur(10px)",fontSize:"16px",color:"white"}})}),r.jsx(U.Item,{name:"password",rules:[{required:!0,message:"请输入密码"},{min:6,message:"密码至少6个字符"}],children:r.jsx(le.Password,{prefix:r.jsx(ke,{size:20,style:{color:"#3b82f6"}}),placeholder:"请输入密码",size:"large",autoComplete:"current-password",style:{height:"56px",borderRadius:"12px",border:"1px solid rgba(255, 255, 255, 0.15)",background:"linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%)",backdropFilter:"blur(10px)",fontSize:"16px"}})}),r.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"24px"},children:r.jsx(oa,{checked:a,onChange:c=>o(c.target.checked),style:{color:"rgba(255, 255, 255, 0.8)"},children:"记住登录状态"})}),r.jsx(U.Item,{children:r.jsx(V,{type:"primary",htmlType:"submit",loading:e,size:"large",block:!0,icon:!e&&r.jsx(Xo,{size:20}),style:{height:"56px",fontSize:"16px",fontWeight:600,borderRadius:"12px",background:"linear-gradient(135deg, #2563eb 0%, #0891b2 100%)",border:"none",boxShadow:"0 8px 24px rgba(37, 99, 235, 0.4)",transition:"all 0.3s ease"},onMouseEnter:c=>{c.currentTarget.style.transform="translateY(-2px)",c.currentTarget.style.boxShadow="0 12px 32px rgba(37, 99, 235, 0.5)"},onMouseLeave:c=>{c.currentTarget.style.transform="translateY(0)",c.currentTarget.style.boxShadow="0 8px 24px rgba(37, 99, 235, 0.4)"},children:e?"登录中...":"立即登录"})})]}),r.jsxs("div",{style:{textAlign:"center",paddingTop:"24px",borderTop:"1px solid rgba(255, 255, 255, 0.1)"},children:[r.jsx(Xe,{style:{color:"rgba(255, 255, 255, 0.5)",fontSize:"12px"},children:"IDEALAB Professional v2.0"}),r.jsx("br",{}),r.jsx(Xe,{style:{color:"rgba(255, 255, 255, 0.4)",fontSize:"11px"},children:"© 2024 企业级数据分析平台 • 安全登录系统"})]})]})})]})})]})},Bn=[{id:"sentiment-analysis",name:"舆情报告生成",description:"分析网络舆情，生成专业舆情报告",icon:Je,category:"分析工具",path:"/tools/sentiment-analysis",featured:!0},{id:"equity-penetration",name:"股权穿透工具",description:"深度分析企业股权结构和穿透关系",icon:fs,category:"金融工具",path:"/tools/equity-penetration",featured:!0},{id:"equity-pledge",name:"股权质押分析",description:"分析股权质押情况和风险评估",icon:Oe,category:"金融工具",path:"/tools/equity-pledge"},{id:"bond-analysis",name:"债券市场分析",description:"债券市场数据分析和投资建议",icon:ir,category:"金融工具",path:"/tools/bond-analysis"},{id:"batch-ocr",name:"批量OCR",description:"批量识别图片和PDF中的文字内容",icon:Dt,category:"文档工具",path:"/tools/batch-ocr"},{id:"batch-watermark",name:"批量加水印",description:"为图片和文档批量添加水印",icon:ps,category:"文档工具",path:"/tools/batch-watermark"},{id:"excel-merger",name:"Excel暴力合并",description:"智能合并多个Excel文件和工作表",icon:ms,category:"文档工具",path:"/tools/excel-merger"}],{Text:Ma}=te,Ra=({tool:e,onClick:t})=>{const[s,n]=x.useState(!1),i=e.icon;return r.jsx(Vt,{title:e.description,children:r.jsxs("div",{onClick:t,onMouseEnter:()=>n(!0),onMouseLeave:()=>n(!1),style:{display:"flex",flexDirection:"column",alignItems:"center",gap:"12px",padding:"16px",borderRadius:"12px",cursor:"pointer",transition:"all 0.2s ease-in-out",background:s?"var(--bg-elevated)":"transparent",position:"relative"},children:[r.jsx("div",{style:{width:"64px",height:"64px",borderRadius:"16px",background:"linear-gradient(135deg, var(--bg-surface), var(--bg-elevated))",display:"flex",alignItems:"center",justifyContent:"center",transition:"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",transform:s?"scale(1.1) rotate(5deg)":"scale(1) rotate(0deg)",border:"1px solid var(--border-primary)",boxShadow:s?"0 4px 12px rgba(0,0,0,0.1)":"none",color:"var(--text-primary)"},children:r.jsx(i,{size:28})}),e.featured&&r.jsx("div",{style:{position:"absolute",top:"8px",right:"8px",background:"linear-gradient(135deg, var(--warning-500) 0%, var(--warning-400) 100%)",borderRadius:"50%",width:"20px",height:"20px",display:"flex",alignItems:"center",justifyContent:"center",color:"#fff"},children:r.jsx(zs,{size:12})}),r.jsx(Ma,{style:{color:"var(--text-primary)",fontWeight:"500",textAlign:"center",maxWidth:"100px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e.name})]})})},{Title:Kt,Text:Ee}=te,{Search:ka}=le,Ln=["在数据的海洋中探索无限可能，让洞察点亮前行的路","每一次分析都是一场发现之旅，愿智慧伴您同行","智能时代的探索者，让我们一起解锁数据的奥秘","在这里，复杂的数据变成简单的答案","用数据的语言诉说故事，让每个决策都闪闪发光","欢迎来到智能分析的世界，每一个工具都是您的得力助手","数据驱动未来，洞察创造价值，期待与您共同书写精彩","在算法与直觉的交响中，发现属于您的灵感","让数据成为您最好的朋友，在这里开启智慧之门"],Da=()=>{const{user:e}=me(),t=rr(),[s,n]=x.useState(""),[i,a]=x.useState("all"),o=x.useMemo(()=>Ln[Math.floor(Math.random()*Ln.length)],[]),l=u=>{t(u.path)},d=["all",...Array.from(new Set(Bn.map(u=>u.category)))],c=Bn.filter(u=>{const h=u.name.toLowerCase().includes(s.toLowerCase())||u.description.toLowerCase().includes(s.toLowerCase()),f=i==="all"||u.category===i;return h&&f});return r.jsxs("div",{style:{position:"relative",minHeight:"100%",height:"100%"},children:[r.jsx(Ws,{}),r.jsx("div",{style:{position:"relative",zIndex:1,padding:"32px",height:"100%"},children:r.jsxs(kn,{gutter:[40,32],style:{height:"100%",minHeight:"600px"},children:[r.jsx(Ht,{xs:24,lg:10,xl:8,children:r.jsx("div",{style:{display:"flex",flexDirection:"column",height:"100%"},children:r.jsxs(xs,{style:{background:"linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%)",backdropFilter:"blur(25px)",border:"1px solid rgba(255, 255, 255, 0.15)",borderRadius:"24px",position:"relative",overflow:"hidden",boxShadow:"0 20px 40px rgba(0, 0, 0, 0.15)",height:"100%"},bodyStyle:{padding:"32px",height:"100%",display:"flex",flexDirection:"column"},children:[r.jsx("div",{style:{position:"absolute",top:0,left:0,right:0,height:"4px",background:"linear-gradient(90deg, #2563eb 0%, #0891b2 50%, #22d3ee 100%)"}}),r.jsx("div",{style:{position:"absolute",top:"20px",right:"20px",width:"60px",height:"60px",borderRadius:"50%",background:"radial-gradient(circle, rgba(34, 211, 238, 0.1) 0%, transparent 70%)",pointerEvents:"none"}}),r.jsxs("div",{style:{marginBottom:"32px"},children:[r.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"12px",marginBottom:"16px"},children:[r.jsx(Yo,{size:24,style:{color:"#22d3ee"}}),r.jsx(Kt,{level:3,style:{color:"white",margin:0,fontSize:"24px",fontWeight:700,background:"linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent"},children:"欢迎回来"})]}),r.jsxs(Ee,{style:{fontSize:"18px",color:"white",display:"block",marginBottom:"12px",fontWeight:600},children:[e==null?void 0:e.username," 👋"]}),r.jsx(Ee,{style:{fontSize:"14px",color:"rgba(255, 255, 255, 0.7)",display:"block",lineHeight:1.6,fontStyle:"italic"},children:o})]}),r.jsx("div",{style:{flex:1,display:"flex",flexDirection:"column"},children:r.jsxs("div",{style:{background:"rgba(255, 255, 255, 0.08)",border:"1px solid rgba(255, 255, 255, 0.15)",borderRadius:"16px",padding:"24px",flex:1,display:"flex",flexDirection:"column"},children:[r.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"12px",marginBottom:"20px"},children:[r.jsx(Kt,{level:5,style:{color:"white",margin:0,fontSize:"16px",fontWeight:600},children:"系统公告"}),r.jsx(ys,{count:"3",size:"small",style:{marginLeft:"auto"}})]}),r.jsxs("div",{style:{flex:1,display:"flex",flexDirection:"column",gap:"14px"},children:[r.jsxs("div",{style:{background:"rgba(16, 185, 129, 0.1)",border:"1px solid rgba(16, 185, 129, 0.25)",borderRadius:"10px",padding:"14px",position:"relative"},children:[r.jsx("div",{style:{position:"absolute",top:"10px",right:"10px",width:"6px",height:"6px",borderRadius:"50%",background:"#10b981"}}),r.jsx(Ee,{style:{color:"#10b981",fontSize:"12px",fontWeight:600,margin:0,display:"block",marginBottom:"6px"},children:"[新功能] 股权穿透分析工具上线，点击查看"}),r.jsx(Ee,{style:{color:"rgba(255, 255, 255, 0.7)",fontSize:"11px",lineHeight:1.4,margin:0},children:"深度挖掘公司股权结构，一键生成关系网络图"})]}),r.jsxs("div",{style:{background:"rgba(59, 130, 246, 0.1)",border:"1px solid rgba(59, 130, 246, 0.25)",borderRadius:"10px",padding:"14px"},children:[r.jsx(Ee,{style:{color:"#3b82f6",fontSize:"12px",fontWeight:600,margin:0,display:"block",marginBottom:"6px"},children:"[系统维护] 平台将于今晚进行例行维护"}),r.jsx(Ee,{style:{color:"rgba(255, 255, 255, 0.7)",fontSize:"11px",lineHeight:1.4,margin:0},children:"维护时间：凌晨2点至4点，服务可能短暂中断"})]})]}),r.jsx(V,{type:"text",style:{color:"#22d3ee",fontSize:"12px",padding:"8px 0",height:"auto",marginTop:"16px",alignSelf:"flex-start"},children:"查看全部公告 →"})]})})]})})}),r.jsx(Ht,{xs:24,lg:14,xl:16,children:r.jsxs("div",{style:{height:"100%",display:"flex",flexDirection:"column"},children:[r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"24px",flexWrap:"wrap",gap:"16px"},children:[r.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[r.jsx(Kt,{level:3,style:{color:"white",margin:0,fontSize:"24px",fontWeight:700},children:"工具矩阵"}),r.jsx(ys,{count:c.length,style:{backgroundColor:"#2563eb"},showZero:!0})]}),r.jsx("div",{style:{display:"flex",gap:"12px",alignItems:"center"},children:r.jsx(ka,{placeholder:"搜索工具...",value:s,onChange:u=>n(u.target.value),style:{width:280},allowClear:!0,prefix:r.jsx(fs,{size:16})})})]}),r.jsx("div",{style:{marginBottom:"24px"},children:r.jsx("div",{style:{display:"flex",gap:"10px",flexWrap:"wrap"},children:d.map(u=>r.jsx(V,{size:"middle",type:i===u?"primary":"default",onClick:()=>a(u),style:{borderRadius:"20px",background:i===u?"linear-gradient(135deg, #2563eb 0%, #0891b2 100%)":"rgba(255, 255, 255, 0.06)",border:i===u?"none":"1px solid rgba(255, 255, 255, 0.12)",color:i===u?"white":"rgba(255, 255, 255, 0.8)",fontWeight:i===u?600:400,height:"36px",paddingLeft:"20px",paddingRight:"20px"},children:u==="all"?"全部":u},u))})}),r.jsxs(xs,{style:{background:"linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.02) 100%)",backdropFilter:"blur(25px) saturate(1.3)",border:"1px solid rgba(255, 255, 255, 0.12)",borderRadius:"24px",boxShadow:"0 20px 40px rgba(0, 0, 0, 0.15)",position:"relative",overflow:"hidden",flex:1},bodyStyle:{padding:"32px",height:"100%"},children:[r.jsx("div",{style:{position:"absolute",top:"30px",right:"30px",width:"80px",height:"80px",borderRadius:"50%",background:"radial-gradient(circle, rgba(37, 99, 235, 0.05) 0%, transparent 70%)",pointerEvents:"none"}}),c.length>0?r.jsx("div",{style:{height:"100%",overflow:"auto",paddingRight:"8px",minHeight:"400px"},children:r.jsx(kn,{gutter:[24,24],children:c.map(u=>r.jsx(Ht,{xs:12,sm:12,md:8,lg:6,xl:4,xxl:3,children:r.jsx(Ra,{tool:u,onClick:()=>l(u)})},u.id))})}):r.jsxs("div",{style:{height:"100%",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",color:"rgba(255, 255, 255, 0.6)"},children:[r.jsx(fs,{size:48,style:{marginBottom:"16px",opacity:.3}}),r.jsx("div",{style:{fontSize:"16px",marginBottom:"8px"},children:"未找到匹配的工具"}),r.jsx("div",{style:{fontSize:"14px"},children:"请尝试其他搜索关键词"})]})]})]})})]})})]})},G=({children:e,className:t,...s})=>r.jsx(xs,{...s,className:`glassmorphic-card ${t}`,style:{background:"rgba(255, 255, 255, 0.1)",backdropFilter:"blur(10px)",border:"1px solid rgba(255, 255, 255, 0.2)",borderRadius:"12px",height:"100%",...s.style},children:e}),{Sider:Va,Content:Ea}=K,{Title:_t,Text:In,Paragraph:Ba}=te,La=()=>{const[e,t]=x.useState(!1),s=()=>{t(!0),k.info("舆情分析功能正在开发中..."),setTimeout(()=>t(!1),2e3)};return r.jsx("div",{style:{width:"100%",height:"100%",minHeight:"600px",position:"relative"},children:r.jsxs(K,{style:{background:"transparent",padding:"16px",height:"100%",minHeight:"600px"},children:[r.jsx(Va,{width:280,style:{background:"transparent",marginRight:"16px"},children:r.jsxs(G,{title:r.jsx(_t,{level:5,className:"!text-white",children:"控制面板"}),children:[r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"16px"},children:[r.jsx(q,{title:r.jsx(In,{className:"!text-gray-300",children:"分析文档"}),value:0,prefix:r.jsx(Dt,{size:16}),valueStyle:{color:"#fff",fontSize:"18px"}}),r.jsx(q,{title:r.jsx(In,{className:"!text-gray-300",children:"情感分值"}),value:0,prefix:r.jsx(Oe,{size:16}),valueStyle:{color:"#fff",fontSize:"18px"}})]}),r.jsx(_t,{level:5,className:"!text-white",style:{marginTop:"24px"},children:"快速操作"}),r.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[r.jsx(V,{type:"primary",icon:r.jsx(Mn,{size:16}),onClick:s,loading:e,style:{width:"100%"},children:"开始分析"}),r.jsx(V,{icon:r.jsx(Je,{size:16}),style:{width:"100%"},disabled:!0,children:"生成报告"})]})]})}),r.jsx(Ea,{style:{position:"relative"},children:r.jsx(G,{style:{padding:0,height:"100%"},children:r.jsxs("div",{style:{padding:"48px",height:"100%",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",textAlign:"center"},children:[r.jsx(Mn,{size:64,style:{color:"#22d3ee",marginBottom:"24px"}}),r.jsx(_t,{level:3,className:"!text-white",style:{marginBottom:"16px"},children:"舆情分析工具"}),r.jsx(Ba,{style:{color:"rgba(255, 255, 255, 0.7)",fontSize:"16px",maxWidth:"400px",lineHeight:1.6},children:"智能分析网络舆情，生成专业舆情报告。 该功能正在开发中，敬请期待。"})]})})})]})})},Ia="modulepreload",Na=function(e){return"/"+e},Nn={},dr=function(t,s,n){let i=Promise.resolve();if(s&&s.length>0){document.getElementsByTagName("link");const o=document.querySelector("meta[property=csp-nonce]"),l=(o==null?void 0:o.nonce)||(o==null?void 0:o.getAttribute("nonce"));i=Promise.allSettled(s.map(d=>{if(d=Na(d),d in Nn)return;Nn[d]=!0;const c=d.endsWith(".css"),u=c?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${d}"]${u}`))return;const h=document.createElement("link");if(h.rel=c?"stylesheet":Ia,c||(h.as="script"),h.crossOrigin="",h.href=d,l&&h.setAttribute("nonce",l),document.head.appendChild(h),c)return new Promise((f,m)=>{h.addEventListener("load",f),h.addEventListener("error",()=>m(new Error(`Unable to preload CSS for ${d}`)))})}))}function a(o){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=o,window.dispatchEvent(l),!l.defaultPrevented)throw o}return i.then(o=>{for(const l of o||[])l.status==="rejected"&&a(l.reason);return t().catch(a)})},Us=x.createContext({});function Hs(e){const t=x.useRef(null);return t.current===null&&(t.current=e()),t.current}const Ks=typeof window<"u",hr=Ks?x.useLayoutEffect:x.useEffect,Ft=x.createContext(null);function _s(e,t){e.indexOf(t)===-1&&e.push(t)}function Gs(e,t){const s=e.indexOf(t);s>-1&&e.splice(s,1)}const fe=(e,t,s)=>s>t?t:s<e?e:s;let qs=()=>{};const pe={},fr=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e);function pr(e){return typeof e=="object"&&e!==null}const mr=e=>/^0[^.\s]+$/u.test(e);function Xs(e){let t;return()=>(t===void 0&&(t=e()),t)}const ee=e=>e,Fa=(e,t)=>s=>t(e(s)),mt=(...e)=>e.reduce(Fa),lt=(e,t,s)=>{const n=t-e;return n===0?1:(s-e)/n};class Ys{constructor(){this.subscriptions=[]}add(t){return _s(this.subscriptions,t),()=>Gs(this.subscriptions,t)}notify(t,s,n){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,s,n);else for(let a=0;a<i;a++){const o=this.subscriptions[a];o&&o(t,s,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const ce=e=>e*1e3,ue=e=>e/1e3;function gr(e,t){return t?e*(1e3/t):0}const xr=(e,t,s)=>(((1-3*s+3*t)*e+(3*s-6*t))*e+3*t)*e,za=1e-7,Oa=12;function $a(e,t,s,n,i){let a,o,l=0;do o=t+(s-t)/2,a=xr(o,n,i)-e,a>0?s=o:t=o;while(Math.abs(a)>za&&++l<Oa);return o}function gt(e,t,s,n){if(e===t&&s===n)return ee;const i=a=>$a(a,0,1,e,s);return a=>a===0||a===1?a:xr(i(a),t,n)}const yr=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,vr=e=>t=>1-e(1-t),br=gt(.33,1.53,.69,.99),Zs=vr(br),wr=yr(Zs),jr=e=>(e*=2)<1?.5*Zs(e):.5*(2-Math.pow(2,-10*(e-1))),Js=e=>1-Math.sin(Math.acos(e)),Sr=vr(Js),Tr=yr(Js),Wa=gt(.42,0,1,1),Ua=gt(0,0,.58,1),Cr=gt(.42,0,.58,1),Ha=e=>Array.isArray(e)&&typeof e[0]!="number",Pr=e=>Array.isArray(e)&&typeof e[0]=="number",Ka={linear:ee,easeIn:Wa,easeInOut:Cr,easeOut:Ua,circIn:Js,circInOut:Tr,circOut:Sr,backIn:Zs,backInOut:wr,backOut:br,anticipate:jr},_a=e=>typeof e=="string",Fn=e=>{if(Pr(e)){qs(e.length===4);const[t,s,n,i]=e;return gt(t,s,n,i)}else if(_a(e))return Ka[e];return e},vt=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];function Ga(e,t){let s=new Set,n=new Set,i=!1,a=!1;const o=new WeakSet;let l={delta:0,timestamp:0,isProcessing:!1};function d(u){o.has(u)&&(c.schedule(u),e()),u(l)}const c={schedule:(u,h=!1,f=!1)=>{const p=f&&i?s:n;return h&&o.add(u),p.has(u)||p.add(u),u},cancel:u=>{n.delete(u),o.delete(u)},process:u=>{if(l=u,i){a=!0;return}i=!0,[s,n]=[n,s],s.forEach(d),s.clear(),i=!1,a&&(a=!1,c.process(u))}};return c}const qa=40;function Ar(e,t){let s=!1,n=!0;const i={delta:0,timestamp:0,isProcessing:!1},a=()=>s=!0,o=vt.reduce((w,C)=>(w[C]=Ga(a),w),{}),{setup:l,read:d,resolveKeyframes:c,preUpdate:u,update:h,preRender:f,render:m,postRender:p}=o,y=()=>{const w=pe.useManualTiming?i.timestamp:performance.now();s=!1,pe.useManualTiming||(i.delta=n?1e3/60:Math.max(Math.min(w-i.timestamp,qa),1)),i.timestamp=w,i.isProcessing=!0,l.process(i),d.process(i),c.process(i),u.process(i),h.process(i),f.process(i),m.process(i),p.process(i),i.isProcessing=!1,s&&t&&(n=!1,e(y))},j=()=>{s=!0,n=!0,i.isProcessing||e(y)};return{schedule:vt.reduce((w,C)=>{const T=o[C];return w[C]=(A,g=!1,S=!1)=>(s||j(),T.schedule(A,g,S)),w},{}),cancel:w=>{for(let C=0;C<vt.length;C++)o[vt[C]].cancel(w)},state:i,steps:o}}const{schedule:E,cancel:we,state:W,steps:Gt}=Ar(typeof requestAnimationFrame<"u"?requestAnimationFrame:ee,!0);let At;function Xa(){At=void 0}const Y={now:()=>(At===void 0&&Y.set(W.isProcessing||pe.useManualTiming?W.timestamp:performance.now()),At),set:e=>{At=e,queueMicrotask(Xa)}},Mr=e=>t=>typeof t=="string"&&t.startsWith(e),Qs=Mr("--"),Ya=Mr("var(--"),en=e=>Ya(e)?Za.test(e.split("/*")[0].trim()):!1,Za=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Ue={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},ct={...Ue,transform:e=>fe(0,1,e)},bt={...Ue,default:1},Qe=e=>Math.round(e*1e5)/1e5,tn=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Ja(e){return e==null}const Qa=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,sn=(e,t)=>s=>!!(typeof s=="string"&&Qa.test(s)&&s.startsWith(e)||t&&!Ja(s)&&Object.prototype.hasOwnProperty.call(s,t)),Rr=(e,t,s)=>n=>{if(typeof n!="string")return n;const[i,a,o,l]=n.match(tn);return{[e]:parseFloat(i),[t]:parseFloat(a),[s]:parseFloat(o),alpha:l!==void 0?parseFloat(l):1}},el=e=>fe(0,255,e),qt={...Ue,transform:e=>Math.round(el(e))},Me={test:sn("rgb","red"),parse:Rr("red","green","blue"),transform:({red:e,green:t,blue:s,alpha:n=1})=>"rgba("+qt.transform(e)+", "+qt.transform(t)+", "+qt.transform(s)+", "+Qe(ct.transform(n))+")"};function tl(e){let t="",s="",n="",i="";return e.length>5?(t=e.substring(1,3),s=e.substring(3,5),n=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),s=e.substring(2,3),n=e.substring(3,4),i=e.substring(4,5),t+=t,s+=s,n+=n,i+=i),{red:parseInt(t,16),green:parseInt(s,16),blue:parseInt(n,16),alpha:i?parseInt(i,16)/255:1}}const vs={test:sn("#"),parse:tl,transform:Me.transform},xt=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),be=xt("deg"),de=xt("%"),P=xt("px"),sl=xt("vh"),nl=xt("vw"),zn={...de,parse:e=>de.parse(e)/100,transform:e=>de.transform(e*100)},Le={test:sn("hsl","hue"),parse:Rr("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:s,alpha:n=1})=>"hsla("+Math.round(e)+", "+de.transform(Qe(t))+", "+de.transform(Qe(s))+", "+Qe(ct.transform(n))+")"},z={test:e=>Me.test(e)||vs.test(e)||Le.test(e),parse:e=>Me.test(e)?Me.parse(e):Le.test(e)?Le.parse(e):vs.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?Me.transform(e):Le.transform(e),getAnimatableNone:e=>{const t=z.parse(e);return t.alpha=0,z.transform(t)}},il=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function rl(e){var t,s;return isNaN(e)&&typeof e=="string"&&(((t=e.match(tn))==null?void 0:t.length)||0)+(((s=e.match(il))==null?void 0:s.length)||0)>0}const kr="number",Dr="color",ol="var",al="var(",On="${}",ll=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ut(e){const t=e.toString(),s=[],n={color:[],number:[],var:[]},i=[];let a=0;const l=t.replace(ll,d=>(z.test(d)?(n.color.push(a),i.push(Dr),s.push(z.parse(d))):d.startsWith(al)?(n.var.push(a),i.push(ol),s.push(d)):(n.number.push(a),i.push(kr),s.push(parseFloat(d))),++a,On)).split(On);return{values:s,split:l,indexes:n,types:i}}function Vr(e){return ut(e).values}function Er(e){const{split:t,types:s}=ut(e),n=t.length;return i=>{let a="";for(let o=0;o<n;o++)if(a+=t[o],i[o]!==void 0){const l=s[o];l===kr?a+=Qe(i[o]):l===Dr?a+=z.transform(i[o]):a+=i[o]}return a}}const cl=e=>typeof e=="number"?0:z.test(e)?z.getAnimatableNone(e):e;function ul(e){const t=Vr(e);return Er(e)(t.map(cl))}const je={test:rl,parse:Vr,createTransformer:Er,getAnimatableNone:ul};function Xt(e,t,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?e+(t-e)*6*s:s<1/2?t:s<2/3?e+(t-e)*(2/3-s)*6:e}function dl({hue:e,saturation:t,lightness:s,alpha:n}){e/=360,t/=100,s/=100;let i=0,a=0,o=0;if(!t)i=a=o=s;else{const l=s<.5?s*(1+t):s+t-s*t,d=2*s-l;i=Xt(d,l,e+1/3),a=Xt(d,l,e),o=Xt(d,l,e-1/3)}return{red:Math.round(i*255),green:Math.round(a*255),blue:Math.round(o*255),alpha:n}}function Et(e,t){return s=>s>0?t:e}const B=(e,t,s)=>e+(t-e)*s,Yt=(e,t,s)=>{const n=e*e,i=s*(t*t-n)+n;return i<0?0:Math.sqrt(i)},hl=[vs,Me,Le],fl=e=>hl.find(t=>t.test(e));function $n(e){const t=fl(e);if(!t)return!1;let s=t.parse(e);return t===Le&&(s=dl(s)),s}const Wn=(e,t)=>{const s=$n(e),n=$n(t);if(!s||!n)return Et(e,t);const i={...s};return a=>(i.red=Yt(s.red,n.red,a),i.green=Yt(s.green,n.green,a),i.blue=Yt(s.blue,n.blue,a),i.alpha=B(s.alpha,n.alpha,a),Me.transform(i))},bs=new Set(["none","hidden"]);function pl(e,t){return bs.has(e)?s=>s<=0?e:t:s=>s>=1?t:e}function ml(e,t){return s=>B(e,t,s)}function nn(e){return typeof e=="number"?ml:typeof e=="string"?en(e)?Et:z.test(e)?Wn:yl:Array.isArray(e)?Br:typeof e=="object"?z.test(e)?Wn:gl:Et}function Br(e,t){const s=[...e],n=s.length,i=e.map((a,o)=>nn(a)(a,t[o]));return a=>{for(let o=0;o<n;o++)s[o]=i[o](a);return s}}function gl(e,t){const s={...e,...t},n={};for(const i in s)e[i]!==void 0&&t[i]!==void 0&&(n[i]=nn(e[i])(e[i],t[i]));return i=>{for(const a in n)s[a]=n[a](i);return s}}function xl(e,t){const s=[],n={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){const a=t.types[i],o=e.indexes[a][n[a]],l=e.values[o]??0;s[i]=l,n[a]++}return s}const yl=(e,t)=>{const s=je.createTransformer(t),n=ut(e),i=ut(t);return n.indexes.var.length===i.indexes.var.length&&n.indexes.color.length===i.indexes.color.length&&n.indexes.number.length>=i.indexes.number.length?bs.has(e)&&!i.values.length||bs.has(t)&&!n.values.length?pl(e,t):mt(Br(xl(n,i),i.values),s):Et(e,t)};function Lr(e,t,s){return typeof e=="number"&&typeof t=="number"&&typeof s=="number"?B(e,t,s):nn(e)(e,t)}const vl=e=>{const t=({timestamp:s})=>e(s);return{start:(s=!0)=>E.update(t,s),stop:()=>we(t),now:()=>W.isProcessing?W.timestamp:Y.now()}},Ir=(e,t,s=10)=>{let n="";const i=Math.max(Math.round(t/s),2);for(let a=0;a<i;a++)n+=Math.round(e(a/(i-1))*1e4)/1e4+", ";return`linear(${n.substring(0,n.length-2)})`},Bt=2e4;function rn(e){let t=0;const s=50;let n=e.next(t);for(;!n.done&&t<Bt;)t+=s,n=e.next(t);return t>=Bt?1/0:t}function bl(e,t=100,s){const n=s({...e,keyframes:[0,t]}),i=Math.min(rn(n),Bt);return{type:"keyframes",ease:a=>n.next(i*a).value/t,duration:ue(i)}}const wl=5;function Nr(e,t,s){const n=Math.max(t-wl,0);return gr(s-e(n),t-n)}const N={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Zt=.001;function jl({duration:e=N.duration,bounce:t=N.bounce,velocity:s=N.velocity,mass:n=N.mass}){let i,a,o=1-t;o=fe(N.minDamping,N.maxDamping,o),e=fe(N.minDuration,N.maxDuration,ue(e)),o<1?(i=c=>{const u=c*o,h=u*e,f=u-s,m=ws(c,o),p=Math.exp(-h);return Zt-f/m*p},a=c=>{const h=c*o*e,f=h*s+s,m=Math.pow(o,2)*Math.pow(c,2)*e,p=Math.exp(-h),y=ws(Math.pow(c,2),o);return(-i(c)+Zt>0?-1:1)*((f-m)*p)/y}):(i=c=>{const u=Math.exp(-c*e),h=(c-s)*e+1;return-Zt+u*h},a=c=>{const u=Math.exp(-c*e),h=(s-c)*(e*e);return u*h});const l=5/e,d=Tl(i,a,l);if(e=ce(e),isNaN(d))return{stiffness:N.stiffness,damping:N.damping,duration:e};{const c=Math.pow(d,2)*n;return{stiffness:c,damping:o*2*Math.sqrt(n*c),duration:e}}}const Sl=12;function Tl(e,t,s){let n=s;for(let i=1;i<Sl;i++)n=n-e(n)/t(n);return n}function ws(e,t){return e*Math.sqrt(1-t*t)}const Cl=["duration","bounce"],Pl=["stiffness","damping","mass"];function Un(e,t){return t.some(s=>e[s]!==void 0)}function Al(e){let t={velocity:N.velocity,stiffness:N.stiffness,damping:N.damping,mass:N.mass,isResolvedFromDuration:!1,...e};if(!Un(e,Pl)&&Un(e,Cl))if(e.visualDuration){const s=e.visualDuration,n=2*Math.PI/(s*1.2),i=n*n,a=2*fe(.05,1,1-(e.bounce||0))*Math.sqrt(i);t={...t,mass:N.mass,stiffness:i,damping:a}}else{const s=jl(e);t={...t,...s,mass:N.mass},t.isResolvedFromDuration=!0}return t}function Lt(e=N.visualDuration,t=N.bounce){const s=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:n,restDelta:i}=s;const a=s.keyframes[0],o=s.keyframes[s.keyframes.length-1],l={done:!1,value:a},{stiffness:d,damping:c,mass:u,duration:h,velocity:f,isResolvedFromDuration:m}=Al({...s,velocity:-ue(s.velocity||0)}),p=f||0,y=c/(2*Math.sqrt(d*u)),j=o-a,b=ue(Math.sqrt(d/u)),v=Math.abs(j)<5;n||(n=v?N.restSpeed.granular:N.restSpeed.default),i||(i=v?N.restDelta.granular:N.restDelta.default);let w;if(y<1){const T=ws(b,y);w=A=>{const g=Math.exp(-y*b*A);return o-g*((p+y*b*j)/T*Math.sin(T*A)+j*Math.cos(T*A))}}else if(y===1)w=T=>o-Math.exp(-b*T)*(j+(p+b*j)*T);else{const T=b*Math.sqrt(y*y-1);w=A=>{const g=Math.exp(-y*b*A),S=Math.min(T*A,300);return o-g*((p+y*b*j)*Math.sinh(S)+T*j*Math.cosh(S))/T}}const C={calculatedDuration:m&&h||null,next:T=>{const A=w(T);if(m)l.done=T>=h;else{let g=T===0?p:0;y<1&&(g=T===0?ce(p):Nr(w,T,A));const S=Math.abs(g)<=n,R=Math.abs(o-A)<=i;l.done=S&&R}return l.value=l.done?o:A,l},toString:()=>{const T=Math.min(rn(C),Bt),A=Ir(g=>C.next(T*g).value,T,30);return T+"ms "+A},toTransition:()=>{}};return C}Lt.applyToOptions=e=>{const t=bl(e,100,Lt);return e.ease=t.ease,e.duration=ce(t.duration),e.type="keyframes",e};function js({keyframes:e,velocity:t=0,power:s=.8,timeConstant:n=325,bounceDamping:i=10,bounceStiffness:a=500,modifyTarget:o,min:l,max:d,restDelta:c=.5,restSpeed:u}){const h=e[0],f={done:!1,value:h},m=S=>l!==void 0&&S<l||d!==void 0&&S>d,p=S=>l===void 0?d:d===void 0||Math.abs(l-S)<Math.abs(d-S)?l:d;let y=s*t;const j=h+y,b=o===void 0?j:o(j);b!==j&&(y=b-h);const v=S=>-y*Math.exp(-S/n),w=S=>b+v(S),C=S=>{const R=v(S),D=w(S);f.done=Math.abs(R)<=c,f.value=f.done?b:D};let T,A;const g=S=>{m(f.value)&&(T=S,A=Lt({keyframes:[f.value,p(f.value)],velocity:Nr(w,S,f.value),damping:i,stiffness:a,restDelta:c,restSpeed:u}))};return g(0),{calculatedDuration:null,next:S=>{let R=!1;return!A&&T===void 0&&(R=!0,C(S),g(S)),T!==void 0&&S>=T?A.next(S-T):(!R&&C(S),f)}}}function Ml(e,t,s){const n=[],i=s||pe.mix||Lr,a=e.length-1;for(let o=0;o<a;o++){let l=i(e[o],e[o+1]);if(t){const d=Array.isArray(t)?t[o]||ee:t;l=mt(d,l)}n.push(l)}return n}function Rl(e,t,{clamp:s=!0,ease:n,mixer:i}={}){const a=e.length;if(qs(a===t.length),a===1)return()=>t[0];if(a===2&&t[0]===t[1])return()=>t[1];const o=e[0]===e[1];e[0]>e[a-1]&&(e=[...e].reverse(),t=[...t].reverse());const l=Ml(t,n,i),d=l.length,c=u=>{if(o&&u<e[0])return t[0];let h=0;if(d>1)for(;h<e.length-2&&!(u<e[h+1]);h++);const f=lt(e[h],e[h+1],u);return l[h](f)};return s?u=>c(fe(e[0],e[a-1],u)):c}function kl(e,t){const s=e[e.length-1];for(let n=1;n<=t;n++){const i=lt(0,t,n);e.push(B(s,1,i))}}function Dl(e){const t=[0];return kl(t,e.length-1),t}function Vl(e,t){return e.map(s=>s*t)}function El(e,t){return e.map(()=>t||Cr).splice(0,e.length-1)}function et({duration:e=300,keyframes:t,times:s,ease:n="easeInOut"}){const i=Ha(n)?n.map(Fn):Fn(n),a={done:!1,value:t[0]},o=Vl(s&&s.length===t.length?s:Dl(t),e),l=Rl(o,t,{ease:Array.isArray(i)?i:El(t,i)});return{calculatedDuration:e,next:d=>(a.value=l(d),a.done=d>=e,a)}}const Bl=e=>e!==null;function on(e,{repeat:t,repeatType:s="loop"},n,i=1){const a=e.filter(Bl),l=i<0||t&&s!=="loop"&&t%2===1?0:a.length-1;return!l||n===void 0?a[l]:n}const Ll={decay:js,inertia:js,tween:et,keyframes:et,spring:Lt};function Fr(e){typeof e.type=="string"&&(e.type=Ll[e.type])}class an{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,s){return this.finished.then(t,s)}}const Il=e=>e/100;class ln extends an{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var n,i;const{motionValue:s}=this.options;s&&s.updatedAt!==Y.now()&&this.tick(Y.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(i=(n=this.options).onStop)==null||i.call(n))},this.options=t,this.initAnimation(),this.play(),t.autoplay===!1&&this.pause()}initAnimation(){const{options:t}=this;Fr(t);const{type:s=et,repeat:n=0,repeatDelay:i=0,repeatType:a,velocity:o=0}=t;let{keyframes:l}=t;const d=s||et;d!==et&&typeof l[0]!="number"&&(this.mixKeyframes=mt(Il,Lr(l[0],l[1])),l=[0,100]);const c=d({...t,keyframes:l});a==="mirror"&&(this.mirroredGenerator=d({...t,keyframes:[...l].reverse(),velocity:-o})),c.calculatedDuration===null&&(c.calculatedDuration=rn(c));const{calculatedDuration:u}=c;this.calculatedDuration=u,this.resolvedDuration=u+i,this.totalDuration=this.resolvedDuration*(n+1)-i,this.generator=c}updateTime(t){const s=Math.round(t-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=s}tick(t,s=!1){const{generator:n,totalDuration:i,mixKeyframes:a,mirroredGenerator:o,resolvedDuration:l,calculatedDuration:d}=this;if(this.startTime===null)return n.next(0);const{delay:c=0,keyframes:u,repeat:h,repeatType:f,repeatDelay:m,type:p,onUpdate:y,finalKeyframe:j}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),s?this.currentTime=t:this.updateTime(t);const b=this.currentTime-c*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?b<0:b>i;this.currentTime=Math.max(b,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=i);let w=this.currentTime,C=n;if(h){const S=Math.min(this.currentTime,i)/l;let R=Math.floor(S),D=S%1;!D&&S>=1&&(D=1),D===1&&R--,R=Math.min(R,h+1),!!(R%2)&&(f==="reverse"?(D=1-D,m&&(D-=m/l)):f==="mirror"&&(C=o)),w=fe(0,1,D)*l}const T=v?{done:!1,value:u[0]}:C.next(w);a&&(T.value=a(T.value));let{done:A}=T;!v&&d!==null&&(A=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const g=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&A);return g&&p!==js&&(T.value=on(u,this.options,j,this.speed)),y&&y(T.value),g&&this.finish(),T}then(t,s){return this.finished.then(t,s)}get duration(){return ue(this.calculatedDuration)}get time(){return ue(this.currentTime)}set time(t){var s;t=ce(t),this.currentTime=t,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),(s=this.driver)==null||s.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(Y.now());const s=this.playbackSpeed!==t;this.playbackSpeed=t,s&&(this.time=ue(this.currentTime))}play(){var i,a;if(this.isStopped)return;const{driver:t=vl,startTime:s}=this.options;this.driver||(this.driver=t(o=>this.tick(o))),(a=(i=this.options).onPlay)==null||a.call(i);const n=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=n):this.holdTime!==null?this.startTime=n-this.holdTime:this.startTime||(this.startTime=s??n),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(Y.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var t,s;this.notifyFinished(),this.teardown(),this.state="finished",(s=(t=this.options).onComplete)==null||s.call(t)}cancel(){var t,s;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(s=(t=this.options).onCancel)==null||s.call(t)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){var s;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(s=this.driver)==null||s.stop(),t.observe(this)}}function Nl(e){for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}const Re=e=>e*180/Math.PI,Ss=e=>{const t=Re(Math.atan2(e[1],e[0]));return Ts(t)},Fl={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:Ss,rotateZ:Ss,skewX:e=>Re(Math.atan(e[1])),skewY:e=>Re(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},Ts=e=>(e=e%360,e<0&&(e+=360),e),Hn=Ss,Kn=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),_n=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),zl={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Kn,scaleY:_n,scale:e=>(Kn(e)+_n(e))/2,rotateX:e=>Ts(Re(Math.atan2(e[6],e[5]))),rotateY:e=>Ts(Re(Math.atan2(-e[2],e[0]))),rotateZ:Hn,rotate:Hn,skewX:e=>Re(Math.atan(e[4])),skewY:e=>Re(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function Cs(e){return e.includes("scale")?1:0}function Ps(e,t){if(!e||e==="none")return Cs(t);const s=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let n,i;if(s)n=zl,i=s;else{const l=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=Fl,i=l}if(!i)return Cs(t);const a=n[t],o=i[1].split(",").map($l);return typeof a=="function"?a(o):o[a]}const Ol=(e,t)=>{const{transform:s="none"}=getComputedStyle(e);return Ps(s,t)};function $l(e){return parseFloat(e.trim())}const He=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Ke=new Set(He),Gn=e=>e===Ue||e===P,Wl=new Set(["x","y","z"]),Ul=He.filter(e=>!Wl.has(e));function Hl(e){const t=[];return Ul.forEach(s=>{const n=e.getValue(s);n!==void 0&&(t.push([s,n.get()]),n.set(s.startsWith("scale")?1:0))}),t}const De={width:({x:e},{paddingLeft:t="0",paddingRight:s="0"})=>e.max-e.min-parseFloat(t)-parseFloat(s),height:({y:e},{paddingTop:t="0",paddingBottom:s="0"})=>e.max-e.min-parseFloat(t)-parseFloat(s),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>Ps(t,"x"),y:(e,{transform:t})=>Ps(t,"y")};De.translateX=De.x;De.translateY=De.y;const Ve=new Set;let As=!1,Ms=!1,Rs=!1;function zr(){if(Ms){const e=Array.from(Ve).filter(n=>n.needsMeasurement),t=new Set(e.map(n=>n.element)),s=new Map;t.forEach(n=>{const i=Hl(n);i.length&&(s.set(n,i),n.render())}),e.forEach(n=>n.measureInitialState()),t.forEach(n=>{n.render();const i=s.get(n);i&&i.forEach(([a,o])=>{var l;(l=n.getValue(a))==null||l.set(o)})}),e.forEach(n=>n.measureEndState()),e.forEach(n=>{n.suspendedScrollY!==void 0&&window.scrollTo(0,n.suspendedScrollY)})}Ms=!1,As=!1,Ve.forEach(e=>e.complete(Rs)),Ve.clear()}function Or(){Ve.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Ms=!0)})}function Kl(){Rs=!0,Or(),zr(),Rs=!1}class cn{constructor(t,s,n,i,a,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=s,this.name=n,this.motionValue=i,this.element=a,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(Ve.add(this),As||(As=!0,E.read(Or),E.resolveKeyframes(zr))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:s,element:n,motionValue:i}=this;if(t[0]===null){const a=i==null?void 0:i.get(),o=t[t.length-1];if(a!==void 0)t[0]=a;else if(n&&s){const l=n.readValue(s,o);l!=null&&(t[0]=l)}t[0]===void 0&&(t[0]=o),i&&a===void 0&&i.set(t[0])}Nl(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),Ve.delete(this)}cancel(){this.state==="scheduled"&&(Ve.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const _l=e=>e.startsWith("--");function Gl(e,t,s){_l(t)?e.style.setProperty(t,s):e.style[t]=s}const ql=Xs(()=>window.ScrollTimeline!==void 0),Xl={};function Yl(e,t){const s=Xs(e);return()=>Xl[t]??s()}const $r=Yl(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),Ze=([e,t,s,n])=>`cubic-bezier(${e}, ${t}, ${s}, ${n})`,qn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ze([0,.65,.55,1]),circOut:Ze([.55,0,1,.45]),backIn:Ze([.31,.01,.66,-.59]),backOut:Ze([.33,1.53,.69,.99])};function Wr(e,t){if(e)return typeof e=="function"?$r()?Ir(e,t):"ease-out":Pr(e)?Ze(e):Array.isArray(e)?e.map(s=>Wr(s,t)||qn.easeOut):qn[e]}function Zl(e,t,s,{delay:n=0,duration:i=300,repeat:a=0,repeatType:o="loop",ease:l="easeOut",times:d}={},c=void 0){const u={[t]:s};d&&(u.offset=d);const h=Wr(l,i);Array.isArray(h)&&(u.easing=h);const f={delay:n,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:a+1,direction:o==="reverse"?"alternate":"normal"};return c&&(f.pseudoElement=c),e.animate(u,f)}function Ur(e){return typeof e=="function"&&"applyToOptions"in e}function Jl({type:e,...t}){return Ur(e)&&$r()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}class Ql extends an{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:s,name:n,keyframes:i,pseudoElement:a,allowFlatten:o=!1,finalKeyframe:l,onComplete:d}=t;this.isPseudoElement=!!a,this.allowFlatten=o,this.options=t,qs(typeof t.type!="string");const c=Jl(t);this.animation=Zl(s,n,i,c,a),c.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!a){const u=on(i,this.options,l,this.speed);this.updateMotionValue?this.updateMotionValue(u):Gl(s,n,u),this.animation.cancel()}d==null||d(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var t,s;(s=(t=this.animation).finish)==null||s.call(t)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;t==="idle"||t==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var t,s;this.isPseudoElement||(s=(t=this.animation).commitStyles)==null||s.call(t)}get duration(){var s,n;const t=((n=(s=this.animation.effect)==null?void 0:s.getComputedTiming)==null?void 0:n.call(s).duration)||0;return ue(Number(t))}get time(){return ue(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=ce(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:s}){var n;return this.allowFlatten&&((n=this.animation.effect)==null||n.updateTiming({easing:"linear"})),this.animation.onfinish=null,t&&ql()?(this.animation.timeline=t,ee):s(this)}}const Hr={anticipate:jr,backInOut:wr,circInOut:Tr};function ec(e){return e in Hr}function tc(e){typeof e.ease=="string"&&ec(e.ease)&&(e.ease=Hr[e.ease])}const Xn=10;class sc extends Ql{constructor(t){tc(t),Fr(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:s,onUpdate:n,onComplete:i,element:a,...o}=this.options;if(!s)return;if(t!==void 0){s.set(t);return}const l=new ln({...o,autoplay:!1}),d=ce(this.finishedTime??this.time);s.setWithVelocity(l.sample(d-Xn).value,l.sample(d).value,Xn),l.stop()}}const Yn=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(je.test(e)||e==="0")&&!e.startsWith("url("));function nc(e){const t=e[0];if(e.length===1)return!0;for(let s=0;s<e.length;s++)if(e[s]!==t)return!0}function ic(e,t,s,n){const i=e[0];if(i===null)return!1;if(t==="display"||t==="visibility")return!0;const a=e[e.length-1],o=Yn(i,t),l=Yn(a,t);return!o||!l?!1:nc(e)||(s==="spring"||Ur(s))&&n}const rc=new Set(["opacity","clipPath","filter","transform"]),oc=Xs(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function ac(e){var u;const{motionValue:t,name:s,repeatDelay:n,repeatType:i,damping:a,type:o}=e;if(!(((u=t==null?void 0:t.owner)==null?void 0:u.current)instanceof HTMLElement))return!1;const{onUpdate:d,transformTemplate:c}=t.owner.getProps();return oc()&&s&&rc.has(s)&&(s!=="transform"||!c)&&!d&&!n&&i!=="mirror"&&a!==0&&o!=="inertia"}const lc=40;class cc extends an{constructor({autoplay:t=!0,delay:s=0,type:n="keyframes",repeat:i=0,repeatDelay:a=0,repeatType:o="loop",keyframes:l,name:d,motionValue:c,element:u,...h}){var p;super(),this.stop=()=>{var y,j;this._animation&&(this._animation.stop(),(y=this.stopTimeline)==null||y.call(this)),(j=this.keyframeResolver)==null||j.cancel()},this.createdAt=Y.now();const f={autoplay:t,delay:s,type:n,repeat:i,repeatDelay:a,repeatType:o,name:d,motionValue:c,element:u,...h},m=(u==null?void 0:u.KeyframeResolver)||cn;this.keyframeResolver=new m(l,(y,j,b)=>this.onKeyframesResolved(y,j,f,!b),d,c,u),(p=this.keyframeResolver)==null||p.scheduleResolve()}onKeyframesResolved(t,s,n,i){this.keyframeResolver=void 0;const{name:a,type:o,velocity:l,delay:d,isHandoff:c,onUpdate:u}=n;this.resolvedAt=Y.now(),ic(t,a,o,l)||((pe.instantAnimations||!d)&&(u==null||u(on(t,n,s))),t[0]=t[t.length-1],n.duration=0,n.repeat=0);const f={startTime:i?this.resolvedAt?this.resolvedAt-this.createdAt>lc?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:s,...n,keyframes:t},m=!c&&ac(f)?new sc({...f,element:f.motionValue.owner.current}):new ln(f);m.finished.then(()=>this.notifyFinished()).catch(ee),this.pendingTimeline&&(this.stopTimeline=m.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=m}get finished(){return this._animation?this.animation.finished:this._finished}then(t,s){return this.finished.finally(t).then(()=>{})}get animation(){var t;return this._animation||((t=this.keyframeResolver)==null||t.resume(),Kl()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var t;this._animation&&this.animation.cancel(),(t=this.keyframeResolver)==null||t.cancel()}}const uc=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function dc(e){const t=uc.exec(e);if(!t)return[,];const[,s,n,i]=t;return[`--${s??n}`,i]}function Kr(e,t,s=1){const[n,i]=dc(e);if(!n)return;const a=window.getComputedStyle(t).getPropertyValue(n);if(a){const o=a.trim();return fr(o)?parseFloat(o):o}return en(i)?Kr(i,t,s+1):i}function un(e,t){return(e==null?void 0:e[t])??(e==null?void 0:e.default)??e}const _r=new Set(["width","height","top","left","right","bottom",...He]),hc={test:e=>e==="auto",parse:e=>e},Gr=e=>t=>t.test(e),qr=[Ue,P,de,be,nl,sl,hc],Zn=e=>qr.find(Gr(e));function fc(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||mr(e):!0}const pc=new Set(["brightness","contrast","saturate","opacity"]);function mc(e){const[t,s]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[n]=s.match(tn)||[];if(!n)return e;const i=s.replace(n,"");let a=pc.has(t)?1:0;return n!==s&&(a*=100),t+"("+a+i+")"}const gc=/\b([a-z-]*)\(.*?\)/gu,ks={...je,getAnimatableNone:e=>{const t=e.match(gc);return t?t.map(mc).join(" "):e}},Jn={...Ue,transform:Math.round},xc={rotate:be,rotateX:be,rotateY:be,rotateZ:be,scale:bt,scaleX:bt,scaleY:bt,scaleZ:bt,skew:be,skewX:be,skewY:be,distance:P,translateX:P,translateY:P,translateZ:P,x:P,y:P,z:P,perspective:P,transformPerspective:P,opacity:ct,originX:zn,originY:zn,originZ:P},dn={borderWidth:P,borderTopWidth:P,borderRightWidth:P,borderBottomWidth:P,borderLeftWidth:P,borderRadius:P,radius:P,borderTopLeftRadius:P,borderTopRightRadius:P,borderBottomRightRadius:P,borderBottomLeftRadius:P,width:P,maxWidth:P,height:P,maxHeight:P,top:P,right:P,bottom:P,left:P,padding:P,paddingTop:P,paddingRight:P,paddingBottom:P,paddingLeft:P,margin:P,marginTop:P,marginRight:P,marginBottom:P,marginLeft:P,backgroundPositionX:P,backgroundPositionY:P,...xc,zIndex:Jn,fillOpacity:ct,strokeOpacity:ct,numOctaves:Jn},yc={...dn,color:z,backgroundColor:z,outlineColor:z,fill:z,stroke:z,borderColor:z,borderTopColor:z,borderRightColor:z,borderBottomColor:z,borderLeftColor:z,filter:ks,WebkitFilter:ks},Xr=e=>yc[e];function Yr(e,t){let s=Xr(e);return s!==ks&&(s=je),s.getAnimatableNone?s.getAnimatableNone(t):void 0}const vc=new Set(["auto","none","0"]);function bc(e,t,s){let n=0,i;for(;n<e.length&&!i;){const a=e[n];typeof a=="string"&&!vc.has(a)&&ut(a).values.length&&(i=e[n]),n++}if(i&&s)for(const a of t)e[a]=Yr(s,i)}class wc extends cn{constructor(t,s,n,i,a){super(t,s,n,i,a,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:s,name:n}=this;if(!s||!s.current)return;super.readKeyframes();for(let d=0;d<t.length;d++){let c=t[d];if(typeof c=="string"&&(c=c.trim(),en(c))){const u=Kr(c,s.current);u!==void 0&&(t[d]=u),d===t.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!_r.has(n)||t.length!==2)return;const[i,a]=t,o=Zn(i),l=Zn(a);if(o!==l)if(Gn(o)&&Gn(l))for(let d=0;d<t.length;d++){const c=t[d];typeof c=="string"&&(t[d]=parseFloat(c))}else De[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:s}=this,n=[];for(let i=0;i<t.length;i++)(t[i]===null||fc(t[i]))&&n.push(i);n.length&&bc(t,n,s)}measureInitialState(){const{element:t,unresolvedKeyframes:s,name:n}=this;if(!t||!t.current)return;n==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=De[n](t.measureViewportBox(),window.getComputedStyle(t.current)),s[0]=this.measuredOrigin;const i=s[s.length-1];i!==void 0&&t.getValue(n,i).jump(i,!1)}measureEndState(){var l;const{element:t,name:s,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const i=t.getValue(s);i&&i.jump(this.measuredOrigin,!1);const a=n.length-1,o=n[a];n[a]=De[s](t.measureViewportBox(),window.getComputedStyle(t.current)),o!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=o),(l=this.removedTransforms)!=null&&l.length&&this.removedTransforms.forEach(([d,c])=>{t.getValue(d).set(c)}),this.resolveNoneKeyframes()}}function jc(e,t,s){if(e instanceof EventTarget)return[e];if(typeof e=="string"){let n=document;const i=(s==null?void 0:s[e])??n.querySelectorAll(e);return i?Array.from(i):[]}return Array.from(e)}const Zr=(e,t)=>t&&typeof e=="number"?t.transform(e):e;function Jr(e){return pr(e)&&"offsetHeight"in e}const Qn=30,Sc=e=>!isNaN(parseFloat(e));class Tc{constructor(t,s={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(n,i=!0)=>{var o,l;const a=Y.now();if(this.updatedAt!==a&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(n),this.current!==this.prev&&((o=this.events.change)==null||o.notify(this.current),this.dependents))for(const d of this.dependents)d.dirty();i&&((l=this.events.renderRequest)==null||l.notify(this.current))},this.hasAnimated=!1,this.setCurrent(t),this.owner=s.owner}setCurrent(t){this.current=t,this.updatedAt=Y.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=Sc(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,s){this.events[t]||(this.events[t]=new Ys);const n=this.events[t].add(s);return t==="change"?()=>{n(),E.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,s){this.passiveEffect=t,this.stopPassiveEffect=s}set(t,s=!0){!s||!this.passiveEffect?this.updateAndNotify(t,s):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,s,n){this.set(s),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,s=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,s&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var t;(t=this.events.change)==null||t.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=Y.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Qn)return 0;const s=Math.min(this.updatedAt-this.prevUpdatedAt,Qn);return gr(parseFloat(this.current)-parseFloat(this.prevFrameValue),s)}start(t){return this.stop(),new Promise(s=>{this.hasAnimated=!0,this.animation=t(s),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var t,s;(t=this.dependents)==null||t.clear(),(s=this.events.destroy)==null||s.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function $e(e,t){return new Tc(e,t)}const{schedule:hn}=Ar(queueMicrotask,!1),ie={x:!1,y:!1};function Qr(){return ie.x||ie.y}function Cc(e){return e==="x"||e==="y"?ie[e]?null:(ie[e]=!0,()=>{ie[e]=!1}):ie.x||ie.y?null:(ie.x=ie.y=!0,()=>{ie.x=ie.y=!1})}function eo(e,t){const s=jc(e),n=new AbortController,i={passive:!0,...t,signal:n.signal};return[s,i,()=>n.abort()]}function ei(e){return!(e.pointerType==="touch"||Qr())}function Pc(e,t,s={}){const[n,i,a]=eo(e,s),o=l=>{if(!ei(l))return;const{target:d}=l,c=t(d,l);if(typeof c!="function"||!d)return;const u=h=>{ei(h)&&(c(h),d.removeEventListener("pointerleave",u))};d.addEventListener("pointerleave",u,i)};return n.forEach(l=>{l.addEventListener("pointerenter",o,i)}),a}const to=(e,t)=>t?e===t?!0:to(e,t.parentElement):!1,fn=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,Ac=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function Mc(e){return Ac.has(e.tagName)||e.tabIndex!==-1}const Mt=new WeakSet;function ti(e){return t=>{t.key==="Enter"&&e(t)}}function Jt(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const Rc=(e,t)=>{const s=e.currentTarget;if(!s)return;const n=ti(()=>{if(Mt.has(s))return;Jt(s,"down");const i=ti(()=>{Jt(s,"up")}),a=()=>Jt(s,"cancel");s.addEventListener("keyup",i,t),s.addEventListener("blur",a,t)});s.addEventListener("keydown",n,t),s.addEventListener("blur",()=>s.removeEventListener("keydown",n),t)};function si(e){return fn(e)&&!Qr()}function kc(e,t,s={}){const[n,i,a]=eo(e,s),o=l=>{const d=l.currentTarget;if(!si(l))return;Mt.add(d);const c=t(d,l),u=(m,p)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",f),Mt.has(d)&&Mt.delete(d),si(m)&&typeof c=="function"&&c(m,{success:p})},h=m=>{u(m,d===window||d===document||s.useGlobalTarget||to(d,m.target))},f=m=>{u(m,!1)};window.addEventListener("pointerup",h,i),window.addEventListener("pointercancel",f,i)};return n.forEach(l=>{(s.useGlobalTarget?window:l).addEventListener("pointerdown",o,i),Jr(l)&&(l.addEventListener("focus",c=>Rc(c,i)),!Mc(l)&&!l.hasAttribute("tabindex")&&(l.tabIndex=0))}),a}function so(e){return pr(e)&&"ownerSVGElement"in e}function Dc(e){return so(e)&&e.tagName==="svg"}const H=e=>!!(e&&e.getVelocity),Vc=[...qr,z,je],Ec=e=>Vc.find(Gr(e)),pn=x.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class Bc extends x.Component{getSnapshotBeforeUpdate(t){const s=this.props.childRef.current;if(s&&t.isPresent&&!this.props.isPresent){const n=s.offsetParent,i=Jr(n)&&n.offsetWidth||0,a=this.props.sizeRef.current;a.height=s.offsetHeight||0,a.width=s.offsetWidth||0,a.top=s.offsetTop,a.left=s.offsetLeft,a.right=i-a.width-a.left}return null}componentDidUpdate(){}render(){return this.props.children}}function Lc({children:e,isPresent:t,anchorX:s,root:n}){const i=x.useId(),a=x.useRef(null),o=x.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=x.useContext(pn);return x.useInsertionEffect(()=>{const{width:d,height:c,top:u,left:h,right:f}=o.current;if(t||!a.current||!d||!c)return;const m=s==="left"?`left: ${h}`:`right: ${f}`;a.current.dataset.motionPopId=i;const p=document.createElement("style");l&&(p.nonce=l);const y=n??document.head;return y.appendChild(p),p.sheet&&p.sheet.insertRule(`
          [data-motion-pop-id="${i}"] {
            position: absolute !important;
            width: ${d}px !important;
            height: ${c}px !important;
            ${m}px !important;
            top: ${u}px !important;
          }
        `),()=>{y.removeChild(p),y.contains(p)&&y.removeChild(p)}},[t]),r.jsx(Bc,{isPresent:t,childRef:a,sizeRef:o,children:x.cloneElement(e,{ref:a})})}const Ic=({children:e,initial:t,isPresent:s,onExitComplete:n,custom:i,presenceAffectsLayout:a,mode:o,anchorX:l,root:d})=>{const c=Hs(Nc),u=x.useId();let h=!0,f=x.useMemo(()=>(h=!1,{id:u,initial:t,isPresent:s,custom:i,onExitComplete:m=>{c.set(m,!0);for(const p of c.values())if(!p)return;n&&n()},register:m=>(c.set(m,!1),()=>c.delete(m))}),[s,c,n]);return a&&h&&(f={...f}),x.useMemo(()=>{c.forEach((m,p)=>c.set(p,!1))},[s]),x.useEffect(()=>{!s&&!c.size&&n&&n()},[s]),o==="popLayout"&&(e=r.jsx(Lc,{isPresent:s,anchorX:l,root:d,children:e})),r.jsx(Ft.Provider,{value:f,children:e})};function Nc(){return new Map}function no(e=!0){const t=x.useContext(Ft);if(t===null)return[!0,null];const{isPresent:s,onExitComplete:n,register:i}=t,a=x.useId();x.useEffect(()=>{if(e)return i(a)},[e]);const o=x.useCallback(()=>e&&n&&n(a),[a,n,e]);return!s&&n?[!1,o]:[!0]}const wt=e=>e.key||"";function ni(e){const t=[];return x.Children.forEach(e,s=>{x.isValidElement(s)&&t.push(s)}),t}const Fc=({children:e,custom:t,initial:s=!0,onExitComplete:n,presenceAffectsLayout:i=!0,mode:a="sync",propagate:o=!1,anchorX:l="left",root:d})=>{const[c,u]=no(o),h=x.useMemo(()=>ni(e),[e]),f=o&&!c?[]:h.map(wt),m=x.useRef(!0),p=x.useRef(h),y=Hs(()=>new Map),[j,b]=x.useState(h),[v,w]=x.useState(h);hr(()=>{m.current=!1,p.current=h;for(let A=0;A<v.length;A++){const g=wt(v[A]);f.includes(g)?y.delete(g):y.get(g)!==!0&&y.set(g,!1)}},[v,f.length,f.join("-")]);const C=[];if(h!==j){let A=[...h];for(let g=0;g<v.length;g++){const S=v[g],R=wt(S);f.includes(R)||(A.splice(g,0,S),C.push(S))}return a==="wait"&&C.length&&(A=C),w(ni(A)),b(h),null}const{forceRender:T}=x.useContext(Us);return r.jsx(r.Fragment,{children:v.map(A=>{const g=wt(A),S=o&&!c?!1:h===v||f.includes(g),R=()=>{if(y.has(g))y.set(g,!0);else return;let D=!0;y.forEach(O=>{O||(D=!1)}),D&&(T==null||T(),w(p.current),o&&(u==null||u()),n&&n())};return r.jsx(Ic,{isPresent:S,initial:!m.current||s?void 0:!1,custom:t,presenceAffectsLayout:i,mode:a,root:d,onExitComplete:S?void 0:R,anchorX:l,children:A},g)})})},io=x.createContext({strict:!1}),ii={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},We={};for(const e in ii)We[e]={isEnabled:t=>ii[e].some(s=>!!t[s])};function zc(e){for(const t in e)We[t]={...We[t],...e[t]}}const Oc=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function It(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Oc.has(e)}let ro=e=>!It(e);function $c(e){typeof e=="function"&&(ro=t=>t.startsWith("on")?!It(t):e(t))}try{$c(require("@emotion/is-prop-valid").default)}catch{}function Wc(e,t,s){const n={};for(const i in e)i==="values"&&typeof e.values=="object"||(ro(i)||s===!0&&It(i)||!t&&!It(i)||e.draggable&&i.startsWith("onDrag"))&&(n[i]=e[i]);return n}const zt=x.createContext({});function Ot(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}function dt(e){return typeof e=="string"||Array.isArray(e)}const mn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],gn=["initial",...mn];function $t(e){return Ot(e.animate)||gn.some(t=>dt(e[t]))}function oo(e){return!!($t(e)||e.variants)}function Uc(e,t){if($t(e)){const{initial:s,animate:n}=e;return{initial:s===!1||dt(s)?s:void 0,animate:dt(n)?n:void 0}}return e.inherit!==!1?t:{}}function Hc(e){const{initial:t,animate:s}=Uc(e,x.useContext(zt));return x.useMemo(()=>({initial:t,animate:s}),[ri(t),ri(s)])}function ri(e){return Array.isArray(e)?e.join(" "):e}const ht={};function Kc(e){for(const t in e)ht[t]=e[t],Qs(t)&&(ht[t].isCSSVariable=!0)}function ao(e,{layout:t,layoutId:s}){return Ke.has(e)||e.startsWith("origin")||(t||s!==void 0)&&(!!ht[e]||e==="opacity")}const _c={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Gc=He.length;function qc(e,t,s){let n="",i=!0;for(let a=0;a<Gc;a++){const o=He[a],l=e[o];if(l===void 0)continue;let d=!0;if(typeof l=="number"?d=l===(o.startsWith("scale")?1:0):d=parseFloat(l)===0,!d||s){const c=Zr(l,dn[o]);if(!d){i=!1;const u=_c[o]||o;n+=`${u}(${c}) `}s&&(t[o]=c)}}return n=n.trim(),s?n=s(t,i?"":n):i&&(n="none"),n}function xn(e,t,s){const{style:n,vars:i,transformOrigin:a}=e;let o=!1,l=!1;for(const d in t){const c=t[d];if(Ke.has(d)){o=!0;continue}else if(Qs(d)){i[d]=c;continue}else{const u=Zr(c,dn[d]);d.startsWith("origin")?(l=!0,a[d]=u):n[d]=u}}if(t.transform||(o||s?n.transform=qc(t,e.transform,s):n.transform&&(n.transform="none")),l){const{originX:d="50%",originY:c="50%",originZ:u=0}=a;n.transformOrigin=`${d} ${c} ${u}`}}const yn=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function lo(e,t,s){for(const n in t)!H(t[n])&&!ao(n,s)&&(e[n]=t[n])}function Xc({transformTemplate:e},t){return x.useMemo(()=>{const s=yn();return xn(s,t,e),Object.assign({},s.vars,s.style)},[t])}function Yc(e,t){const s=e.style||{},n={};return lo(n,s,e),Object.assign(n,Xc(e,t)),n}function Zc(e,t){const s={},n=Yc(e,t);return e.drag&&e.dragListener!==!1&&(s.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(s.tabIndex=0),s.style=n,s}const Jc={offset:"stroke-dashoffset",array:"stroke-dasharray"},Qc={offset:"strokeDashoffset",array:"strokeDasharray"};function eu(e,t,s=1,n=0,i=!0){e.pathLength=1;const a=i?Jc:Qc;e[a.offset]=P.transform(-n);const o=P.transform(t),l=P.transform(s);e[a.array]=`${o} ${l}`}function co(e,{attrX:t,attrY:s,attrScale:n,pathLength:i,pathSpacing:a=1,pathOffset:o=0,...l},d,c,u){if(xn(e,l,c),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:h,style:f}=e;h.transform&&(f.transform=h.transform,delete h.transform),(f.transform||h.transformOrigin)&&(f.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),f.transform&&(f.transformBox=(u==null?void 0:u.transformBox)??"fill-box",delete h.transformBox),t!==void 0&&(h.x=t),s!==void 0&&(h.y=s),n!==void 0&&(h.scale=n),i!==void 0&&eu(h,i,a,o,!1)}const uo=()=>({...yn(),attrs:{}}),ho=e=>typeof e=="string"&&e.toLowerCase()==="svg";function tu(e,t,s,n){const i=x.useMemo(()=>{const a=uo();return co(a,t,ho(n),e.transformTemplate,e.style),{...a.attrs,style:{...a.style}}},[t]);if(e.style){const a={};lo(a,e.style,e),i.style={...a,...i.style}}return i}const su=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function vn(e){return typeof e!="string"||e.includes("-")?!1:!!(su.indexOf(e)>-1||/[A-Z]/u.test(e))}function nu(e,t,s,{latestValues:n},i,a=!1){const l=(vn(e)?tu:Zc)(t,n,i,e),d=Wc(t,typeof e=="string",a),c=e!==x.Fragment?{...d,...l,ref:s}:{},{children:u}=t,h=x.useMemo(()=>H(u)?u.get():u,[u]);return x.createElement(e,{...c,children:h})}function oi(e){const t=[{},{}];return e==null||e.values.forEach((s,n)=>{t[0][n]=s.get(),t[1][n]=s.getVelocity()}),t}function bn(e,t,s,n){if(typeof t=="function"){const[i,a]=oi(n);t=t(s!==void 0?s:e.custom,i,a)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[i,a]=oi(n);t=t(s!==void 0?s:e.custom,i,a)}return t}function Rt(e){return H(e)?e.get():e}function iu({scrapeMotionValuesFromProps:e,createRenderState:t},s,n,i){return{latestValues:ru(s,n,i,e),renderState:t()}}function ru(e,t,s,n){const i={},a=n(e,{});for(const f in a)i[f]=Rt(a[f]);let{initial:o,animate:l}=e;const d=$t(e),c=oo(e);t&&c&&!d&&e.inherit!==!1&&(o===void 0&&(o=t.initial),l===void 0&&(l=t.animate));let u=s?s.initial===!1:!1;u=u||o===!1;const h=u?l:o;if(h&&typeof h!="boolean"&&!Ot(h)){const f=Array.isArray(h)?h:[h];for(let m=0;m<f.length;m++){const p=bn(e,f[m]);if(p){const{transitionEnd:y,transition:j,...b}=p;for(const v in b){let w=b[v];if(Array.isArray(w)){const C=u?w.length-1:0;w=w[C]}w!==null&&(i[v]=w)}for(const v in y)i[v]=y[v]}}}return i}const fo=e=>(t,s)=>{const n=x.useContext(zt),i=x.useContext(Ft),a=()=>iu(e,t,n,i);return s?a():Hs(a)};function wn(e,t,s){var a;const{style:n}=e,i={};for(const o in n)(H(n[o])||t.style&&H(t.style[o])||ao(o,e)||((a=s==null?void 0:s.getValue(o))==null?void 0:a.liveStyle)!==void 0)&&(i[o]=n[o]);return i}const ou=fo({scrapeMotionValuesFromProps:wn,createRenderState:yn});function po(e,t,s){const n=wn(e,t,s);for(const i in e)if(H(e[i])||H(t[i])){const a=He.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;n[a]=e[i]}return n}const au=fo({scrapeMotionValuesFromProps:po,createRenderState:uo}),lu=Symbol.for("motionComponentSymbol");function Ie(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function cu(e,t,s){return x.useCallback(n=>{n&&e.onMount&&e.onMount(n),t&&(n?t.mount(n):t.unmount()),s&&(typeof s=="function"?s(n):Ie(s)&&(s.current=n))},[t])}const jn=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),uu="framerAppearId",mo="data-"+jn(uu),go=x.createContext({});function du(e,t,s,n,i){var y,j;const{visualElement:a}=x.useContext(zt),o=x.useContext(io),l=x.useContext(Ft),d=x.useContext(pn).reducedMotion,c=x.useRef(null);n=n||o.renderer,!c.current&&n&&(c.current=n(e,{visualState:t,parent:a,props:s,presenceContext:l,blockInitialAnimation:l?l.initial===!1:!1,reducedMotionConfig:d}));const u=c.current,h=x.useContext(go);u&&!u.projection&&i&&(u.type==="html"||u.type==="svg")&&hu(c.current,s,i,h);const f=x.useRef(!1);x.useInsertionEffect(()=>{u&&f.current&&u.update(s,l)});const m=s[mo],p=x.useRef(!!m&&!((y=window.MotionHandoffIsComplete)!=null&&y.call(window,m))&&((j=window.MotionHasOptimisedAnimation)==null?void 0:j.call(window,m)));return hr(()=>{u&&(f.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),u.scheduleRenderMicrotask(),p.current&&u.animationState&&u.animationState.animateChanges())}),x.useEffect(()=>{u&&(!p.current&&u.animationState&&u.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{var b;(b=window.MotionHandoffMarkAsComplete)==null||b.call(window,m)}),p.current=!1))}),u}function hu(e,t,s,n){const{layoutId:i,layout:a,drag:o,dragConstraints:l,layoutScroll:d,layoutRoot:c,layoutCrossfade:u}=t;e.projection=new s(e.latestValues,t["data-framer-portal-id"]?void 0:xo(e.parent)),e.projection.setOptions({layoutId:i,layout:a,alwaysMeasureLayout:!!o||l&&Ie(l),visualElement:e,animationType:typeof a=="string"?a:"both",initialPromotionConfig:n,crossfade:u,layoutScroll:d,layoutRoot:c})}function xo(e){if(e)return e.options.allowProjection!==!1?e.projection:xo(e.parent)}function Qt(e,{forwardMotionProps:t=!1}={},s,n){s&&zc(s);const i=vn(e)?au:ou;function a(l,d){let c;const u={...x.useContext(pn),...l,layoutId:fu(l)},{isStatic:h}=u,f=Hc(l),m=i(l,h);if(!h&&Ks){pu();const p=mu(u);c=p.MeasureLayout,f.visualElement=du(e,m,u,n,p.ProjectionNode)}return r.jsxs(zt.Provider,{value:f,children:[c&&f.visualElement?r.jsx(c,{visualElement:f.visualElement,...u}):null,nu(e,l,cu(m,f.visualElement,d),m,h,t)]})}a.displayName=`motion.${typeof e=="string"?e:`create(${e.displayName??e.name??""})`}`;const o=x.forwardRef(a);return o[lu]=e,o}function fu({layoutId:e}){const t=x.useContext(Us).id;return t&&e!==void 0?t+"-"+e:e}function pu(e,t){x.useContext(io).strict}function mu(e){const{drag:t,layout:s}=We;if(!t&&!s)return{};const n={...t,...s};return{MeasureLayout:t!=null&&t.isEnabled(e)||s!=null&&s.isEnabled(e)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}function gu(e,t){if(typeof Proxy>"u")return Qt;const s=new Map,n=(a,o)=>Qt(a,o,e,t),i=(a,o)=>n(a,o);return new Proxy(i,{get:(a,o)=>o==="create"?n:(s.has(o)||s.set(o,Qt(o,void 0,e,t)),s.get(o))})}function yo({top:e,left:t,right:s,bottom:n}){return{x:{min:t,max:s},y:{min:e,max:n}}}function xu({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function yu(e,t){if(!t)return e;const s=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:s.y,left:s.x,bottom:n.y,right:n.x}}function es(e){return e===void 0||e===1}function Ds({scale:e,scaleX:t,scaleY:s}){return!es(e)||!es(t)||!es(s)}function Pe(e){return Ds(e)||vo(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function vo(e){return ai(e.x)||ai(e.y)}function ai(e){return e&&e!=="0%"}function Nt(e,t,s){const n=e-s,i=t*n;return s+i}function li(e,t,s,n,i){return i!==void 0&&(e=Nt(e,i,n)),Nt(e,s,n)+t}function Vs(e,t=0,s=1,n,i){e.min=li(e.min,t,s,n,i),e.max=li(e.max,t,s,n,i)}function bo(e,{x:t,y:s}){Vs(e.x,t.translate,t.scale,t.originPoint),Vs(e.y,s.translate,s.scale,s.originPoint)}const ci=.999999999999,ui=1.0000000000001;function vu(e,t,s,n=!1){const i=s.length;if(!i)return;t.x=t.y=1;let a,o;for(let l=0;l<i;l++){a=s[l],o=a.projectionDelta;const{visualElement:d}=a.options;d&&d.props.style&&d.props.style.display==="contents"||(n&&a.options.layoutScroll&&a.scroll&&a!==a.root&&Fe(e,{x:-a.scroll.offset.x,y:-a.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,bo(e,o)),n&&Pe(a.latestValues)&&Fe(e,a.latestValues))}t.x<ui&&t.x>ci&&(t.x=1),t.y<ui&&t.y>ci&&(t.y=1)}function Ne(e,t){e.min=e.min+t,e.max=e.max+t}function di(e,t,s,n,i=.5){const a=B(e.min,e.max,i);Vs(e,t,s,a,n)}function Fe(e,t){di(e.x,t.x,t.scaleX,t.scale,t.originX),di(e.y,t.y,t.scaleY,t.scale,t.originY)}function wo(e,t){return yo(yu(e.getBoundingClientRect(),t))}function bu(e,t,s){const n=wo(e,s),{scroll:i}=t;return i&&(Ne(n.x,i.offset.x),Ne(n.y,i.offset.y)),n}const hi=()=>({translate:0,scale:1,origin:0,originPoint:0}),ze=()=>({x:hi(),y:hi()}),fi=()=>({min:0,max:0}),F=()=>({x:fi(),y:fi()}),Es={current:null},jo={current:!1};function wu(){if(jo.current=!0,!!Ks)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Es.current=e.matches;e.addEventListener("change",t),t()}else Es.current=!1}const ju=new WeakMap;function Su(e,t,s){for(const n in t){const i=t[n],a=s[n];if(H(i))e.addValue(n,i);else if(H(a))e.addValue(n,$e(i,{owner:e}));else if(a!==i)if(e.hasValue(n)){const o=e.getValue(n);o.liveStyle===!0?o.jump(i):o.hasAnimated||o.set(i)}else{const o=e.getStaticValue(n);e.addValue(n,$e(o!==void 0?o:i,{owner:e}))}}for(const n in s)t[n]===void 0&&e.removeValue(n);return t}const pi=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Tu{scrapeMotionValuesFromProps(t,s,n){return{}}constructor({parent:t,props:s,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:a,visualState:o},l={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=cn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const f=Y.now();this.renderScheduledAt<f&&(this.renderScheduledAt=f,E.render(this.render,!1,!0))};const{latestValues:d,renderState:c}=o;this.latestValues=d,this.baseTarget={...d},this.initialValues=s.initial?{...d}:{},this.renderState=c,this.parent=t,this.props=s,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=l,this.blockInitialAnimation=!!a,this.isControllingVariants=$t(s),this.isVariantNode=oo(s),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(s,{},this);for(const f in h){const m=h[f];d[f]!==void 0&&H(m)&&m.set(d[f],!1)}}mount(t){this.current=t,ju.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((s,n)=>this.bindToMotionValue(n,s)),jo.current||wu(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Es.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),we(this.notifyUpdate),we(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const s=this.features[t];s&&(s.unmount(),s.isMounted=!1)}this.current=null}bindToMotionValue(t,s){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=Ke.has(t);n&&this.onBindTransform&&this.onBindTransform();const i=s.on("change",l=>{this.latestValues[t]=l,this.props.onUpdate&&E.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),a=s.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,s)),this.valueSubscriptions.set(t,()=>{i(),a(),o&&o(),s.owner&&s.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in We){const s=We[t];if(!s)continue;const{isEnabled:n,Feature:i}=s;if(!this.features[t]&&i&&n(this.props)&&(this.features[t]=new i(this)),this.features[t]){const a=this.features[t];a.isMounted?a.update():(a.mount(),a.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):F()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,s){this.latestValues[t]=s}update(t,s){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=s;for(let n=0;n<pi.length;n++){const i=pi[n];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const a="on"+i,o=t[a];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=Su(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const s=this.getClosestVariantNode();if(s)return s.variantChildren&&s.variantChildren.add(t),()=>s.variantChildren.delete(t)}addValue(t,s){const n=this.values.get(t);s!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,s),this.values.set(t,s),this.latestValues[t]=s.get())}removeValue(t){this.values.delete(t);const s=this.valueSubscriptions.get(t);s&&(s(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,s){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return n===void 0&&s!==void 0&&(n=$e(s===null?void 0:s,{owner:this}),this.addValue(t,n)),n}readValue(t,s){let n=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options);return n!=null&&(typeof n=="string"&&(fr(n)||mr(n))?n=parseFloat(n):!Ec(n)&&je.test(s)&&(n=Yr(t,s)),this.setBaseTarget(t,H(n)?n.get():n)),H(n)?n.get():n}setBaseTarget(t,s){this.baseTarget[t]=s}getBaseTarget(t){var a;const{initial:s}=this.props;let n;if(typeof s=="string"||typeof s=="object"){const o=bn(this.props,s,(a=this.presenceContext)==null?void 0:a.custom);o&&(n=o[t])}if(s&&n!==void 0)return n;const i=this.getBaseTargetFromProps(this.props,t);return i!==void 0&&!H(i)?i:this.initialValues[t]!==void 0&&n===void 0?void 0:this.baseTarget[t]}on(t,s){return this.events[t]||(this.events[t]=new Ys),this.events[t].add(s)}notify(t,...s){this.events[t]&&this.events[t].notify(...s)}scheduleRenderMicrotask(){hn.render(this.render)}}class So extends Tu{constructor(){super(...arguments),this.KeyframeResolver=wc}sortInstanceNodePosition(t,s){return t.compareDocumentPosition(s)&2?1:-1}getBaseTargetFromProps(t,s){return t.style?t.style[s]:void 0}removeValueFromRenderState(t,{vars:s,style:n}){delete s[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;H(t)&&(this.childSubscription=t.on("change",s=>{this.current&&(this.current.textContent=`${s}`)}))}}function To(e,{style:t,vars:s},n,i){const a=e.style;let o;for(o in t)a[o]=t[o];i==null||i.applyProjectionStyles(a,n);for(o in s)a.setProperty(o,s[o])}function Cu(e){return window.getComputedStyle(e)}class Pu extends So{constructor(){super(...arguments),this.type="html",this.renderInstance=To}readValueFromInstance(t,s){var n;if(Ke.has(s))return(n=this.projection)!=null&&n.isProjecting?Cs(s):Ol(t,s);{const i=Cu(t),a=(Qs(s)?i.getPropertyValue(s):i[s])||0;return typeof a=="string"?a.trim():a}}measureInstanceViewportBox(t,{transformPagePoint:s}){return wo(t,s)}build(t,s,n){xn(t,s,n.transformTemplate)}scrapeMotionValuesFromProps(t,s,n){return wn(t,s,n)}}const Co=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Au(e,t,s,n){To(e,t,void 0,n);for(const i in t.attrs)e.setAttribute(Co.has(i)?i:jn(i),t.attrs[i])}class Mu extends So{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=F}getBaseTargetFromProps(t,s){return t[s]}readValueFromInstance(t,s){if(Ke.has(s)){const n=Xr(s);return n&&n.default||0}return s=Co.has(s)?s:jn(s),t.getAttribute(s)}scrapeMotionValuesFromProps(t,s,n){return po(t,s,n)}build(t,s,n){co(t,s,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,s,n,i){Au(t,s,n,i)}mount(t){this.isSVGTag=ho(t.tagName),super.mount(t)}}const Ru=(e,t)=>vn(e)?new Mu(t):new Pu(t,{allowProjection:e!==x.Fragment});function ft(e,t,s){const n=e.getProps();return bn(n,t,s!==void 0?s:n.custom,e)}const Bs=e=>Array.isArray(e);function ku(e,t,s){e.hasValue(t)?e.getValue(t).set(s):e.addValue(t,$e(s))}function Du(e){return Bs(e)?e[e.length-1]||0:e}function Vu(e,t){const s=ft(e,t);let{transitionEnd:n={},transition:i={},...a}=s||{};a={...a,...n};for(const o in a){const l=Du(a[o]);ku(e,o,l)}}function Eu(e){return!!(H(e)&&e.add)}function Ls(e,t){const s=e.getValue("willChange");if(Eu(s))return s.add(t);if(!s&&pe.WillChange){const n=new pe.WillChange("auto");e.addValue("willChange",n),n.add(t)}}function Po(e){return e.props[mo]}const Bu=e=>e!==null;function Lu(e,{repeat:t,repeatType:s="loop"},n){const i=e.filter(Bu),a=t&&s!=="loop"&&t%2===1?0:i.length-1;return i[a]}const Iu={type:"spring",stiffness:500,damping:25,restSpeed:10},Nu=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),Fu={type:"keyframes",duration:.8},zu={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Ou=(e,{keyframes:t})=>t.length>2?Fu:Ke.has(e)?e.startsWith("scale")?Nu(t[1]):Iu:zu;function $u({when:e,delay:t,delayChildren:s,staggerChildren:n,staggerDirection:i,repeat:a,repeatType:o,repeatDelay:l,from:d,elapsed:c,...u}){return!!Object.keys(u).length}const Sn=(e,t,s,n={},i,a)=>o=>{const l=un(n,e)||{},d=l.delay||n.delay||0;let{elapsed:c=0}=n;c=c-ce(d);const u={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-c,onUpdate:f=>{t.set(f),l.onUpdate&&l.onUpdate(f)},onComplete:()=>{o(),l.onComplete&&l.onComplete()},name:e,motionValue:t,element:a?void 0:i};$u(l)||Object.assign(u,Ou(e,u)),u.duration&&(u.duration=ce(u.duration)),u.repeatDelay&&(u.repeatDelay=ce(u.repeatDelay)),u.from!==void 0&&(u.keyframes[0]=u.from);let h=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(u.duration=0,u.delay===0&&(h=!0)),(pe.instantAnimations||pe.skipAnimations)&&(h=!0,u.duration=0,u.delay=0),u.allowFlatten=!l.type&&!l.ease,h&&!a&&t.get()!==void 0){const f=Lu(u.keyframes,l);if(f!==void 0){E.update(()=>{u.onUpdate(f),u.onComplete()});return}}return l.isSync?new ln(u):new cc(u)};function Wu({protectedKeys:e,needsAnimating:t},s){const n=e.hasOwnProperty(s)&&t[s]!==!0;return t[s]=!1,n}function Ao(e,t,{delay:s=0,transitionOverride:n,type:i}={}){let{transition:a=e.getDefaultTransition(),transitionEnd:o,...l}=t;n&&(a=n);const d=[],c=i&&e.animationState&&e.animationState.getState()[i];for(const u in l){const h=e.getValue(u,e.latestValues[u]??null),f=l[u];if(f===void 0||c&&Wu(c,u))continue;const m={delay:s,...un(a||{},u)},p=h.get();if(p!==void 0&&!h.isAnimating&&!Array.isArray(f)&&f===p&&!m.velocity)continue;let y=!1;if(window.MotionHandoffAnimation){const b=Po(e);if(b){const v=window.MotionHandoffAnimation(b,u,E);v!==null&&(m.startTime=v,y=!0)}}Ls(e,u),h.start(Sn(u,h,f,e.shouldReduceMotion&&_r.has(u)?{type:!1}:m,e,y));const j=h.animation;j&&d.push(j)}return o&&Promise.all(d).then(()=>{E.update(()=>{o&&Vu(e,o)})}),d}function Is(e,t,s={}){var d;const n=ft(e,t,s.type==="exit"?(d=e.presenceContext)==null?void 0:d.custom:void 0);let{transition:i=e.getDefaultTransition()||{}}=n||{};s.transitionOverride&&(i=s.transitionOverride);const a=n?()=>Promise.all(Ao(e,n,s)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:h,staggerDirection:f}=i;return Uu(e,t,c,u,h,f,s)}:()=>Promise.resolve(),{when:l}=i;if(l){const[c,u]=l==="beforeChildren"?[a,o]:[o,a];return c().then(()=>u())}else return Promise.all([a(),o(s.delay)])}function Uu(e,t,s=0,n=0,i=0,a=1,o){const l=[],d=e.variantChildren.size,c=(d-1)*i,u=typeof n=="function",h=u?f=>n(f,d):a===1?(f=0)=>f*i:(f=0)=>c-f*i;return Array.from(e.variantChildren).sort(Hu).forEach((f,m)=>{f.notify("AnimationStart",t),l.push(Is(f,t,{...o,delay:s+(u?0:n)+h(m)}).then(()=>f.notify("AnimationComplete",t)))}),Promise.all(l)}function Hu(e,t){return e.sortNodePosition(t)}function Ku(e,t,s={}){e.notify("AnimationStart",t);let n;if(Array.isArray(t)){const i=t.map(a=>Is(e,a,s));n=Promise.all(i)}else if(typeof t=="string")n=Is(e,t,s);else{const i=typeof t=="function"?ft(e,t,s.custom):t;n=Promise.all(Ao(e,i,s))}return n.then(()=>{e.notify("AnimationComplete",t)})}function Mo(e,t){if(!Array.isArray(t))return!1;const s=t.length;if(s!==e.length)return!1;for(let n=0;n<s;n++)if(t[n]!==e[n])return!1;return!0}const _u=gn.length;function Ro(e){if(!e)return;if(!e.isControllingVariants){const s=e.parent?Ro(e.parent)||{}:{};return e.props.initial!==void 0&&(s.initial=e.props.initial),s}const t={};for(let s=0;s<_u;s++){const n=gn[s],i=e.props[n];(dt(i)||i===!1)&&(t[n]=i)}return t}const Gu=[...mn].reverse(),qu=mn.length;function Xu(e){return t=>Promise.all(t.map(({animation:s,options:n})=>Ku(e,s,n)))}function Yu(e){let t=Xu(e),s=mi(),n=!0;const i=d=>(c,u)=>{var f;const h=ft(e,u,d==="exit"?(f=e.presenceContext)==null?void 0:f.custom:void 0);if(h){const{transition:m,transitionEnd:p,...y}=h;c={...c,...y,...p}}return c};function a(d){t=d(e)}function o(d){const{props:c}=e,u=Ro(e.parent)||{},h=[],f=new Set;let m={},p=1/0;for(let j=0;j<qu;j++){const b=Gu[j],v=s[b],w=c[b]!==void 0?c[b]:u[b],C=dt(w),T=b===d?v.isActive:null;T===!1&&(p=j);let A=w===u[b]&&w!==c[b]&&C;if(A&&n&&e.manuallyAnimateOnMount&&(A=!1),v.protectedKeys={...m},!v.isActive&&T===null||!w&&!v.prevProp||Ot(w)||typeof w=="boolean")continue;const g=Zu(v.prevProp,w);let S=g||b===d&&v.isActive&&!A&&C||j>p&&C,R=!1;const D=Array.isArray(w)?w:[w];let O=D.reduce(i(b),{});T===!1&&(O={});const{prevResolvedValues:Z={}}=v,ge={...Z,...O},he=$=>{S=!0,f.has($)&&(R=!0,f.delete($)),v.needsAnimating[$]=!0;const se=e.getValue($);se&&(se.liveStyle=!1)};for(const $ in ge){const se=O[$],Ge=Z[$];if(m.hasOwnProperty($))continue;let qe=!1;Bs(se)&&Bs(Ge)?qe=!Mo(se,Ge):qe=se!==Ge,qe?se!=null?he($):f.add($):se!==void 0&&f.has($)?he($):v.protectedKeys[$]=!0}v.prevProp=w,v.prevResolvedValues=O,v.isActive&&(m={...m,...O}),n&&e.blockInitialAnimation&&(S=!1),S&&(!(A&&g)||R)&&h.push(...D.map($=>({animation:$,options:{type:b}})))}if(f.size){const j={};if(typeof c.initial!="boolean"){const b=ft(e,Array.isArray(c.initial)?c.initial[0]:c.initial);b&&b.transition&&(j.transition=b.transition)}f.forEach(b=>{const v=e.getBaseTarget(b),w=e.getValue(b);w&&(w.liveStyle=!0),j[b]=v??null}),h.push({animation:j})}let y=!!h.length;return n&&(c.initial===!1||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(y=!1),n=!1,y?t(h):Promise.resolve()}function l(d,c){var h;if(s[d].isActive===c)return Promise.resolve();(h=e.variantChildren)==null||h.forEach(f=>{var m;return(m=f.animationState)==null?void 0:m.setActive(d,c)}),s[d].isActive=c;const u=o(d);for(const f in s)s[f].protectedKeys={};return u}return{animateChanges:o,setActive:l,setAnimateFunction:a,getState:()=>s,reset:()=>{s=mi(),n=!0}}}function Zu(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Mo(t,e):!1}function Ce(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function mi(){return{animate:Ce(!0),whileInView:Ce(),whileHover:Ce(),whileTap:Ce(),whileDrag:Ce(),whileFocus:Ce(),exit:Ce()}}class Se{constructor(t){this.isMounted=!1,this.node=t}update(){}}class Ju extends Se{constructor(t){super(t),t.animationState||(t.animationState=Yu(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Ot(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:s}=this.node.prevProps||{};t!==s&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)==null||t.call(this)}}let Qu=0;class ed extends Se{constructor(){super(...arguments),this.id=Qu++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:s}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const i=this.node.animationState.setActive("exit",!t);s&&!t&&i.then(()=>{s(this.id)})}mount(){const{register:t,onExitComplete:s}=this.node.presenceContext||{};s&&s(this.id),t&&(this.unmount=t(this.id))}unmount(){}}const td={animation:{Feature:Ju},exit:{Feature:ed}};function pt(e,t,s,n={passive:!0}){return e.addEventListener(t,s,n),()=>e.removeEventListener(t,s)}function yt(e){return{point:{x:e.pageX,y:e.pageY}}}const sd=e=>t=>fn(t)&&e(t,yt(t));function tt(e,t,s,n){return pt(e,t,sd(s),n)}const ko=1e-4,nd=1-ko,id=1+ko,Do=.01,rd=0-Do,od=0+Do;function _(e){return e.max-e.min}function ad(e,t,s){return Math.abs(e-t)<=s}function gi(e,t,s,n=.5){e.origin=n,e.originPoint=B(t.min,t.max,e.origin),e.scale=_(s)/_(t),e.translate=B(s.min,s.max,e.origin)-e.originPoint,(e.scale>=nd&&e.scale<=id||isNaN(e.scale))&&(e.scale=1),(e.translate>=rd&&e.translate<=od||isNaN(e.translate))&&(e.translate=0)}function st(e,t,s,n){gi(e.x,t.x,s.x,n?n.originX:void 0),gi(e.y,t.y,s.y,n?n.originY:void 0)}function xi(e,t,s){e.min=s.min+t.min,e.max=e.min+_(t)}function ld(e,t,s){xi(e.x,t.x,s.x),xi(e.y,t.y,s.y)}function yi(e,t,s){e.min=t.min-s.min,e.max=e.min+_(t)}function nt(e,t,s){yi(e.x,t.x,s.x),yi(e.y,t.y,s.y)}function Q(e){return[e("x"),e("y")]}const Vo=({current:e})=>e?e.ownerDocument.defaultView:null,vi=(e,t)=>Math.abs(e-t);function cd(e,t){const s=vi(e.x,t.x),n=vi(e.y,t.y);return Math.sqrt(s**2+n**2)}class Eo{constructor(t,s,{transformPagePoint:n,contextWindow:i=window,dragSnapToOrigin:a=!1,distanceThreshold:o=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const f=ss(this.lastMoveEventInfo,this.history),m=this.startEvent!==null,p=cd(f.offset,{x:0,y:0})>=this.distanceThreshold;if(!m&&!p)return;const{point:y}=f,{timestamp:j}=W;this.history.push({...y,timestamp:j});const{onStart:b,onMove:v}=this.handlers;m||(b&&b(this.lastMoveEvent,f),this.startEvent=this.lastMoveEvent),v&&v(this.lastMoveEvent,f)},this.handlePointerMove=(f,m)=>{this.lastMoveEvent=f,this.lastMoveEventInfo=ts(m,this.transformPagePoint),E.update(this.updatePoint,!0)},this.handlePointerUp=(f,m)=>{this.end();const{onEnd:p,onSessionEnd:y,resumeAnimation:j}=this.handlers;if(this.dragSnapToOrigin&&j&&j(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const b=ss(f.type==="pointercancel"?this.lastMoveEventInfo:ts(m,this.transformPagePoint),this.history);this.startEvent&&p&&p(f,b),y&&y(f,b)},!fn(t))return;this.dragSnapToOrigin=a,this.handlers=s,this.transformPagePoint=n,this.distanceThreshold=o,this.contextWindow=i||window;const l=yt(t),d=ts(l,this.transformPagePoint),{point:c}=d,{timestamp:u}=W;this.history=[{...c,timestamp:u}];const{onSessionStart:h}=s;h&&h(t,ss(d,this.history)),this.removeListeners=mt(tt(this.contextWindow,"pointermove",this.handlePointerMove),tt(this.contextWindow,"pointerup",this.handlePointerUp),tt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),we(this.updatePoint)}}function ts(e,t){return t?{point:t(e.point)}:e}function bi(e,t){return{x:e.x-t.x,y:e.y-t.y}}function ss({point:e},t){return{point:e,delta:bi(e,Bo(t)),offset:bi(e,ud(t)),velocity:dd(t,.1)}}function ud(e){return e[0]}function Bo(e){return e[e.length-1]}function dd(e,t){if(e.length<2)return{x:0,y:0};let s=e.length-1,n=null;const i=Bo(e);for(;s>=0&&(n=e[s],!(i.timestamp-n.timestamp>ce(t)));)s--;if(!n)return{x:0,y:0};const a=ue(i.timestamp-n.timestamp);if(a===0)return{x:0,y:0};const o={x:(i.x-n.x)/a,y:(i.y-n.y)/a};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function hd(e,{min:t,max:s},n){return t!==void 0&&e<t?e=n?B(t,e,n.min):Math.max(e,t):s!==void 0&&e>s&&(e=n?B(s,e,n.max):Math.min(e,s)),e}function wi(e,t,s){return{min:t!==void 0?e.min+t:void 0,max:s!==void 0?e.max+s-(e.max-e.min):void 0}}function fd(e,{top:t,left:s,bottom:n,right:i}){return{x:wi(e.x,s,i),y:wi(e.y,t,n)}}function ji(e,t){let s=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([s,n]=[n,s]),{min:s,max:n}}function pd(e,t){return{x:ji(e.x,t.x),y:ji(e.y,t.y)}}function md(e,t){let s=.5;const n=_(e),i=_(t);return i>n?s=lt(t.min,t.max-n,e.min):n>i&&(s=lt(e.min,e.max-i,t.min)),fe(0,1,s)}function gd(e,t){const s={};return t.min!==void 0&&(s.min=t.min-e.min),t.max!==void 0&&(s.max=t.max-e.min),s}const Ns=.35;function xd(e=Ns){return e===!1?e=0:e===!0&&(e=Ns),{x:Si(e,"left","right"),y:Si(e,"top","bottom")}}function Si(e,t,s){return{min:Ti(e,t),max:Ti(e,s)}}function Ti(e,t){return typeof e=="number"?e:e[t]||0}const yd=new WeakMap;class vd{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=F(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:s=!1,distanceThreshold:n}={}){const{presenceContext:i}=this.visualElement;if(i&&i.isPresent===!1)return;const a=h=>{const{dragSnapToOrigin:f}=this.getProps();f?this.pauseAnimation():this.stopAnimation(),s&&this.snapToCursor(yt(h).point)},o=(h,f)=>{const{drag:m,dragPropagation:p,onDragStart:y}=this.getProps();if(m&&!p&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Cc(m),!this.openDragLock))return;this.latestPointerEvent=h,this.latestPanInfo=f,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Q(b=>{let v=this.getAxisMotionValue(b).get()||0;if(de.test(v)){const{projection:w}=this.visualElement;if(w&&w.layout){const C=w.layout.layoutBox[b];C&&(v=_(C)*(parseFloat(v)/100))}}this.originPoint[b]=v}),y&&E.postRender(()=>y(h,f)),Ls(this.visualElement,"transform");const{animationState:j}=this.visualElement;j&&j.setActive("whileDrag",!0)},l=(h,f)=>{this.latestPointerEvent=h,this.latestPanInfo=f;const{dragPropagation:m,dragDirectionLock:p,onDirectionLock:y,onDrag:j}=this.getProps();if(!m&&!this.openDragLock)return;const{offset:b}=f;if(p&&this.currentDirection===null){this.currentDirection=bd(b),this.currentDirection!==null&&y&&y(this.currentDirection);return}this.updateAxis("x",f.point,b),this.updateAxis("y",f.point,b),this.visualElement.render(),j&&j(h,f)},d=(h,f)=>{this.latestPointerEvent=h,this.latestPanInfo=f,this.stop(h,f),this.latestPointerEvent=null,this.latestPanInfo=null},c=()=>Q(h=>{var f;return this.getAnimationState(h)==="paused"&&((f=this.getAxisMotionValue(h).animation)==null?void 0:f.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Eo(t,{onSessionStart:a,onStart:o,onMove:l,onSessionEnd:d,resumeAnimation:c},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,distanceThreshold:n,contextWindow:Vo(this.visualElement)})}stop(t,s){const n=t||this.latestPointerEvent,i=s||this.latestPanInfo,a=this.isDragging;if(this.cancel(),!a||!i||!n)return;const{velocity:o}=i;this.startAnimation(o);const{onDragEnd:l}=this.getProps();l&&E.postRender(()=>l(n,i))}cancel(){this.isDragging=!1;const{projection:t,animationState:s}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),s&&s.setActive("whileDrag",!1)}updateAxis(t,s,n){const{drag:i}=this.getProps();if(!n||!jt(t,i,this.currentDirection))return;const a=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=hd(o,this.constraints[t],this.elastic[t])),a.set(o)}resolveConstraints(){var a;const{dragConstraints:t,dragElastic:s}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(a=this.visualElement.projection)==null?void 0:a.layout,i=this.constraints;t&&Ie(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=fd(n.layoutBox,t):this.constraints=!1,this.elastic=xd(s),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&Q(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=gd(n.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:s}=this.getProps();if(!t||!Ie(t))return!1;const n=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const a=bu(n,i.root,this.visualElement.getTransformPagePoint());let o=pd(i.layout.layoutBox,a);if(s){const l=s(xu(o));this.hasMutatedConstraints=!!l,l&&(o=yo(l))}return o}startAnimation(t){const{drag:s,dragMomentum:n,dragElastic:i,dragTransition:a,dragSnapToOrigin:o,onDragTransitionEnd:l}=this.getProps(),d=this.constraints||{},c=Q(u=>{if(!jt(u,s,this.currentDirection))return;let h=d&&d[u]||{};o&&(h={min:0,max:0});const f=i?200:1e6,m=i?40:1e7,p={type:"inertia",velocity:n?t[u]:0,bounceStiffness:f,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...a,...h};return this.startAxisValueAnimation(u,p)});return Promise.all(c).then(l)}startAxisValueAnimation(t,s){const n=this.getAxisMotionValue(t);return Ls(this.visualElement,t),n.start(Sn(t,n,0,s,this.visualElement,!1))}stopAnimation(){Q(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Q(t=>{var s;return(s=this.getAxisMotionValue(t).animation)==null?void 0:s.pause()})}getAnimationState(t){var s;return(s=this.getAxisMotionValue(t).animation)==null?void 0:s.state}getAxisMotionValue(t){const s=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),i=n[s];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){Q(s=>{const{drag:n}=this.getProps();if(!jt(s,n,this.currentDirection))return;const{projection:i}=this.visualElement,a=this.getAxisMotionValue(s);if(i&&i.layout){const{min:o,max:l}=i.layout.layoutBox[s];a.set(t[s]-B(o,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:s}=this.getProps(),{projection:n}=this.visualElement;if(!Ie(s)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Q(o=>{const l=this.getAxisMotionValue(o);if(l&&this.constraints!==!1){const d=l.get();i[o]=md({min:d,max:d},this.constraints[o])}});const{transformTemplate:a}=this.visualElement.getProps();this.visualElement.current.style.transform=a?a({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),Q(o=>{if(!jt(o,t,null))return;const l=this.getAxisMotionValue(o),{min:d,max:c}=this.constraints[o];l.set(B(d,c,i[o]))})}addListeners(){if(!this.visualElement.current)return;yd.set(this.visualElement,this);const t=this.visualElement.current,s=tt(t,"pointerdown",d=>{const{drag:c,dragListener:u=!0}=this.getProps();c&&u&&this.start(d)}),n=()=>{const{dragConstraints:d}=this.getProps();Ie(d)&&d.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,a=i.addEventListener("measure",n);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),E.read(n);const o=pt(window,"resize",()=>this.scalePositionWithinConstraints()),l=i.addEventListener("didUpdate",({delta:d,hasLayoutChanged:c})=>{this.isDragging&&c&&(Q(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=d[u].translate,h.set(h.get()+d[u].translate))}),this.visualElement.render())});return()=>{o(),s(),a(),l&&l()}}getProps(){const t=this.visualElement.getProps(),{drag:s=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:a=!1,dragElastic:o=Ns,dragMomentum:l=!0}=t;return{...t,drag:s,dragDirectionLock:n,dragPropagation:i,dragConstraints:a,dragElastic:o,dragMomentum:l}}}function jt(e,t,s){return(t===!0||t===e)&&(s===null||s===e)}function bd(e,t=10){let s=null;return Math.abs(e.y)>t?s="y":Math.abs(e.x)>t&&(s="x"),s}class wd extends Se{constructor(t){super(t),this.removeGroupControls=ee,this.removeListeners=ee,this.controls=new vd(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ee}unmount(){this.removeGroupControls(),this.removeListeners()}}const Ci=e=>(t,s)=>{e&&E.postRender(()=>e(t,s))};class jd extends Se{constructor(){super(...arguments),this.removePointerDownListener=ee}onPointerDown(t){this.session=new Eo(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Vo(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:s,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:Ci(t),onStart:Ci(s),onMove:n,onEnd:(a,o)=>{delete this.session,i&&E.postRender(()=>i(a,o))}}}mount(){this.removePointerDownListener=tt(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const kt={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Pi(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const Ye={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(P.test(e))e=parseFloat(e);else return e;const s=Pi(e,t.target.x),n=Pi(e,t.target.y);return`${s}% ${n}%`}},Sd={correct:(e,{treeScale:t,projectionDelta:s})=>{const n=e,i=je.parse(e);if(i.length>5)return n;const a=je.createTransformer(e),o=typeof i[0]!="number"?1:0,l=s.x.scale*t.x,d=s.y.scale*t.y;i[0+o]/=l,i[1+o]/=d;const c=B(l,d,.5);return typeof i[2+o]=="number"&&(i[2+o]/=c),typeof i[3+o]=="number"&&(i[3+o]/=c),a(i)}};let Ai=!1;class Td extends x.Component{componentDidMount(){const{visualElement:t,layoutGroup:s,switchLayoutGroup:n,layoutId:i}=this.props,{projection:a}=t;Kc(Cd),a&&(s.group&&s.group.add(a),n&&n.register&&i&&n.register(a),Ai&&a.root.didUpdate(),a.addEventListener("animationComplete",()=>{this.safeToRemove()}),a.setOptions({...a.options,onExitComplete:()=>this.safeToRemove()})),kt.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:s,visualElement:n,drag:i,isPresent:a}=this.props,{projection:o}=n;return o&&(o.isPresent=a,Ai=!0,i||t.layoutDependency!==s||s===void 0||t.isPresent!==a?o.willUpdate():this.safeToRemove(),t.isPresent!==a&&(a?o.promote():o.relegate()||E.postRender(()=>{const l=o.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),hn.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:s,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),s&&s.group&&s.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Lo(e){const[t,s]=no(),n=x.useContext(Us);return r.jsx(Td,{...e,layoutGroup:n,switchLayoutGroup:x.useContext(go),isPresent:t,safeToRemove:s})}const Cd={borderRadius:{...Ye,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Ye,borderTopRightRadius:Ye,borderBottomLeftRadius:Ye,borderBottomRightRadius:Ye,boxShadow:Sd};function Pd(e,t,s){const n=H(e)?e:$e(e);return n.start(Sn("",n,t,s)),n.animation}const Ad=(e,t)=>e.depth-t.depth;class Md{constructor(){this.children=[],this.isDirty=!1}add(t){_s(this.children,t),this.isDirty=!0}remove(t){Gs(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Ad),this.isDirty=!1,this.children.forEach(t)}}function Rd(e,t){const s=Y.now(),n=({timestamp:i})=>{const a=i-s;a>=t&&(we(n),e(a-t))};return E.setup(n,!0),()=>we(n)}const Io=["TopLeft","TopRight","BottomLeft","BottomRight"],kd=Io.length,Mi=e=>typeof e=="string"?parseFloat(e):e,Ri=e=>typeof e=="number"||P.test(e);function Dd(e,t,s,n,i,a){i?(e.opacity=B(0,s.opacity??1,Vd(n)),e.opacityExit=B(t.opacity??1,0,Ed(n))):a&&(e.opacity=B(t.opacity??1,s.opacity??1,n));for(let o=0;o<kd;o++){const l=`border${Io[o]}Radius`;let d=ki(t,l),c=ki(s,l);if(d===void 0&&c===void 0)continue;d||(d=0),c||(c=0),d===0||c===0||Ri(d)===Ri(c)?(e[l]=Math.max(B(Mi(d),Mi(c),n),0),(de.test(c)||de.test(d))&&(e[l]+="%")):e[l]=c}(t.rotate||s.rotate)&&(e.rotate=B(t.rotate||0,s.rotate||0,n))}function ki(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const Vd=No(0,.5,Sr),Ed=No(.5,.95,ee);function No(e,t,s){return n=>n<e?0:n>t?1:s(lt(e,t,n))}function Di(e,t){e.min=t.min,e.max=t.max}function J(e,t){Di(e.x,t.x),Di(e.y,t.y)}function Vi(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function Ei(e,t,s,n,i){return e-=t,e=Nt(e,1/s,n),i!==void 0&&(e=Nt(e,1/i,n)),e}function Bd(e,t=0,s=1,n=.5,i,a=e,o=e){if(de.test(t)&&(t=parseFloat(t),t=B(o.min,o.max,t/100)-o.min),typeof t!="number")return;let l=B(a.min,a.max,n);e===a&&(l-=t),e.min=Ei(e.min,t,s,l,i),e.max=Ei(e.max,t,s,l,i)}function Bi(e,t,[s,n,i],a,o){Bd(e,t[s],t[n],t[i],t.scale,a,o)}const Ld=["x","scaleX","originX"],Id=["y","scaleY","originY"];function Li(e,t,s,n){Bi(e.x,t,Ld,s?s.x:void 0,n?n.x:void 0),Bi(e.y,t,Id,s?s.y:void 0,n?n.y:void 0)}function Ii(e){return e.translate===0&&e.scale===1}function Fo(e){return Ii(e.x)&&Ii(e.y)}function Ni(e,t){return e.min===t.min&&e.max===t.max}function Nd(e,t){return Ni(e.x,t.x)&&Ni(e.y,t.y)}function Fi(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function zo(e,t){return Fi(e.x,t.x)&&Fi(e.y,t.y)}function zi(e){return _(e.x)/_(e.y)}function Oi(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class Fd{constructor(){this.members=[]}add(t){_s(this.members,t),t.scheduleRender()}remove(t){if(Gs(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const s=this.members[this.members.length-1];s&&this.promote(s)}}relegate(t){const s=this.members.findIndex(i=>t===i);if(s===0)return!1;let n;for(let i=s;i>=0;i--){const a=this.members[i];if(a.isPresent!==!1){n=a;break}}return n?(this.promote(n),!0):!1}promote(t,s){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,s&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&n.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:s,resumingFrom:n}=t;s.onExitComplete&&s.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function zd(e,t,s){let n="";const i=e.x.translate/t.x,a=e.y.translate/t.y,o=(s==null?void 0:s.z)||0;if((i||a||o)&&(n=`translate3d(${i}px, ${a}px, ${o}px) `),(t.x!==1||t.y!==1)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),s){const{transformPerspective:c,rotate:u,rotateX:h,rotateY:f,skewX:m,skewY:p}=s;c&&(n=`perspective(${c}px) ${n}`),u&&(n+=`rotate(${u}deg) `),h&&(n+=`rotateX(${h}deg) `),f&&(n+=`rotateY(${f}deg) `),m&&(n+=`skewX(${m}deg) `),p&&(n+=`skewY(${p}deg) `)}const l=e.x.scale*t.x,d=e.y.scale*t.y;return(l!==1||d!==1)&&(n+=`scale(${l}, ${d})`),n||"none"}const ns=["","X","Y","Z"],Od=1e3;let $d=0;function is(e,t,s,n){const{latestValues:i}=t;i[e]&&(s[e]=i[e],t.setStaticValue(e,0),n&&(n[e]=0))}function Oo(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const s=Po(t);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:i,layoutId:a}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",E,!(i||a))}const{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&Oo(n)}function $o({attachResizeListener:e,defaultParent:t,measureScroll:s,checkIsScrollRoot:n,resetTransform:i}){return class{constructor(o={},l=t==null?void 0:t()){this.id=$d++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(Hd),this.nodes.forEach(qd),this.nodes.forEach(Xd),this.nodes.forEach(Kd)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0;for(let d=0;d<this.path.length;d++)this.path[d].shouldResetTransform=!0;this.root===this&&(this.nodes=new Md)}addEventListener(o,l){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new Ys),this.eventHandlers.get(o).add(l)}notifyListeners(o,...l){const d=this.eventHandlers.get(o);d&&d.notify(...l)}hasListeners(o){return this.eventHandlers.has(o)}mount(o){if(this.instance)return;this.isSVG=so(o)&&!Dc(o),this.instance=o;const{layoutId:l,layout:d,visualElement:c}=this.options;if(c&&!c.current&&c.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(d||l)&&(this.isLayoutDirty=!0),e){let u,h=0;const f=()=>this.root.updateBlockedByResize=!1;E.read(()=>{h=window.innerWidth}),e(o,()=>{const m=window.innerWidth;m!==h&&(h=m,this.root.updateBlockedByResize=!0,u&&u(),u=Rd(f,250),kt.hasAnimatedSinceResize&&(kt.hasAnimatedSinceResize=!1,this.nodes.forEach(Ui)))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&c&&(l||d)&&this.addEventListener("didUpdate",({delta:u,hasLayoutChanged:h,hasRelativeLayoutChanged:f,layout:m})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const p=this.options.transition||c.getDefaultTransition()||eh,{onLayoutAnimationStart:y,onLayoutAnimationComplete:j}=c.getProps(),b=!this.targetLayout||!zo(this.targetLayout,m),v=!h&&f;if(this.options.layoutRoot||this.resumeFrom||v||h&&(b||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const w={...un(p,"layout"),onPlay:y,onComplete:j};(c.shouldReduceMotion||this.options.layoutRoot)&&(w.delay=0,w.type=!1),this.startAnimation(w),this.setAnimationOrigin(u,v)}else h||Ui(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=m})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),we(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Yd),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Oo(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:l,layout:d}=this.options;if(l===void 0&&!d)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach($i);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(Wi);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(Gd),this.nodes.forEach(Wd),this.nodes.forEach(Ud)):this.nodes.forEach(Wi),this.clearAllSnapshots();const l=Y.now();W.delta=fe(0,1e3/60,l-W.timestamp),W.timestamp=l,W.isProcessing=!0,Gt.update.process(W),Gt.preRender.process(W),Gt.render.process(W),W.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,hn.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(_d),this.sharedNodes.forEach(Zd)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,E.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){E.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!_(this.snapshot.measuredBox.x)&&!_(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let d=0;d<this.path.length;d++)this.path[d].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=F(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let l=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(l=!1),l&&this.instance){const d=n(this.instance);this.scroll={animationId:this.root.animationId,phase:o,isRoot:d,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:d}}}resetTransform(){if(!i)return;const o=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,l=this.projectionDelta&&!Fo(this.projectionDelta),d=this.getTransformTemplate(),c=d?d(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;o&&this.instance&&(l||Pe(this.latestValues)||u)&&(i(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const l=this.measurePageBox();let d=this.removeElementScroll(l);return o&&(d=this.removeTransform(d)),th(d),{animationId:this.root.animationId,measuredBox:l,layoutBox:d,latestValues:{},source:this.id}}measurePageBox(){var c;const{visualElement:o}=this.options;if(!o)return F();const l=o.measureViewportBox();if(!(((c=this.scroll)==null?void 0:c.wasRoot)||this.path.some(sh))){const{scroll:u}=this.root;u&&(Ne(l.x,u.offset.x),Ne(l.y,u.offset.y))}return l}removeElementScroll(o){var d;const l=F();if(J(l,o),(d=this.scroll)!=null&&d.wasRoot)return l;for(let c=0;c<this.path.length;c++){const u=this.path[c],{scroll:h,options:f}=u;u!==this.root&&h&&f.layoutScroll&&(h.wasRoot&&J(l,o),Ne(l.x,h.offset.x),Ne(l.y,h.offset.y))}return l}applyTransform(o,l=!1){const d=F();J(d,o);for(let c=0;c<this.path.length;c++){const u=this.path[c];!l&&u.options.layoutScroll&&u.scroll&&u!==u.root&&Fe(d,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),Pe(u.latestValues)&&Fe(d,u.latestValues)}return Pe(this.latestValues)&&Fe(d,this.latestValues),d}removeTransform(o){const l=F();J(l,o);for(let d=0;d<this.path.length;d++){const c=this.path[d];if(!c.instance||!Pe(c.latestValues))continue;Ds(c.latestValues)&&c.updateSnapshot();const u=F(),h=c.measurePageBox();J(u,h),Li(l,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return Pe(this.latestValues)&&Li(l,this.latestValues),l}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==W.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var f;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const d=!!this.resumingFrom||this!==l;if(!(o||d&&this.isSharedProjectionDirty||this.isProjectionDirty||(f=this.parent)!=null&&f.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:u,layoutId:h}=this.options;if(!(!this.layout||!(u||h))){if(this.resolvedRelativeTargetAt=W.timestamp,!this.targetDelta&&!this.relativeTarget){const m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=F(),this.relativeTargetOrigin=F(),nt(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),J(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=F(),this.targetWithTransforms=F()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),ld(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):J(this.target,this.layout.layoutBox),bo(this.target,this.targetDelta)):J(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const m=this.getClosestProjectingParent();m&&!!m.resumingFrom==!!this.resumingFrom&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=F(),this.relativeTargetOrigin=F(),nt(this.relativeTargetOrigin,this.target,m.target),J(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Ds(this.parent.latestValues)||vo(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var p;const o=this.getLead(),l=!!this.resumingFrom||this!==o;let d=!0;if((this.isProjectionDirty||(p=this.parent)!=null&&p.isProjectionDirty)&&(d=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(d=!1),this.resolvedRelativeTargetAt===W.timestamp&&(d=!1),d)return;const{layout:c,layoutId:u}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||u))return;J(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,f=this.treeScale.y;vu(this.layoutCorrected,this.treeScale,this.path,l),o.layout&&!o.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(o.target=o.layout.layoutBox,o.targetWithTransforms=F());const{target:m}=o;if(!m){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Vi(this.prevProjectionDelta.x,this.projectionDelta.x),Vi(this.prevProjectionDelta.y,this.projectionDelta.y)),st(this.projectionDelta,this.layoutCorrected,m,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==f||!Oi(this.projectionDelta.x,this.prevProjectionDelta.x)||!Oi(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",m))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){var l;if((l=this.options.visualElement)==null||l.scheduleRender(),o){const d=this.getStack();d&&d.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ze(),this.projectionDelta=ze(),this.projectionDeltaWithTransform=ze()}setAnimationOrigin(o,l=!1){const d=this.snapshot,c=d?d.latestValues:{},u={...this.latestValues},h=ze();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;const f=F(),m=d?d.source:void 0,p=this.layout?this.layout.source:void 0,y=m!==p,j=this.getStack(),b=!j||j.members.length<=1,v=!!(y&&!b&&this.options.crossfade===!0&&!this.path.some(Qd));this.animationProgress=0;let w;this.mixTargetDelta=C=>{const T=C/1e3;Hi(h.x,o.x,T),Hi(h.y,o.y,T),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(nt(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Jd(this.relativeTarget,this.relativeTargetOrigin,f,T),w&&Nd(this.relativeTarget,w)&&(this.isProjectionDirty=!1),w||(w=F()),J(w,this.relativeTarget)),y&&(this.animationValues=u,Dd(u,c,this.latestValues,T,v,b)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=T},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){var l,d,c;this.notifyListeners("animationStart"),(l=this.currentAnimation)==null||l.stop(),(c=(d=this.resumingFrom)==null?void 0:d.currentAnimation)==null||c.stop(),this.pendingAnimation&&(we(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=E.update(()=>{kt.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=$e(0)),this.currentAnimation=Pd(this.motionValue,[0,1e3],{...o,velocity:0,isSync:!0,onUpdate:u=>{this.mixTargetDelta(u),o.onUpdate&&o.onUpdate(u)},onStop:()=>{},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Od),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:l,target:d,layout:c,latestValues:u}=o;if(!(!l||!d||!c)){if(this!==o&&this.layout&&c&&Wo(this.options.animationType,this.layout.layoutBox,c.layoutBox)){d=this.target||F();const h=_(this.layout.layoutBox.x);d.x.min=o.target.x.min,d.x.max=d.x.min+h;const f=_(this.layout.layoutBox.y);d.y.min=o.target.y.min,d.y.max=d.y.min+f}J(l,d),Fe(l,u),st(this.projectionDeltaWithTransform,this.layoutCorrected,l,u)}}registerSharedNode(o,l){this.sharedNodes.has(o)||this.sharedNodes.set(o,new Fd),this.sharedNodes.get(o).add(l);const c=l.options.initialPromotionConfig;l.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(l):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var l;const{layoutId:o}=this.options;return o?((l=this.getStack())==null?void 0:l.lead)||this:this}getPrevLead(){var l;const{layoutId:o}=this.options;return o?(l=this.getStack())==null?void 0:l.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:l,preserveFollowOpacity:d}={}){const c=this.getStack();c&&c.promote(this,d),o&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetSkewAndRotation(){const{visualElement:o}=this.options;if(!o)return;let l=!1;const{latestValues:d}=o;if((d.z||d.rotate||d.rotateX||d.rotateY||d.rotateZ||d.skewX||d.skewY)&&(l=!0),!l)return;const c={};d.z&&is("z",o,c,this.animationValues);for(let u=0;u<ns.length;u++)is(`rotate${ns[u]}`,o,c,this.animationValues),is(`skew${ns[u]}`,o,c,this.animationValues);o.render();for(const u in c)o.setStaticValue(u,c[u]),this.animationValues&&(this.animationValues[u]=c[u]);o.scheduleRender()}applyProjectionStyles(o,l){if(!this.instance||this.isSVG)return;if(!this.isVisible){o.visibility="hidden";return}const d=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,o.visibility="",o.opacity="",o.pointerEvents=Rt(l==null?void 0:l.pointerEvents)||"",o.transform=d?d(this.latestValues,""):"none";return}const c=this.getLead();if(!this.projectionDelta||!this.layout||!c.target){this.options.layoutId&&(o.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,o.pointerEvents=Rt(l==null?void 0:l.pointerEvents)||""),this.hasProjected&&!Pe(this.latestValues)&&(o.transform=d?d({},""):"none",this.hasProjected=!1);return}o.visibility="";const u=c.animationValues||c.latestValues;this.applyTransformsToTarget();let h=zd(this.projectionDeltaWithTransform,this.treeScale,u);d&&(h=d(u,h)),o.transform=h;const{x:f,y:m}=this.projectionDelta;o.transformOrigin=`${f.origin*100}% ${m.origin*100}% 0`,c.animationValues?o.opacity=c===this?u.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:u.opacityExit:o.opacity=c===this?u.opacity!==void 0?u.opacity:"":u.opacityExit!==void 0?u.opacityExit:0;for(const p in ht){if(u[p]===void 0)continue;const{correct:y,applyTo:j,isCSSVariable:b}=ht[p],v=h==="none"?u[p]:y(u[p],c);if(j){const w=j.length;for(let C=0;C<w;C++)o[j[C]]=v}else b?this.options.visualElement.renderState.vars[p]=v:o[p]=v}this.options.layoutId&&(o.pointerEvents=c===this?Rt(l==null?void 0:l.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var l;return(l=o.currentAnimation)==null?void 0:l.stop()}),this.root.nodes.forEach($i),this.root.sharedNodes.clear()}}}function Wd(e){e.updateLayout()}function Ud(e){var s;const t=((s=e.resumeFrom)==null?void 0:s.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:i}=e.layout,{animationType:a}=e.options,o=t.source!==e.layout.source;a==="size"?Q(h=>{const f=o?t.measuredBox[h]:t.layoutBox[h],m=_(f);f.min=n[h].min,f.max=f.min+m}):Wo(a,t.layoutBox,n)&&Q(h=>{const f=o?t.measuredBox[h]:t.layoutBox[h],m=_(n[h]);f.max=f.min+m,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[h].max=e.relativeTarget[h].min+m)});const l=ze();st(l,n,t.layoutBox);const d=ze();o?st(d,e.applyTransform(i,!0),t.measuredBox):st(d,n,t.layoutBox);const c=!Fo(l);let u=!1;if(!e.resumeFrom){const h=e.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:m}=h;if(f&&m){const p=F();nt(p,t.layoutBox,f.layoutBox);const y=F();nt(y,n,m.layoutBox),zo(p,y)||(u=!0),h.options.layoutRoot&&(e.relativeTarget=y,e.relativeTargetOrigin=p,e.relativeParent=h)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:d,layoutDelta:l,hasLayoutChanged:c,hasRelativeLayoutChanged:u})}else if(e.isLead()){const{onExitComplete:n}=e.options;n&&n()}e.options.transition=void 0}function Hd(e){e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Kd(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function _d(e){e.clearSnapshot()}function $i(e){e.clearMeasurements()}function Wi(e){e.isLayoutDirty=!1}function Gd(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Ui(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function qd(e){e.resolveTargetDelta()}function Xd(e){e.calcProjection()}function Yd(e){e.resetSkewAndRotation()}function Zd(e){e.removeLeadSnapshot()}function Hi(e,t,s){e.translate=B(t.translate,0,s),e.scale=B(t.scale,1,s),e.origin=t.origin,e.originPoint=t.originPoint}function Ki(e,t,s,n){e.min=B(t.min,s.min,n),e.max=B(t.max,s.max,n)}function Jd(e,t,s,n){Ki(e.x,t.x,s.x,n),Ki(e.y,t.y,s.y,n)}function Qd(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const eh={duration:.45,ease:[.4,0,.1,1]},_i=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),Gi=_i("applewebkit/")&&!_i("chrome/")?Math.round:ee;function qi(e){e.min=Gi(e.min),e.max=Gi(e.max)}function th(e){qi(e.x),qi(e.y)}function Wo(e,t,s){return e==="position"||e==="preserve-aspect"&&!ad(zi(t),zi(s),.2)}function sh(e){var t;return e!==e.root&&((t=e.scroll)==null?void 0:t.wasRoot)}const nh=$o({attachResizeListener:(e,t)=>pt(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rs={current:void 0},Uo=$o({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!rs.current){const e=new nh({});e.mount(window),e.setOptions({layoutScroll:!0}),rs.current=e}return rs.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),ih={pan:{Feature:jd},drag:{Feature:wd,ProjectionNode:Uo,MeasureLayout:Lo}};function Xi(e,t,s){const{props:n}=e;e.animationState&&n.whileHover&&e.animationState.setActive("whileHover",s==="Start");const i="onHover"+s,a=n[i];a&&E.postRender(()=>a(t,yt(t)))}class rh extends Se{mount(){const{current:t}=this.node;t&&(this.unmount=Pc(t,(s,n)=>(Xi(this.node,n,"Start"),i=>Xi(this.node,i,"End"))))}unmount(){}}class oh extends Se{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=mt(pt(this.node.current,"focus",()=>this.onFocus()),pt(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Yi(e,t,s){const{props:n}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&n.whileTap&&e.animationState.setActive("whileTap",s==="Start");const i="onTap"+(s==="End"?"":s),a=n[i];a&&E.postRender(()=>a(t,yt(t)))}class ah extends Se{mount(){const{current:t}=this.node;t&&(this.unmount=kc(t,(s,n)=>(Yi(this.node,n,"Start"),(i,{success:a})=>Yi(this.node,i,a?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Fs=new WeakMap,os=new WeakMap,lh=e=>{const t=Fs.get(e.target);t&&t(e)},ch=e=>{e.forEach(lh)};function uh({root:e,...t}){const s=e||document;os.has(s)||os.set(s,{});const n=os.get(s),i=JSON.stringify(t);return n[i]||(n[i]=new IntersectionObserver(ch,{root:e,...t})),n[i]}function dh(e,t,s){const n=uh(t);return Fs.set(e,s),n.observe(e),()=>{Fs.delete(e),n.unobserve(e)}}const hh={some:0,all:1};class fh extends Se{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:s,margin:n,amount:i="some",once:a}=t,o={root:s?s.current:void 0,rootMargin:n,threshold:typeof i=="number"?i:hh[i]},l=d=>{const{isIntersecting:c}=d;if(this.isInView===c||(this.isInView=c,a&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:u,onViewportLeave:h}=this.node.getProps(),f=c?u:h;f&&f(d)};return dh(this.node.current,o,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:s}=this.node;["amount","margin","root"].some(ph(t,s))&&this.startObserver()}unmount(){}}function ph({viewport:e={}},{viewport:t={}}={}){return s=>e[s]!==t[s]}const mh={inView:{Feature:fh},tap:{Feature:ah},focus:{Feature:oh},hover:{Feature:rh}},gh={layout:{ProjectionNode:Uo,MeasureLayout:Lo}},xh={...td,...mh,...ih,...gh},yh=gu(xh,Ru),{Title:Zi,Text:it}=te,xe=({title:e,content:t})=>t?r.jsx("div",{style:{marginBottom:"8px",paddingBottom:"8px",borderBottom:"1px solid rgba(255, 255, 255, 0.08)"},children:r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",gap:"8px"},children:[r.jsx(it,{className:"!text-gray-400",style:{fontSize:"11px",fontWeight:500,minWidth:"70px",lineHeight:"1.3",flexShrink:0},children:e}),r.jsx("div",{style:{flex:1,textAlign:"right",minWidth:0},children:typeof t=="string"&&t.length>20?r.jsx(Vt,{title:t,placement:"topLeft",children:r.jsx(it,{className:"!text-white",style:{fontSize:"12px",lineHeight:"1.3",display:"block",wordBreak:"break-all",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",maxWidth:"140px"},children:t})}):r.jsx(it,{className:"!text-white",style:{fontSize:"12px",lineHeight:"1.3",wordBreak:"break-all"},children:t})})]})}):null,vh=({node:e,onNodeExpand:t})=>{var s,n;return r.jsx(Fc,{children:e&&r.jsxs(yh.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},children:[r.jsxs(Zi,{level:5,style:{display:"flex",alignItems:"center",marginBottom:"20px"},className:"text-white",children:[e.type==="person"?r.jsx(aa,{className:"mr-2"}):r.jsx(la,{className:"mr-2"}),r.jsx(it,{ellipsis:{tooltip:e.name},className:"!text-white",children:e.name})]}),e.percentage&&r.jsx(q,{title:r.jsx(it,{className:"!text-gray-300",children:"穿透持股比例"}),value:e.percentage,suffix:"%",valueStyle:{color:"#fff",fontSize:"18px"}}),r.jsx(ot,{style:{background:"rgba(255,255,255,0.2)"}}),t&&(((s=e.expandable)==null?void 0:s.up)||((n=e.expandable)==null?void 0:n.down))&&r.jsxs(r.Fragment,{children:[r.jsx(Zi,{level:5,className:"!text-white mb-3",children:"节点操作"}),r.jsxs(at,{className:"w-full",children:[e.expandable.up&&r.jsx(V,{icon:r.jsx(ca,{}),onClick:()=>t(e.id,"up"),className:"w-full",children:"向上展开"}),e.expandable.down&&r.jsx(V,{icon:r.jsx(ua,{}),onClick:()=>t(e.id,"down"),className:"w-full",children:"向下展开"})]}),r.jsx(ot,{style:{background:"rgba(255,255,255,0.2)"}})]}),r.jsx(xe,{title:"法定代表人",content:e.法定代表人}),r.jsx(xe,{title:"注册资本",content:e.注册资本}),r.jsx(xe,{title:"成立日期",content:e.成立日期}),r.jsx(xe,{title:"天眼评分",content:e.天眼评分}),r.jsx(xe,{title:"行业",content:e.行业}),r.jsx(xe,{title:"组织机构代码",content:e.组织机构代码}),r.jsx(xe,{title:"经营范围",content:e.经营范围}),r.jsx(xe,{title:"标签",content:e.标签})]},e.id)})},Tn={equityPenetration:async e=>{const t=await X.post("/financial/equity-penetration",{},{params:e});return t&&t.graph_data?{success:!0,data:t}:t},expandNode:async(e,t)=>({success:!0,data:await X.get(`/financial/expand-node/${e}`,{params:{direction:t}})}),searchCompanies:async e=>{const t=await X.get("/financial/search-companies",{params:{keyword:e}});return t&&t.suggestions?{success:!0,data:t}:t},equityPledge:async e=>X.post("/financial/equity-pledge",{company_id:e}),bondAnalysis:async e=>X.post("/financial/bond-analysis",e),getCompanyInfo:async e=>X.get(`/financial/company/${e}/info`),getCompanyShareholders:async e=>X.get(`/financial/company/${e}/shareholders`),getCompanyInvestments:async e=>X.get(`/financial/company/${e}/investments`),getBondMarketOverview:async()=>X.get("/financial/bond/market-overview"),getBondRecommendations:async e=>X.get("/financial/bond/recommendations",{params:e})};function bh(e,t){const[s,n]=x.useState(e);return x.useEffect(()=>{const i=setTimeout(()=>{n(e)},t);return()=>{clearTimeout(i)}},[e,t]),s}const wh=Os.lazy(()=>dr(()=>import("./AdvancedEquityGraph-B6dIjYW0.js"),__vite__mapDeps([0,1,2,3]))),jh=Os.lazy(()=>dr(()=>import("./RealtimeCrawlModal-CdWpPaus.js"),__vite__mapDeps([4,1,2]))),{Sider:Ji,Content:Sh}=K,{Title:St,Text:Tt}=te,Th=async e=>{try{return await Tn.equityPenetration({company_id:e,up_depth:2,down_depth:2})}catch(t){return console.error("Failed to fetch initial equity data:",t),k.error("获取初始股权数据失败，请检查后端服务"),null}},Ch=async e=>{var t;if(!e)return[];try{const s=await Tn.searchCompanies(e);if(s!=null&&s.success&&((t=s.data)!=null&&t.suggestions)){const n=new Set;return s.data.suggestions.map(a=>((a==null?void 0:a.name)??"").toString().trim()).filter(a=>!!a).filter(a=>n.has(a)?!1:(n.add(a),!0)).map(a=>({value:a}))}return[]}catch(s){return console.error("Failed to search companies:",s),[]}},Ph=()=>{const[e,t]=x.useState(!0),[s,n]=x.useState({nodes:[],edges:[]}),[i,a]=x.useState(null),[o,l]=x.useState(0),[d,c]=x.useState(0),[u,h]=x.useState("北京海开控股（集团）股份有限公司"),[f,m]=x.useState(""),[p,y]=x.useState([]),[j,b]=x.useState(0),[v,w]=x.useState(!0),[C,T]=x.useState(!0),[A,g]=x.useState(new Set),[S,R]=x.useState(!1),[D,O]=x.useState(""),Z=bh(f,300),ge=x.useCallback(async M=>{var L;if(!M.trim()){k.warning("请输入公司名称");return}t(!0),a(null),n({nodes:[],edges:[]});try{const I=await Th(M);if(I!=null&&I.success&&((L=I.data)!=null&&L.graph_data)&&I.data.graph_data.nodes.length>0){if(n(I.data.graph_data),l(I.data.graph_data.nodes.length),c(I.data.graph_data.nodes.length),I.data.graph_data.nodes.length>0){const ne=I.data.graph_data.nodes.find(oe=>oe.name===M||oe.企业名称===M)||I.data.graph_data.nodes[0],re=ne.name||ne.企业名称;re&&re!==f&&m(re)}k.success(`成功获取 ${M} 的初始股权结构`)}else n({nodes:[],edges:[]}),l(0),c(0),Cn(M)}catch{k.error("数据加载失败")}finally{t(!1)}},[]);x.useEffect(()=>{ge(u)},[u]),x.useEffect(()=>{(async()=>{if(Z){const L=await Ch(Z);y(L)}else y([])})()},[Z]);const he=M=>{m(M)},_e=M=>{h(M),m(M)},Cn=M=>{cr.confirm({title:"未找到股权数据",icon:r.jsx(da,{}),content:r.jsxs("div",{children:[r.jsxs("p",{children:["数据库中未找到 ",r.jsx("strong",{children:M})," 的股权关系数据。"]}),r.jsxs("p",{children:["是否启动",r.jsx("strong",{children:"实时股权穿透分析"}),"？该过程将："]}),r.jsxs("ul",{style:{marginLeft:20,marginTop:8},children:[r.jsx("li",{children:"自动搜索并分析该公司的股权结构"}),r.jsx("li",{children:"向上和向下穿透2层关系"}),r.jsx("li",{children:"预计需要2-5分钟时间"}),r.jsx("li",{children:"分析完成后自动显示结果"})]})]}),okText:"开始实时分析",cancelText:"取消",onOk:()=>{O(M),R(!0)},onCancel:()=>{k.info("已取消实时分析")}})},$=x.useCallback(M=>{if(M&&M.nodes&&M.nodes.length>0){const L={nodes:M.nodes,edges:M.edges};n(L),l(M.nodes.length),c(M.nodes.length),k.success(`🎉 实时分析完成！发现 ${M.nodes.length} 个企业节点，${M.edges.length} 个关系`)}else k.warning("实时分析完成，但未发现有效的股权关系数据");R(!1),O("")},[]),se=x.useCallback(()=>{R(!1),O(""),k.info("已取消实时分析")},[]),Ge=x.useCallback(()=>{R(!1),O("")},[]),qe=x.useCallback(M=>{c(M)},[]),Pn=x.useMemo(()=>{const M=new Map;return s.nodes.forEach(L=>{var ne;const I=L.标签||((ne=L.properties)==null?void 0:ne.标签);I&&typeof I=="string"&&I.split("|").filter(oe=>oe.trim()).forEach(oe=>{const Te=oe.trim();Te&&M.set(Te,(M.get(Te)||0)+1)})}),Array.from(M.entries()).map(([L,I])=>({tag:L,count:I})).sort((L,I)=>I.count-L.count)},[s.nodes]),Ho=x.useCallback(M=>{const L=new Set(A);L.has(M)?L.delete(M):L.add(M),g(L)},[A]),Ko=x.useCallback(M=>{a(M)},[]),An=x.useCallback(async(M,L)=>{t(!0);try{const ne=(await Tn.expandNode(M,L)).data;ne&&ne.nodes&&(n(re=>{const oe=new Set(re.nodes.map(Wt=>Wt.id)),Te=ne.nodes.filter(Wt=>!oe.has(Wt.id));return{nodes:[...re.nodes,...Te],edges:[...re.edges,...ne.edges]}}),l(re=>re+ne.nodes.filter(oe=>!new Set(s.nodes.map(Te=>Te.id)).has(oe.id)).length),k.success("节点已展开"))}catch{k.error("展开节点失败")}finally{t(!1)}},[s.nodes]);return r.jsxs("div",{style:{width:"100%",height:"100%",minHeight:"600px",position:"relative"},children:[r.jsxs(K,{style:{background:"transparent",padding:"16px",height:"100%",minHeight:"600px"},children:[r.jsx(Ji,{width:280,style:{background:"transparent",marginRight:"16px"},children:r.jsxs(G,{title:r.jsx(St,{level:5,className:"!text-white",children:"控制面板"}),children:[r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"16px"},children:[r.jsx(q,{title:r.jsx(Tt,{className:"!text-gray-300",children:"节点总数"}),value:o,prefix:r.jsx(Dn,{}),valueStyle:{color:"#fff",fontSize:"18px"}}),r.jsx(q,{title:r.jsx(Tt,{className:"!text-gray-300",children:"可见节点"}),value:d,prefix:r.jsx(Dn,{}),valueStyle:{color:j>0?"#10b981":"#fff",fontSize:"18px"}})]}),r.jsx(St,{level:5,className:"!text-white",children:"关系过滤"}),r.jsx("div",{style:{marginBottom:"16px"},children:r.jsxs(at,{children:[r.jsx(V,{type:v?"primary":"default",size:"small",onClick:()=>w(!v),style:{backgroundColor:v?"#1677ff":"transparent",borderColor:v?"#1677ff":"rgba(255,255,255,0.3)"},children:"股东关系"}),r.jsx(V,{type:C?"primary":"default",size:"small",onClick:()=>T(!C),style:{backgroundColor:C?"#1677ff":"transparent",borderColor:C?"#1677ff":"rgba(255,255,255,0.3)"},children:"对外投资"})]})}),r.jsxs("div",{style:{marginBottom:"20px"},children:[r.jsxs(Tt,{className:"!text-gray-300",style:{fontSize:"12px"},children:["显示股权比例大于 ",j,"% 的关系"]}),r.jsx(ha,{min:0,max:100,value:j,onChange:b,tooltip:{formatter:M=>`${M}%`},style:{marginTop:"8px"}})]}),r.jsx(ot,{style:{background:"rgba(255,255,255,0.2)"}}),r.jsx("div",{style:{marginBottom:"16px",maxHeight:"200px",overflowY:"auto",paddingTop:"8px"},children:Pn.length>0?r.jsx(at,{size:[4,4],wrap:!0,children:Pn.map(({tag:M,count:L})=>r.jsx(fa,{color:A.has(M)?"blue":"default",style:{cursor:"pointer",margin:"2px",fontSize:"11px"},onClick:()=>Ho(M),children:r.jsx(ys,{count:L,size:"small",offset:[8,-8],children:M})},M))}):r.jsx(Tt,{className:"!text-gray-400",style:{fontSize:"12px"},children:"暂无标签数据"})}),r.jsx(ot,{style:{background:"rgba(255,255,255,0.2)"}}),r.jsx(St,{level:5,className:"!text-white",children:"高级分析"}),r.jsx(Vt,{title:"即将推出",children:r.jsx(V,{icon:r.jsx(pa,{}),style:{width:"100%",marginBottom:"12px"},disabled:!0,children:"社区发现"})}),r.jsx(Vt,{title:"即将推出",children:r.jsx(V,{icon:r.jsx(ma,{}),style:{width:"100%"},disabled:!0,children:"中心性分析"})})]})}),r.jsx(Sh,{style:{position:"relative",height:"100%",display:"flex",flexDirection:"column"},children:r.jsxs(G,{style:{padding:0,height:"100%",display:"flex",flexDirection:"column"},bodyStyle:{padding:0,height:"100%",display:"flex",flexDirection:"column"},children:[e&&r.jsx("div",{className:"absolute inset-0 z-20 flex items-center justify-center bg-slate-900 bg-opacity-60 backdrop-blur-sm",children:r.jsx(Vn,{size:"large",tip:r.jsx("span",{className:"text-white text-lg",children:"正在查询股权穿透信息..."})})}),s.nodes.length>0?r.jsx("div",{style:{flex:1,height:"100%"},children:r.jsx(x.Suspense,{fallback:r.jsx("div",{className:"absolute inset-0 z-20 flex items-center justify-center bg-slate-900 bg-opacity-60 backdrop-blur-sm",children:r.jsx(Vn,{size:"large",tip:r.jsx("span",{className:"text-white text-lg",children:"正在加载图谱组件..."})})}),children:r.jsx(wh,{data:s,onNodeClick:Ko,onNodeExpand:An,height:"100%",searchProps:{value:f,options:p,onSearch:he,onSelect:_e,loading:e},equityThreshold:j,relationshipFilter:{showShareholders:v,showInvestments:C},selectedTags:A,onVisibleNodesChange:qe})})}):!e&&r.jsx(ga,{description:"无数据显示，请输入公司名称进行查询",className:"h-full flex flex-col justify-center items-center",imageStyle:{height:100}})]})}),r.jsx(Ji,{width:280,style:{background:"transparent",marginLeft:"16px"},children:r.jsx(G,{title:r.jsx(St,{level:5,className:"!text-white",children:"节点信息"}),children:i?r.jsx(vh,{node:i,onNodeExpand:An}):r.jsxs("div",{className:"text-center text-gray-400 mt-10",children:[r.jsx(xa,{className:"text-3xl mb-4"}),r.jsx("p",{children:"点击任意节点查看详细信息"})]})})})]}),r.jsx(x.Suspense,{fallback:null,children:r.jsx(jh,{visible:S,companyName:D,onComplete:$,onCancel:se,onClose:Ge})})]})},{Sider:Ah,Content:Mh}=K,{Title:as,Text:Qi,Paragraph:Rh}=te,kh=()=>{const[e,t]=x.useState(!1),s=()=>{t(!0),k.info("股权质押分析功能正在开发中..."),setTimeout(()=>t(!1),2e3)};return r.jsx("div",{style:{width:"100%",height:"100%",minHeight:"600px",position:"relative"},children:r.jsxs(K,{style:{background:"transparent",padding:"16px",height:"100%",minHeight:"600px"},children:[r.jsx(Ah,{width:280,style:{background:"transparent",marginRight:"16px"},children:r.jsxs(G,{title:r.jsx(as,{level:5,className:"!text-white",children:"控制面板"}),children:[r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"16px"},children:[r.jsx(q,{title:r.jsx(Qi,{className:"!text-gray-300",children:"质押记录"}),value:0,prefix:r.jsx(rt,{size:16}),valueStyle:{color:"#fff",fontSize:"18px"}}),r.jsx(q,{title:r.jsx(Qi,{className:"!text-gray-300",children:"风险评级"}),value:0,prefix:r.jsx(or,{size:16}),valueStyle:{color:"#fff",fontSize:"18px"}})]}),r.jsx(as,{level:5,className:"!text-white",style:{marginTop:"24px"},children:"快速操作"}),r.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[r.jsx(V,{type:"primary",icon:r.jsx(Oe,{size:16}),onClick:s,loading:e,style:{width:"100%"},children:"开始分析"}),r.jsx(V,{icon:r.jsx(Zo,{size:16}),style:{width:"100%"},disabled:!0,children:"导入数据"})]})]})}),r.jsx(Mh,{style:{position:"relative"},children:r.jsx(G,{style:{padding:0,height:"100%"},children:r.jsxs("div",{style:{padding:"48px",height:"100%",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",textAlign:"center"},children:[r.jsx(rt,{size:64,style:{color:"#22d3ee",marginBottom:"24px"}}),r.jsx(as,{level:3,className:"!text-white",style:{marginBottom:"16px"},children:"股权质押分析工具"}),r.jsx(Rh,{style:{color:"rgba(255, 255, 255, 0.7)",fontSize:"16px",maxWidth:"400px",lineHeight:1.6},children:"专业的股权质押情况分析和风险评估工具。 该功能正在开发中，敬请期待。"})]})})})]})})},{Sider:Dh,Content:Vh}=K,{Title:ls,Text:Be,Paragraph:Eh}=te,Bh=()=>{const[e,t]=x.useState(!1),s=()=>{t(!0),k.info("债券分析功能正在开发中..."),setTimeout(()=>t(!1),2e3)};return r.jsx("div",{style:{width:"100%",height:"100%",minHeight:"600px",position:"relative"},children:r.jsxs(K,{style:{background:"transparent",padding:"16px",height:"100%",minHeight:"600px"},children:[r.jsx(Dh,{width:280,style:{background:"transparent",marginRight:"16px"},children:r.jsxs(G,{title:r.jsx(ls,{level:5,className:"!text-white",children:"控制面板"}),children:[r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"16px"},children:[r.jsx(q,{title:r.jsx(Be,{className:"!text-gray-300",children:"债券数量"}),value:0,prefix:r.jsx(Je,{size:16}),valueStyle:{color:"#fff",fontSize:"18px"}}),r.jsx(q,{title:r.jsx(Be,{className:"!text-gray-300",children:"分析结果"}),value:0,prefix:r.jsx(Oe,{size:16}),valueStyle:{color:"#fff",fontSize:"18px"}})]}),r.jsx(ls,{level:5,className:"!text-white",style:{marginTop:"24px"},children:"快速操作"}),r.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[r.jsx(V,{type:"primary",icon:r.jsx(Je,{size:16}),onClick:s,loading:e,style:{width:"100%"},children:"开始分析"}),r.jsx(V,{icon:r.jsx(ir,{size:16}),style:{width:"100%"},disabled:!0,children:"导入数据"})]})]})}),r.jsx(Vh,{style:{position:"relative"},children:r.jsx(G,{style:{padding:0,height:"100%"},children:r.jsxs("div",{style:{padding:"48px",height:"100%",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",textAlign:"center"},children:[r.jsx(Je,{size:64,style:{color:"#22d3ee",marginBottom:"24px"}}),r.jsx(ls,{level:3,className:"!text-white",style:{marginBottom:"16px"},children:"债券市场分析工具"}),r.jsx(Eh,{style:{color:"rgba(255, 255, 255, 0.7)",fontSize:"16px",maxWidth:"400px",lineHeight:1.6},children:"专业的债券市场数据分析工具，提供投资建议和风险评估。 该功能正在开发中，敬请期待。"}),r.jsxs("div",{style:{display:"flex",gap:"24px",marginTop:"32px",flexWrap:"wrap",justifyContent:"center"},children:[r.jsxs("div",{style:{background:"rgba(255, 255, 255, 0.05)",border:"1px solid rgba(255, 255, 255, 0.1)",borderRadius:"12px",padding:"20px",width:"160px",textAlign:"center"},children:[r.jsx(Oe,{size:32,style:{color:"#10b981",marginBottom:"12px"}}),r.jsx(Be,{className:"!text-white",style:{display:"block",fontWeight:600},children:"收益率分析"}),r.jsx(Be,{style:{color:"rgba(255, 255, 255, 0.6)",fontSize:"12px"},children:"即将推出"})]}),r.jsxs("div",{style:{background:"rgba(255, 255, 255, 0.05)",border:"1px solid rgba(255, 255, 255, 0.1)",borderRadius:"12px",padding:"20px",width:"160px",textAlign:"center"},children:[r.jsx(or,{size:32,style:{color:"#f97316",marginBottom:"12px"}}),r.jsx(Be,{className:"!text-white",style:{display:"block",fontWeight:600},children:"风险评估"}),r.jsx(Be,{style:{color:"rgba(255, 255, 255, 0.6)",fontSize:"12px"},children:"即将推出"})]})]})]})})})]})})},{Sider:Lh,Content:Ih}=K,{Title:cs,Text:er,Paragraph:Nh}=te,Fh=()=>{const[e,t]=x.useState(!1),s=()=>{t(!0),k.info("批量OCR功能正在开发中..."),setTimeout(()=>t(!1),2e3)};return r.jsx("div",{style:{width:"100%",height:"100%",minHeight:"600px",position:"relative"},children:r.jsxs(K,{style:{background:"transparent",padding:"16px",height:"100%",minHeight:"600px"},children:[r.jsx(Lh,{width:280,style:{background:"transparent",marginRight:"16px"},children:r.jsxs(G,{title:r.jsx(cs,{level:5,className:"!text-white",children:"控制面板"}),children:[r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"16px"},children:[r.jsx(q,{title:r.jsx(er,{className:"!text-gray-300",children:"处理文件"}),value:0,prefix:r.jsx(Dt,{size:16}),valueStyle:{color:"#fff",fontSize:"18px"}}),r.jsx(q,{title:r.jsx(er,{className:"!text-gray-300",children:"识别结果"}),value:0,prefix:r.jsx(Pt,{size:16}),valueStyle:{color:"#fff",fontSize:"18px"}})]}),r.jsx(cs,{level:5,className:"!text-white",style:{marginTop:"24px"},children:"快速操作"}),r.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[r.jsx(V,{type:"primary",icon:r.jsx($s,{size:16}),onClick:s,loading:e,style:{width:"100%"},children:"上传处理"}),r.jsx(V,{icon:r.jsx(ar,{size:16}),style:{width:"100%"},disabled:!0,children:"选择文件"})]})]})}),r.jsx(Ih,{style:{position:"relative"},children:r.jsx(G,{style:{padding:0,height:"100%"},children:r.jsxs("div",{style:{padding:"48px",height:"100%",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",textAlign:"center"},children:[r.jsx(Dt,{size:64,style:{color:"#22d3ee",marginBottom:"24px"}}),r.jsx(cs,{level:3,className:"!text-white",style:{marginBottom:"16px"},children:"批量OCR识别工具"}),r.jsx(Nh,{style:{color:"rgba(255, 255, 255, 0.7)",fontSize:"16px",maxWidth:"400px",lineHeight:1.6},children:"智能批量识别图片和PDF中的文字内容。 该功能正在开发中，敬请期待。"})]})})})]})})},{Sider:zh,Content:Oh}=K,{Title:us,Text:tr,Paragraph:$h}=te,Wh=()=>{const[e,t]=x.useState(!1),s=()=>{t(!0),k.info("批量加水印功能正在开发中..."),setTimeout(()=>t(!1),2e3)};return r.jsx("div",{style:{width:"100%",height:"100%",minHeight:"600px",position:"relative"},children:r.jsxs(K,{style:{background:"transparent",padding:"16px",height:"100%",minHeight:"600px"},children:[r.jsx(zh,{width:280,style:{background:"transparent",marginRight:"16px"},children:r.jsxs(G,{title:r.jsx(us,{level:5,className:"!text-white",children:"控制面板"}),children:[r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"16px"},children:[r.jsx(q,{title:r.jsx(tr,{className:"!text-gray-300",children:"处理文件"}),value:0,prefix:r.jsx(ar,{size:16}),valueStyle:{color:"#fff",fontSize:"18px"}}),r.jsx(q,{title:r.jsx(tr,{className:"!text-gray-300",children:"完成数量"}),value:0,prefix:r.jsx(ps,{size:16}),valueStyle:{color:"#fff",fontSize:"18px"}})]}),r.jsx(us,{level:5,className:"!text-white",style:{marginTop:"24px"},children:"快速操作"}),r.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[r.jsx(V,{type:"primary",icon:r.jsx($s,{size:16}),onClick:s,loading:e,style:{width:"100%"},children:"开始处理"}),r.jsx(V,{icon:r.jsx(lr,{size:16}),style:{width:"100%"},disabled:!0,children:"水印设置"})]})]})}),r.jsx(Oh,{style:{position:"relative"},children:r.jsx(G,{style:{padding:0,height:"100%"},children:r.jsxs("div",{style:{padding:"48px",height:"100%",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",textAlign:"center"},children:[r.jsx(ps,{size:64,style:{color:"#22d3ee",marginBottom:"24px"}}),r.jsx(us,{level:3,className:"!text-white",style:{marginBottom:"16px"},children:"批量加水印工具"}),r.jsx($h,{style:{color:"rgba(255, 255, 255, 0.7)",fontSize:"16px",maxWidth:"400px",lineHeight:1.6},children:"为图片和文档批量添加水印，保护您的知识产权。 该功能正在开发中，敬请期待。"})]})})})]})})},{Sider:Uh,Content:Hh}=K,{Title:ds,Text:sr,Paragraph:Kh}=te,_h=()=>{const[e,t]=x.useState(!1),s=()=>{t(!0),k.info("Excel合并功能正在开发中..."),setTimeout(()=>t(!1),2e3)};return r.jsx("div",{style:{width:"100%",height:"100%",minHeight:"600px",position:"relative"},children:r.jsxs(K,{style:{background:"transparent",padding:"16px",height:"100%",minHeight:"600px"},children:[r.jsx(Uh,{width:280,style:{background:"transparent",marginRight:"16px"},children:r.jsxs(G,{title:r.jsx(ds,{level:5,className:"!text-white",children:"控制面板"}),children:[r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"16px"},children:[r.jsx(q,{title:r.jsx(sr,{className:"!text-gray-300",children:"待合并文件"}),value:0,prefix:r.jsx(ms,{size:16}),valueStyle:{color:"#fff",fontSize:"18px"}}),r.jsx(q,{title:r.jsx(sr,{className:"!text-gray-300",children:"合并结果"}),value:0,prefix:r.jsx(Jo,{size:16}),valueStyle:{color:"#fff",fontSize:"18px"}})]}),r.jsx(ds,{level:5,className:"!text-white",style:{marginTop:"24px"},children:"快速操作"}),r.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[r.jsx(V,{type:"primary",icon:r.jsx($s,{size:16}),onClick:s,loading:e,style:{width:"100%"},children:"开始合并"}),r.jsx(V,{icon:r.jsx(Qo,{size:16}),style:{width:"100%"},disabled:!0,children:"下载结果"})]})]})}),r.jsx(Hh,{style:{position:"relative"},children:r.jsx(G,{style:{padding:0,height:"100%"},children:r.jsxs("div",{style:{padding:"48px",height:"100%",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",textAlign:"center"},children:[r.jsx(ms,{size:64,style:{color:"#22d3ee",marginBottom:"24px"}}),r.jsx(ds,{level:3,className:"!text-white",style:{marginBottom:"16px"},children:"Excel暴力合并工具"}),r.jsx(Kh,{style:{color:"rgba(255, 255, 255, 0.7)",fontSize:"16px",maxWidth:"400px",lineHeight:1.6},children:"智能合并多个Excel文件和工作表，提高工作效率。 该功能正在开发中，敬请期待。"})]})})})]})})},Gh=({open:e,onClose:t})=>{var d;const[s]=U.useForm(),[n,i]=x.useState(!1),{user:a}=me(),o=async c=>{i(!0);try{if(c.username&&c.username!==(a==null?void 0:a.username)){const u=await X.post("/user/update-profile",{username:c.username});if(u.success)k.success("用户名更新成功！");else{k.error(u.message||"用户名更新失败");return}}if(c.currentPassword&&c.newPassword){if(c.newPassword!==c.confirmPassword){k.error("两次输入的新密码不一致！");return}const u=await X.post("/user/change-password",{oldPassword:c.currentPassword,newPassword:c.newPassword});if(u.success)k.success("密码更新成功！"),s.resetFields(["currentPassword","newPassword","confirmPassword"]);else{k.error(u.message||"密码更新失败，请检查当前密码是否正确");return}}!c.currentPassword&&!c.newPassword&&c.username===(a==null?void 0:a.username)&&k.info("没有检测到任何更改")}catch{k.error("保存失败，请检查网络连接")}finally{i(!1)}},l=()=>{s.resetFields(),t()};return r.jsxs(ya,{title:"用户设置",width:420,open:e,onClose:l,bodyStyle:{padding:"24px"},extra:r.jsx(V,{type:"primary",onClick:()=>s.submit(),loading:n,icon:r.jsx(ea,{size:16}),children:"保存更改"}),children:[r.jsxs("div",{className:"user-settings-panel",children:[r.jsx("div",{className:"user-profile-section",children:r.jsxs(at,{direction:"vertical",align:"center",style:{width:"100%",marginBottom:"32px"},children:[r.jsx(ur,{size:80,src:a==null?void 0:a.avatar,className:"user-avatar-large",children:(d=a==null?void 0:a.username)==null?void 0:d.charAt(0).toUpperCase()}),r.jsxs("div",{style:{textAlign:"center"},children:[r.jsx("div",{className:"user-current-name",children:a==null?void 0:a.username}),r.jsx("div",{className:"user-role-badge",children:a==null?void 0:a.role})]})]})}),r.jsxs(U,{form:s,layout:"vertical",onFinish:o,initialValues:{username:a==null?void 0:a.username},requiredMark:!1,children:[r.jsxs("div",{className:"settings-section",children:[r.jsx("h4",{className:"section-title",children:"基本信息"}),r.jsx(U.Item,{name:"username",label:"用户名",rules:[{required:!0,message:"请输入用户名"},{min:3,message:"用户名至少3个字符"},{max:20,message:"用户名最多20个字符"}],children:r.jsx(le,{prefix:r.jsx(nr,{size:16}),placeholder:"请输入新的用户名",className:"settings-input"})})]}),r.jsx(ot,{}),r.jsxs("div",{className:"settings-section",children:[r.jsx("h4",{className:"section-title",children:"密码设置"}),r.jsx(U.Item,{name:"currentPassword",label:"当前密码",rules:[({getFieldValue:c})=>({validator(u,h){return c("newPassword")&&!h?Promise.reject("要修改密码，请先输入当前密码"):Promise.resolve()}})],children:r.jsx(le.Password,{prefix:r.jsx(ke,{size:16}),placeholder:"请输入当前密码",className:"settings-input",iconRender:c=>c?r.jsx(Pt,{size:16}):r.jsx(Ut,{size:16})})}),r.jsx(U.Item,{name:"newPassword",label:"新密码",rules:[({getFieldValue:c})=>({validator(u,h){return c("currentPassword")&&!h?Promise.reject("请输入新密码"):h&&h.length<6?Promise.reject("新密码至少6个字符"):Promise.resolve()}})],children:r.jsx(le.Password,{prefix:r.jsx(ke,{size:16}),placeholder:"请输入新密码",className:"settings-input",iconRender:c=>c?r.jsx(Pt,{size:16}):r.jsx(Ut,{size:16})})}),r.jsx(U.Item,{name:"confirmPassword",label:"确认新密码",dependencies:["newPassword"],rules:[({getFieldValue:c})=>({validator(u,h){const f=c("newPassword");return f&&!h?Promise.reject("请确认新密码"):f&&h&&f!==h?Promise.reject("两次输入的密码不一致"):Promise.resolve()}})],children:r.jsx(le.Password,{prefix:r.jsx(ke,{size:16}),placeholder:"请再次输入新密码",className:"settings-input",iconRender:c=>c?r.jsx(Pt,{size:16}):r.jsx(Ut,{size:16})})})]})]})]}),r.jsx("style",{children:`
        .user-profile-section {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          margin: -24px -24px 32px -24px;
          padding: 32px 24px;
          border-radius: 0 0 16px 16px;
        }

        .user-avatar-large {
          border: 4px solid rgba(255, 255, 255, 0.3);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

          .user-current-name {
            font-size: 18px;
            font-weight: 600;
            color: white;
            margin-bottom: 4px;
          }
          
          .user-role-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            backdrop-filter: blur(10px);
          }
          
          .settings-section {
            margin-bottom: 24px;
          }
          
          .section-title {
            color: #1f2937;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
          }
          
          .settings-input {
            border-radius: 8px;
            height: 40px;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
          }
          
          .settings-input:hover,
          .settings-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
        }
      `})]})},{Header:qh,Content:Xh,Footer:Yh}=K,{Title:Zh,Text:hs}=te,ye=({children:e,pageTitle:t})=>{var u;const{user:s,logout:n}=me(),i=rr(),[a,o]=x.useState(new Date),[l,d]=x.useState(!1);x.useEffect(()=>{const h=setInterval(()=>{o(new Date)},1e3);return()=>clearInterval(h)},[]);const c=[{key:"dashboard",label:r.jsxs("div",{className:"menu-item",children:[r.jsx(ta,{size:16}),r.jsx("span",{children:"控制台"})]}),onClick:()=>i("/dashboard")},{key:"settings",label:r.jsxs("div",{className:"menu-item",children:[r.jsx(lr,{size:16}),r.jsx("span",{children:"用户设置"})]}),onClick:()=>d(!0)},{type:"divider"},{key:"logout",label:r.jsxs("div",{className:"menu-item logout",children:[r.jsx(sa,{size:16}),r.jsx("span",{children:"退出登录"})]}),onClick:n}];return r.jsxs("div",{className:"app-layout",children:[r.jsx(Ws,{}),r.jsxs(K,{className:"app-layout-content",children:[r.jsxs(qh,{className:"app-header",children:[r.jsxs("div",{className:"header-left",children:[r.jsxs("div",{className:"brand-container",children:[r.jsxs("div",{className:"logo-container",children:[r.jsx(Ct,{size:24}),r.jsx("div",{className:"logo-sparkle",children:r.jsx(zs,{size:8})})]}),r.jsxs("div",{className:"brand-info",children:[r.jsx(Zh,{level:4,className:"brand-title",children:"IDEALAB"}),r.jsx(hs,{className:"brand-subtitle",children:"专业数据实验室"})]})]}),t&&r.jsxs("div",{className:"breadcrumb",children:[r.jsx(Rn,{size:14,className:"breadcrumb-icon"}),r.jsx("span",{className:"breadcrumb-text",children:t})]})]}),r.jsxs(at,{align:"center",size:"large",children:[r.jsxs("div",{className:"time-display",children:[r.jsx("div",{className:"time-text",children:a.toLocaleTimeString()}),r.jsx("div",{className:"date-text",children:a.toLocaleDateString("zh-CN",{month:"short",day:"numeric"})})]}),r.jsx(va,{menu:{items:c},trigger:["click"],placement:"bottomRight",children:r.jsxs("div",{className:"user-dropdown",children:[r.jsx(ur,{size:36,src:s==null?void 0:s.avatar,className:"user-avatar",children:(u=s==null?void 0:s.username)==null?void 0:u.charAt(0).toUpperCase()}),r.jsxs("div",{className:"user-info",children:[r.jsx(hs,{className:"user-name",children:s==null?void 0:s.username}),r.jsxs("div",{className:"user-role",children:[r.jsx(rt,{size:10}),r.jsx(hs,{className:"role-text",children:s==null?void 0:s.role})]})]}),r.jsx(Rn,{size:14,className:"dropdown-icon"})]})})]})]}),r.jsx(Xh,{className:"app-content",children:e}),r.jsx(Yh,{className:"app-footer",children:r.jsxs("div",{className:"footer-content",children:[r.jsxs("div",{className:"footer-item",children:[r.jsx(Ct,{size:14}),r.jsx("span",{children:"IDEALAB Professional v2.0"})]}),r.jsxs("div",{className:"footer-item",children:[r.jsx("div",{className:"status-dot"}),r.jsx("span",{children:"运行状态正常"})]}),r.jsxs("div",{className:"footer-item",children:[r.jsx(Ct,{size:14}),r.jsx("span",{children:"在线工具: 8个"})]}),r.jsxs("div",{className:"footer-item",children:[r.jsx(rt,{size:14}),r.jsx("span",{children:"所有系统正常"})]})]})})]}),r.jsx(Gh,{open:l,onClose:()=>d(!1)})]})},Jh=()=>{const[e]=U.useForm(),[t,s]=x.useState(!1),{passwordChanged:n,logout:i}=me(),a=async o=>{if(o.newPassword!==o.confirmPassword){k.error("两次输入的新密码不一致!");return}s(!0);try{const l=await X.post("/user/change-password",{oldPassword:o.oldPassword,newPassword:o.newPassword});l.success?(k.success("密码修改成功！"),n()):k.error(l.message||"密码修改失败，请检查您的旧密码是否正确。")}catch{k.error("密码修改失败，发生网络错误。")}finally{s(!1)}};return r.jsxs(cr,{title:"首次登录 - 请修改密码",open:!0,closable:!1,maskClosable:!1,keyboard:!1,footer:[r.jsx(V,{onClick:i,children:"退出登录"},"logout"),r.jsx(V,{type:"primary",loading:t,onClick:()=>e.submit(),children:"确认修改"},"submit")],children:[r.jsx("p",{children:"为了您的账户安全，首次登录系统需要修改初始密码。"}),r.jsxs(U,{form:e,layout:"vertical",onFinish:a,requiredMark:!1,style:{marginTop:"24px"},children:[r.jsx(U.Item,{name:"oldPassword",label:"旧密码",rules:[{required:!0,message:"请输入您的当前密码"}],children:r.jsx(le.Password,{prefix:r.jsx(ke,{size:16}),placeholder:"请输入旧密码"})}),r.jsx(U.Item,{name:"newPassword",label:"新密码",rules:[{required:!0,message:"请输入新密码"},{min:6,message:"密码长度至少为6位"}],children:r.jsx(le.Password,{prefix:r.jsx(ke,{size:16}),placeholder:"请输入新密码"})}),r.jsx(U.Item,{name:"confirmPassword",label:"确认新密码",dependencies:["newPassword"],rules:[{required:!0,message:"请再次输入新密码"},({getFieldValue:o})=>({validator(l,d){return!d||o("newPassword")===d?Promise.resolve():Promise.reject(new Error("两次输入的密码不一致!"))}})],children:r.jsx(le.Password,{prefix:r.jsx(ke,{size:16}),placeholder:"请确认新密码"})})]})]})},ve=({children:e})=>{const{isAuthenticated:t}=me(s=>({isAuthenticated:s.isAuthenticated}));return t?r.jsx(r.Fragment,{children:e}):r.jsx(gs,{to:"/login",replace:!0})},Qh=()=>{const{isAuthenticated:e,checkAuth:t,passwordChangeRequired:s}=me(n=>({isAuthenticated:n.isAuthenticated,checkAuth:n.checkAuth,passwordChangeRequired:n.passwordChangeRequired}));return x.useEffect(()=>{t()},[t]),r.jsx(ba,{locale:ja,theme:{algorithm:wa.darkAlgorithm,token:{colorPrimary:"#2563eb",colorSuccess:"#10b981",colorWarning:"#f97316",colorError:"#dc2626",colorInfo:"#0891b2",borderRadius:8,borderRadiusLG:12,borderRadiusXS:4,fontFamily:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",fontSizeHeading1:32,fontSizeHeading2:24,fontSizeHeading3:20,fontSizeHeading4:18,fontSize:14,fontSizeLG:16,fontSizeSM:12,padding:16,paddingLG:24,paddingSM:12,paddingXS:8,margin:16,marginLG:24,marginSM:12,marginXS:8,boxShadow:"0 4px 16px rgba(0, 0, 0, 0.15)",boxShadowSecondary:"0 2px 8px rgba(0, 0, 0, 0.1)",colorBgContainer:"rgba(255, 255, 255, 0.08)",colorBgElevated:"rgba(255, 255, 255, 0.12)",colorBgLayout:"transparent",colorBgSpotlight:"rgba(255, 255, 255, 0.15)",colorText:"rgba(255, 255, 255, 0.95)",colorTextSecondary:"rgba(255, 255, 255, 0.75)",colorTextTertiary:"rgba(255, 255, 255, 0.55)",colorTextQuaternary:"rgba(255, 255, 255, 0.35)",colorBorder:"rgba(255, 255, 255, 0.15)",colorBorderSecondary:"rgba(255, 255, 255, 0.08)",colorFill:"rgba(255, 255, 255, 0.08)",colorFillSecondary:"rgba(255, 255, 255, 0.05)",colorFillTertiary:"rgba(255, 255, 255, 0.03)",colorFillQuaternary:"rgba(255, 255, 255, 0.02)"},components:{Card:{colorBgContainer:"rgba(255, 255, 255, 0.08)",colorBorder:"rgba(255, 255, 255, 0.15)",borderRadiusLG:12,paddingLG:24,colorTextHeading:"rgba(255, 255, 255, 0.95)"},Input:{colorBgContainer:"rgba(255, 255, 255, 0.05)",colorBorder:"rgba(255, 255, 255, 0.15)",colorText:"rgba(255, 255, 255, 0.95)",colorTextPlaceholder:"rgba(255, 255, 255, 0.55)",borderRadius:8,paddingInline:16,paddingBlock:10,activeBorderColor:"#2563eb",hoverBorderColor:"rgba(255, 255, 255, 0.25)"},Button:{borderRadius:8,paddingInline:24,paddingBlock:10,fontWeight:500,primaryColor:"#ffffff",colorPrimaryBg:"#2563eb",colorPrimaryBgHover:"#0891b2",colorPrimaryBorder:"#2563eb",colorPrimaryBorderHover:"#0891b2",defaultBg:"rgba(255, 255, 255, 0.05)",defaultBorderColor:"rgba(255, 255, 255, 0.15)",defaultColor:"rgba(255, 255, 255, 0.95)",defaultHoverBg:"rgba(255, 255, 255, 0.08)",defaultHoverBorderColor:"rgba(255, 255, 255, 0.25)",defaultHoverColor:"rgba(255, 255, 255, 1)"},Table:{colorBgContainer:"rgba(255, 255, 255, 0.03)",colorBorder:"rgba(255, 255, 255, 0.08)",borderRadius:12,colorText:"rgba(255, 255, 255, 0.95)",colorTextHeading:"rgba(255, 255, 255, 0.95)",headerBg:"rgba(37, 99, 235, 0.12)",headerColor:"rgba(255, 255, 255, 0.95)",rowHoverBg:"rgba(37, 99, 235, 0.08)",borderColor:"rgba(255, 255, 255, 0.08)"},Select:{colorBgContainer:"rgba(255, 255, 255, 0.05)",colorBorder:"rgba(255, 255, 255, 0.15)",colorText:"rgba(255, 255, 255, 0.95)",colorTextPlaceholder:"rgba(255, 255, 255, 0.55)",borderRadius:8,activeBorderColor:"#2563eb",hoverBorderColor:"rgba(255, 255, 255, 0.25)",optionSelectedBg:"rgba(37, 99, 235, 0.2)",optionActiveBg:"rgba(37, 99, 235, 0.1)"},Modal:{colorBgElevated:"rgba(15, 23, 42, 0.95)",colorBorder:"rgba(255, 255, 255, 0.15)",borderRadiusLG:12,headerBg:"rgba(37, 99, 235, 0.1)",titleColor:"rgba(255, 255, 255, 0.95)",contentBg:"transparent"},Message:{colorBgElevated:"rgba(15, 23, 42, 0.95)",colorBorder:"rgba(255, 255, 255, 0.15)",borderRadius:8,colorText:"rgba(255, 255, 255, 0.95)"},Pagination:{itemBg:"rgba(255, 255, 255, 0.05)",itemActiveBg:"#2563eb",itemLinkBg:"rgba(255, 255, 255, 0.05)",itemInputBg:"rgba(255, 255, 255, 0.05)",miniOptionsSizeChangerTop:0},Spin:{colorPrimary:"#2563eb",colorWhite:"#ffffff"},Layout:{bodyBg:"transparent",headerBg:"rgba(255, 255, 255, 0.08)",footerBg:"rgba(255, 255, 255, 0.05)",siderBg:"rgba(255, 255, 255, 0.08)"}}},children:r.jsxs(na,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:[e&&s&&r.jsx(Jh,{}),r.jsxs(ia,{children:[r.jsx(ae,{path:"/login",element:e?r.jsx(gs,{to:"/dashboard",replace:!0}):r.jsx(Aa,{})}),r.jsx(ae,{path:"/dashboard",element:r.jsx(ve,{children:r.jsx(ye,{pageTitle:"控制台",children:r.jsx(Da,{})})})}),r.jsx(ae,{path:"/tools/sentiment-analysis",element:r.jsx(ve,{children:r.jsx(ye,{pageTitle:"舆情分析",children:r.jsx(La,{})})})}),r.jsx(ae,{path:"/tools/equity-penetration",element:r.jsx(ve,{children:r.jsx(ye,{pageTitle:"股权穿透分析",children:r.jsx(Ph,{})})})}),r.jsx(ae,{path:"/tools/equity-pledge",element:r.jsx(ve,{children:r.jsx(ye,{pageTitle:"股权质押分析",children:r.jsx(kh,{})})})}),r.jsx(ae,{path:"/tools/bond-analysis",element:r.jsx(ve,{children:r.jsx(ye,{pageTitle:"债券市场分析",children:r.jsx(Bh,{})})})}),r.jsx(ae,{path:"/tools/batch-ocr",element:r.jsx(ve,{children:r.jsx(ye,{pageTitle:"批量OCR识别",children:r.jsx(Fh,{})})})}),r.jsx(ae,{path:"/tools/batch-watermark",element:r.jsx(ve,{children:r.jsx(ye,{pageTitle:"批量加水印",children:r.jsx(Wh,{})})})}),r.jsx(ae,{path:"/tools/excel-merger",element:r.jsx(ve,{children:r.jsx(ye,{pageTitle:"Excel合并",children:r.jsx(_h,{})})})}),r.jsx(ae,{path:"/",element:r.jsx(gs,{to:"/dashboard",replace:!0})})]})]})})};ra.createRoot(document.getElementById("root")).render(r.jsx(Os.StrictMode,{children:r.jsx(Qh,{})}));
//# sourceMappingURL=index-DkYfujJQ.js.map
