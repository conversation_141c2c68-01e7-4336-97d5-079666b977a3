import os
from typing import List, Optional
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field, field_validator


class Settings(BaseSettings):
    # 应用配置
    APP_NAME: str = "IDEALAB Financial Service"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8003

    # 数据库配置
    DATABASE_URL: str = "postgresql://postgres:password@localhost:5432/idealab_financial"

    # Neo4j配置
    NEO4J_URI: str = "bolt://localhost:7687"
    NEO4J_USER: str = "neo4j"
    NEO4J_PASSWORD: str = "zhiyikeji123"

    # Redis配置
    REDIS_URL: str = "redis://localhost:6379"

    # CORS配置
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:8000"]

    # JWT配置
    JWT_SECRET: str = "your-secret-key"
    JWT_ALGORITHM: str = "HS256"

    # 外部API配置
    TUSHARE_TOKEN: str = ""
    WIND_API_KEY: str = ""

    # 缓存配置
    CACHE_EXPIRE_TIME: int = 3600  # 1小时

    # 分析配置
    MAX_DEPTH: int = 10  # 股权穿透最大深度
    MAX_ENTITIES: int = 1000  # 最大处理实体数

    # 文件配置
    UPLOAD_DIR: str = "./uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB

    @field_validator("ALLOWED_ORIGINS", mode='before')
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v

    model_config = SettingsConfigDict(env_file=".env", case_sensitive=True, extra='ignore')


# 全局设置实例
settings = Settings()

# 兼容旧调用方的工厂函数
_def_settings: Optional[Settings] = settings

def get_settings() -> Settings:
    global _def_settings
    if _def_settings is None:
        _def_settings = Settings()
    return _def_settings
