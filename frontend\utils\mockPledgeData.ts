// 股权质押分析工具的模拟数据
import { PledgeAnalysisResponse, PledgeMarketProfile, CompanyPledgeRatio, PledgeDetail, InstitutionStats } from '@/services';

// 模拟市场概况数据
export const mockMarketProfile: PledgeMarketProfile = {
  date: '2024-01-15',
  total_companies: 5234,
  pledged_companies: 1876,
  total_pledged_shares: 234567890000,
  total_pledged_market_value: 1234567890000,
  average_pledge_ratio: 32.5,
  pledge_ratio_distribution: {
    low: 45.2,      // 0-30%
    medium: 28.7,   // 30-50%
    high: 18.9,     // 50-80%
    critical: 7.2   // 80%+
  }
};

// 模拟公司质押比例排行数据
export const mockCompanyRatios: CompanyPledgeRatio[] = [
  {
    company_code: '000001',
    company_name: '平安银行',
    total_shares: 19405918198,
    pledged_shares: 8765432100,
    pledge_ratio: 45.2,
    market_value: 234567890000,
    pledged_market_value: 106000000000,
    latest_price: 12.08,
    price_change: 0.15,
    price_change_ratio: 1.26,
    update_date: '2024-01-15'
  },
  {
    company_code: '000002',
    company_name: '万科A',
    total_shares: 11930709471,
    pledged_shares: 9544567577,
    pledge_ratio: 80.0,
    market_value: 84111501770,
    pledged_market_value: 67289201416,
    latest_price: 7.05,
    price_change: -0.08,
    price_change_ratio: -1.12,
    update_date: '2024-01-15'
  },
  {
    company_code: '000858',
    company_name: '五粮液',
    total_shares: 3868710699,
    pledged_shares: 2707697489,
    pledge_ratio: 70.0,
    market_value: 567890123456,
    pledged_market_value: 397523086419,
    latest_price: 146.78,
    price_change: 2.34,
    price_change_ratio: 1.62,
    update_date: '2024-01-15'
  },
  {
    company_code: '600036',
    company_name: '招商银行',
    total_shares: 25220645481,
    pledged_shares: 12610322741,
    pledge_ratio: 50.0,
    market_value: 890123456789,
    pledged_market_value: 445061728395,
    latest_price: 35.29,
    price_change: 0.67,
    price_change_ratio: 1.93,
    update_date: '2024-01-15'
  },
  {
    company_code: '600519',
    company_name: '贵州茅台',
    total_shares: 1256197097,
    pledged_shares: *********,
    pledge_ratio: 30.0,
    market_value: 2345678901234,
    pledged_market_value: 703703670370,
    latest_price: 1867.50,
    price_change: -15.20,
    price_change_ratio: -0.81,
    update_date: '2024-01-15'
  }
];

// 模拟证券公司统计数据
export const mockSecuritiesStats: InstitutionStats[] = [
  {
    institution_name: '中信证券',
    pledge_count: 156,
    total_pledge_amount: 45600000000,
    average_pledge_amount: *********,
    active_pledges: 134,
    released_pledges: 22,
    default_pledges: 0,
    risk_ratio: 2.3
  },
  {
    institution_name: '华泰证券',
    pledge_count: 142,
    total_pledge_amount: 38900000000,
    average_pledge_amount: *********,
    active_pledges: 125,
    released_pledges: 17,
    default_pledges: 0,
    risk_ratio: 1.8
  },
  {
    institution_name: '国泰君安',
    pledge_count: 128,
    total_pledge_amount: 34200000000,
    average_pledge_amount: 267187500,
    active_pledges: 112,
    released_pledges: 16,
    default_pledges: 0,
    risk_ratio: 2.1
  },
  {
    institution_name: '海通证券',
    pledge_count: 98,
    total_pledge_amount: ***********,
    average_pledge_amount: *********,
    active_pledges: 89,
    released_pledges: 9,
    default_pledges: 0,
    risk_ratio: 1.5
  },
  {
    institution_name: '广发证券',
    pledge_count: 87,
    total_pledge_amount: ***********,
    average_pledge_amount: *********,
    active_pledges: 78,
    released_pledges: 9,
    default_pledges: 0,
    risk_ratio: 2.8
  }
];

// 模拟银行统计数据
export const mockBankStats: InstitutionStats[] = [
  {
    institution_name: '中国银行',
    pledge_count: 89,
    total_pledge_amount: ***********,
    average_pledge_amount: *********,
    active_pledges: 82,
    released_pledges: 7,
    default_pledges: 0,
    risk_ratio: 1.2
  },
  {
    institution_name: '工商银行',
    pledge_count: 76,
    total_pledge_amount: ***********,
    average_pledge_amount: *********,
    active_pledges: 71,
    released_pledges: 5,
    default_pledges: 0,
    risk_ratio: 0.9
  },
  {
    institution_name: '建设银行',
    pledge_count: 65,
    total_pledge_amount: ***********,
    average_pledge_amount: *********,
    active_pledges: 61,
    released_pledges: 4,
    default_pledges: 0,
    risk_ratio: 1.1
  },
  {
    institution_name: '农业银行',
    pledge_count: 54,
    total_pledge_amount: ***********,
    average_pledge_amount: *********,
    active_pledges: 52,
    released_pledges: 2,
    default_pledges: 0,
    risk_ratio: 0.8
  },
  {
    institution_name: '招商银行',
    pledge_count: 43,
    total_pledge_amount: 32100000000,
    average_pledge_amount: *********,
    active_pledges: 41,
    released_pledges: 2,
    default_pledges: 0,
    risk_ratio: 1.4
  }
];

// 模拟公司分析结果
export const mockAnalysisResult: PledgeAnalysisResponse = {
  company: {
    code: '000002',
    name: '万科A',
    legal_representative: '郁亮',
    registered_capital: 11930709471,
    establishment_date: '1988-12-30',
    company_type: '股份有限公司',
    status: '存续'
  },
  pledge_records: [
    {
      company_code: '000002',
      company_name: '万科A',
      pledger: '王石',
      pledgee: '中国银行深圳分行',
      pledged_shares: 2500000000,
      pledged_ratio: 20.96,
      pledge_date: '2023-06-15',
      release_date: undefined,
      pledge_purpose: '补充流动资金',
      status: 'active'
    },
    {
      company_code: '000002',
      company_name: '万科A',
      pledger: '深圳地铁集团',
      pledgee: '招商银行深圳分行',
      pledged_shares: 3500000000,
      pledged_ratio: 29.34,
      pledge_date: '2023-08-20',
      release_date: undefined,
      pledge_purpose: '项目融资',
      status: 'active'
    },
    {
      company_code: '000002',
      company_name: '万科A',
      pledger: '华润集团',
      pledgee: '中信银行',
      pledged_shares: 1800000000,
      pledged_ratio: 15.09,
      pledge_date: '2023-10-10',
      release_date: '2024-01-10',
      pledge_purpose: '短期融资',
      status: 'released'
    },
    {
      company_code: '000002',
      company_name: '万科A',
      pledger: '安邦保险',
      pledgee: '平安银行',
      pledged_shares: 1744567577,
      pledged_ratio: 14.62,
      pledge_date: '2023-12-01',
      release_date: undefined,
      pledge_purpose: '战略投资',
      status: 'active'
    }
  ],
  total_pledged_ratio: 80.01,
  active_pledges_count: 3,
  risk_assessment: {
    risk_level: 'critical',
    risk_score: 85,
    risk_factors: [
      '质押比例过高(80%+)',
      '股价波动较大',
      '房地产行业风险',
      '多个质押机构',
      '解质压力较大'
    ],
    recommendations: [
      '密切关注股价变动',
      '监控解质风险',
      '适当降低质押比例',
      '分散质押机构',
      '加强流动性管理'
    ]
  },
  trend_analysis: {
    pledge_trend: 'increasing',
    monthly_changes: [
      { month: '2023-06', pledged_ratio: 20.96, change: 0 },
      { month: '2023-07', pledged_ratio: 20.96, change: 0 },
      { month: '2023-08', pledged_ratio: 50.30, change: 29.34 },
      { month: '2023-09', pledged_ratio: 50.30, change: 0 },
      { month: '2023-10', pledged_ratio: 65.39, change: 15.09 },
      { month: '2023-11', pledged_ratio: 65.39, change: 0 },
      { month: '2023-12', pledged_ratio: 80.01, change: 14.62 },
      { month: '2024-01', pledged_ratio: 65.39, change: -14.62 }
    ]
  }
};

// 模拟公司搜索建议
export const mockCompanySuggestions = [
  { code: '000001', name: '平安银行', industry: '银行', market: '深交所' },
  { code: '000002', name: '万科A', industry: '房地产', market: '深交所' },
  { code: '000858', name: '五粮液', industry: '食品饮料', market: '深交所' },
  { code: '600036', name: '招商银行', industry: '银行', market: '上交所' },
  { code: '600519', name: '贵州茅台', industry: '食品饮料', market: '上交所' },
  { code: '600887', name: '伊利股份', industry: '食品饮料', market: '上交所' },
  { code: '000858', name: '五粮液', industry: '食品饮料', market: '深交所' }
];
