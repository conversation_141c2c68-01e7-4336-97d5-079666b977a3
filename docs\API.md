# API 接口文档

## 基础信息

- **Base URL**: `https://api.idealab.com/v1`
- **Content-Type**: `application/json`
- **认证方式**: Bearer Token

## 认证接口

### 用户登录

**POST** `/auth/login`

请求参数：
```json
{
  "username": "string",
  "password": "string"
}
```

响应示例：
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "1",
      "username": "admin",
      "email": "<EMAIL>",
      "avatar": "https://example.com/avatar.jpg",
      "role": "admin"
    }
  }
}
```

### 用户注销

**POST** `/auth/logout`

请求头：
```
Authorization: Bearer {token}
```

## 工具接口

### 1. 对话机器人

**POST** `/tools/chatbot/chat`

请求参数：
```json
{
  "message": "string",
  "sessionId": "string",
  "context": {}
}
```

响应示例：
```json
{
  "code": 200,
  "data": {
    "reply": "这是AI的回复内容",
    "sessionId": "session_12345",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

### 2. 舆情分析

**POST** `/tools/sentiment/analyze`

请求参数：
```json
{
  "texts": ["string"],
  "options": {
    "includeKeywords": true,
    "confidence": 0.8
  }
}
```

响应示例：
```json
{
  "code": 200,
  "data": {
    "results": [
      {
        "text": "这个产品很好用",
        "sentiment": "positive",
        "score": 0.85,
        "keywords": ["产品", "好用"]
      }
    ],
    "summary": {
      "positive": 1,
      "negative": 0,
      "neutral": 0
    }
  }
}
```

### 3. 股权穿透

**GET** `/tools/equity/penetration/{companyName}`

查询参数：
- `depth`: 穿透层级 (默认: 3)
- `minRatio`: 最小持股比例 (默认: 0.1)

响应示例：
```json
{
  "code": 200,
  "data": {
    "company": {
      "name": "示例公司",
      "code": "91110000000000000X",
      "type": "有限责任公司"
    },
    "structure": {
      "children": [
        {
          "name": "子公司A",
          "ratio": 0.6,
          "children": []
        }
      ]
    }
  }
}
```

### 4. 股权质押分析

**GET** `/tools/equity/pledge/{companyCode}`

响应示例：
```json
{
  "code": 200,
  "data": {
    "summary": {
      "totalPledged": 2300,
      "pledgeRatio": 45.6,
      "riskLevel": "medium"
    },
    "records": [
      {
        "pledgor": "张三",
        "pledgee": "中国银行",
        "shares": "1000万股",
        "amount": "5亿元",
        "startDate": "2023-01-15",
        "endDate": "2025-01-15"
      }
    ]
  }
}
```

### 5. 债券分析

**POST** `/tools/bond/analyze`

请求参数：
```json
{
  "bondType": "all",
  "dateRange": {
    "start": "2024-01-01",
    "end": "2024-12-31"
  }
}
```

响应示例：
```json
{
  "code": 200,
  "data": {
    "summary": {
      "averageYield": 3.47,
      "totalVolume": 125600,
      "priceChange": 0.23
    },
    "bonds": [
      {
        "code": "110001",
        "name": "国债2301",
        "price": 101.25,
        "yield": 2.85,
        "rating": "AAA"
      }
    ]
  }
}
```

### 6. 批量OCR

**POST** `/tools/ocr/batch`

请求格式：`multipart/form-data`

参数：
- `files`: 文件数组
- `options`: JSON字符串

响应示例：
```json
{
  "code": 200,
  "data": {
    "taskId": "ocr_task_12345",
    "results": [
      {
        "filename": "image1.jpg",
        "text": "识别出的文字内容",
        "confidence": 0.95
      }
    ]
  }
}
```

### 7. 批量加水印

**POST** `/tools/watermark/batch`

请求格式：`multipart/form-data`

参数：
- `files`: 图片文件数组
- `settings`: 水印设置JSON

响应示例：
```json
{
  "code": 200,
  "data": {
    "taskId": "watermark_task_12345",
    "results": [
      {
        "filename": "watermarked_image1.jpg",
        "downloadUrl": "https://cdn.idealab.com/results/watermarked_image1.jpg"
      }
    ]
  }
}
```

### 8. Excel合并

**POST** `/tools/excel/merge`

请求格式：`multipart/form-data`

参数：
- `files`: Excel文件数组
- `settings`: 合并设置JSON

响应示例：
```json
{
  "code": 200,
  "data": {
    "taskId": "merge_task_12345",
    "result": {
      "filename": "merged_excel.xlsx",
      "downloadUrl": "https://cdn.idealab.com/results/merged_excel.xlsx",
      "totalRows": 5000,
      "fileSize": "2.5MB"
    }
  }
}
```

## 文件上传

### 通用文件上传

**POST** `/upload`

请求格式：`multipart/form-data`

参数：
- `file`: 文件
- `type`: 文件类型 (image/document/excel)

响应示例：
```json
{
  "code": 200,
  "data": {
    "fileId": "file_12345",
    "url": "https://cdn.idealab.com/uploads/file_12345.jpg",
    "size": 1024000,
    "mimeType": "image/jpeg"
  }
}
```

## 错误代码

| 代码 | 说明 |
|------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 请求限制

- 请求频率：100次/分钟
- 文件大小：单个文件最大50MB
- 批量处理：最多100个文件

## SDK示例

### JavaScript/TypeScript

```typescript
import axios from 'axios';

const api = axios.create({
  baseURL: 'https://api.idealab.com/v1',
  headers: {
    'Content-Type': 'application/json'
  }
});

// 设置认证token
api.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 使用示例
const chatWithBot = async (message: string) => {
  const response = await api.post('/tools/chatbot/chat', {
    message,
    sessionId: 'session_123'
  });
  return response.data;
};
```

### Python

```python
import requests

class IdealabAPI:
    def __init__(self, base_url="https://api.idealab.com/v1", token=None):
        self.base_url = base_url
        self.token = token
        self.session = requests.Session()
        if token:
            self.session.headers.update({
                'Authorization': f'Bearer {token}'
            })
    
    def login(self, username, password):
        response = self.session.post(f'{self.base_url}/auth/login', json={
            'username': username,
            'password': password
        })
        return response.json()
    
    def analyze_sentiment(self, texts):
        response = self.session.post(f'{self.base_url}/tools/sentiment/analyze', json={
            'texts': texts
        })
        return response.json()
```