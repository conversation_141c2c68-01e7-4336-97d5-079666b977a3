from typing import Dict, List, Any, Optional
from datetime import datetime, date
import asyncpg
import logging
from decimal import Decimal

from ..models.schemas import (
    PledgeAnalysisRequest, PledgeAnalysisResponse,
    PledgeRecord, Company
)
from ..config.settings import settings

logger = logging.getLogger(__name__)


class PledgeService:
    """股权质押分析服务"""
    
    def __init__(self):
        self.db_pool = None
    
    async def initialize(self):
        """初始化数据库连接池"""
        try:
            # 从settings获取数据库URL并转换为asyncpg格式
            db_url = settings.DATABASE_URL
            # 将postgresql://转换为asyncpg可用的格式
            if db_url.startswith("postgresql://"):
                db_url = db_url.replace("postgresql://", "postgresql://", 1)
            
            self.db_pool = await asyncpg.create_pool(
                db_url,
                min_size=1,
                max_size=10,
                command_timeout=60
            )
            logger.info("PledgeService数据库连接池初始化完成")
        except Exception as e:
            logger.error(f"PledgeService初始化失败: {e}")
            raise
    
    async def close(self):
        """关闭数据库连接池"""
        if self.db_pool:
            await self.db_pool.close()
            logger.info("PledgeService数据库连接池已关闭")
    
    async def analyze_company_pledge(
        self, 
        company_code: str, 
        include_subsidiaries: bool = False,
        date_range: Optional[Dict[str, str]] = None
    ) -> PledgeAnalysisResponse:
        """分析公司股权质押情况"""
        try:
            async with self.db_pool.acquire() as conn:
                # 构建查询条件
                where_conditions = ["stock_code = $1"]
                params = [company_code]
                param_count = 1
                
                if date_range:
                    if date_range.get("start_date"):
                        param_count += 1
                        where_conditions.append(f"announcement_date >= ${param_count}")
                        params.append(date_range["start_date"])
                    
                    if date_range.get("end_date"):
                        param_count += 1
                        where_conditions.append(f"announcement_date <= ${param_count}")
                        params.append(date_range["end_date"])
                
                where_clause = " AND ".join(where_conditions)
                
                # 获取公司基本信息
                company_query = f"""
                SELECT DISTINCT 
                    stock_code, stock_name
                FROM pledge_details 
                WHERE {where_clause}
                LIMIT 1
                """
                
                company_result = await conn.fetchrow(company_query, *params)
                
                if not company_result:
                    raise HTTPException(status_code=404, detail="Company not found")
                
                # 获取质押记录
                pledge_query = f"""
                SELECT 
                    stock_code, stock_name, shareholder_name as pledger,
                    pledge_institution as pledgee, pledged_shares,
                    pledge_ratio_of_total_shares as pledged_ratio,
                    pledge_start_date as pledge_date,
                    announcement_date,
                    CASE 
                        WHEN pledge_start_date <= CURRENT_DATE THEN 'active'
                        ELSE 'pending'
                    END as status
                FROM pledge_details 
                WHERE {where_clause}
                ORDER BY announcement_date DESC, pledge_start_date DESC
                """
                
                pledge_records = await conn.fetch(pledge_query, *params)
                
                # 计算统计数据
                total_pledged_ratio = sum(float(record['pledged_ratio'] or 0) for record in pledge_records)
                active_pledges_count = len([r for r in pledge_records if r['status'] == 'active'])
                
                # 风险评估
                risk_assessment = self._calculate_risk_assessment(total_pledged_ratio, pledge_records)
                
                # 趋势分析
                trend_analysis = await self._calculate_trend_analysis(conn, company_code)
                
                # 构建响应
                company_info = Company(
                    code=company_result['stock_code'],
                    name=company_result['stock_name']
                )
                
                pledge_record_list = []
                for record in pledge_records:
                    pledge_record_list.append(PledgeRecord(
                        pledge_id=f"{record['stock_code']}_{record['pledger']}_{record['pledge_date']}",
                        company_code=record['stock_code'],
                        company_name=record['stock_name'],
                        pledger=record['pledger'],
                        pledgee=record['pledgee'] or "未知",
                        pledged_shares=Decimal(str(record['pledged_shares'] or 0)),
                        pledged_ratio=Decimal(str(record['pledged_ratio'] or 0)),
                        pledge_date=record['pledge_date'] or date.today(),
                        status=record['status']
                    ))
                
                return PledgeAnalysisResponse(
                    company=company_info,
                    pledge_records=pledge_record_list,
                    total_pledged_ratio=total_pledged_ratio,
                    active_pledges_count=active_pledges_count,
                    risk_assessment=risk_assessment,
                    trend_analysis=trend_analysis
                )
                
        except Exception as e:
            logger.error(f"Analyze company pledge failed: {e}")
            raise
    
    def _calculate_risk_assessment(self, total_ratio: float, records: List) -> Dict[str, Any]:
        """计算风险评估"""
        # 风险等级判断
        if total_ratio >= 80:
            risk_level = "critical"
            risk_score = 90 + min(10, (total_ratio - 80) / 2)
        elif total_ratio >= 50:
            risk_level = "high"
            risk_score = 70 + (total_ratio - 50) * 20 / 30
        elif total_ratio >= 30:
            risk_level = "medium"
            risk_score = 40 + (total_ratio - 30) * 30 / 20
        else:
            risk_level = "low"
            risk_score = total_ratio * 40 / 30
        
        # 风险因素
        risk_factors = []
        if total_ratio > 80:
            risk_factors.append("质押比例过高(80%+)")
        if total_ratio > 50:
            risk_factors.append("质押比例较高")
        if len(records) > 10:
            risk_factors.append("质押笔数较多")
        
        # 建议措施
        recommendations = []
        if total_ratio > 70:
            recommendations.append("密切关注股价变动")
            recommendations.append("监控解质风险")
        if total_ratio > 50:
            recommendations.append("适当降低质押比例")
        if len(set(r['pledgee'] for r in records)) < 3:
            recommendations.append("分散质押机构")
        
        return {
            "risk_level": risk_level,
            "risk_score": round(risk_score, 2),
            "risk_factors": risk_factors,
            "recommendations": recommendations
        }
    
    async def _calculate_trend_analysis(self, conn, company_code: str) -> Dict[str, Any]:
        """计算趋势分析"""
        try:
            # 获取最近12个月的数据
            trend_query = """
            SELECT 
                DATE_TRUNC('month', announcement_date) as month,
                SUM(pledge_ratio_of_total_shares) as monthly_ratio
            FROM pledge_details 
            WHERE stock_code = $1 
                AND announcement_date >= CURRENT_DATE - INTERVAL '12 months'
            GROUP BY DATE_TRUNC('month', announcement_date)
            ORDER BY month
            """
            
            trend_data = await conn.fetch(trend_query, company_code)
            
            monthly_changes = []
            prev_ratio = 0
            
            for record in trend_data:
                current_ratio = float(record['monthly_ratio'] or 0)
                change = current_ratio - prev_ratio
                
                monthly_changes.append({
                    "month": record['month'].strftime('%Y-%m'),
                    "pledged_ratio": current_ratio,
                    "change": change
                })
                prev_ratio = current_ratio
            
            # 判断趋势
            if len(monthly_changes) >= 2:
                recent_changes = [m['change'] for m in monthly_changes[-3:]]
                avg_change = sum(recent_changes) / len(recent_changes)
                
                if avg_change > 5:
                    trend = "increasing"
                elif avg_change < -5:
                    trend = "decreasing"
                else:
                    trend = "stable"
            else:
                trend = "stable"
            
            return {
                "pledge_trend": trend,
                "monthly_changes": monthly_changes
            }
            
        except Exception as e:
            logger.error(f"Calculate trend analysis failed: {e}")
            return {
                "pledge_trend": "stable",
                "monthly_changes": []
            }
    
    async def get_market_profile(self) -> Dict[str, Any]:
        """获取市场概况"""
        try:
            async with self.db_pool.acquire() as conn:
                # 获取最新的市场概况数据
                query = """
                SELECT 
                    trading_date,
                    total_pledge_ratio,
                    pledge_company_count,
                    pledge_transaction_count,
                    total_pledge_shares,
                    total_pledge_market_value,
                    hs300_index,
                    index_change_ratio
                FROM pledge_market_profile 
                ORDER BY trading_date DESC 
                LIMIT 1
                """
                
                result = await conn.fetchrow(query)
                
                if not result:
                    # 如果没有市场概况数据，从质押明细计算
                    return await self._calculate_market_profile_from_details(conn)
                
                # 计算风险分布
                risk_distribution = await self._calculate_risk_distribution(conn)
                
                return {
                    "date": result['trading_date'].isoformat(),
                    "total_companies": int(result['pledge_company_count'] or 0),
                    "pledged_companies": int(result['pledge_company_count'] or 0),
                    "total_pledged_shares": int(result['total_pledge_shares'] or 0),
                    "total_pledged_market_value": int(result['total_pledge_market_value'] or 0),
                    "average_pledge_ratio": float(result['total_pledge_ratio'] or 0),
                    "pledge_ratio_distribution": risk_distribution
                }
                
        except Exception as e:
            logger.error(f"Get market profile failed: {e}")
            raise
    
    async def _calculate_market_profile_from_details(self, conn) -> Dict[str, Any]:
        """从质押明细计算市场概况"""
        query = """
        SELECT 
            COUNT(DISTINCT stock_code) as total_companies,
            COUNT(*) as total_pledges,
            SUM(pledged_shares) as total_shares,
            AVG(pledge_ratio_of_total_shares) as avg_ratio,
            MAX(announcement_date) as latest_date
        FROM pledge_details
        """
        
        result = await conn.fetchrow(query)
        risk_distribution = await self._calculate_risk_distribution(conn)
        
        return {
            "date": (result['latest_date'] or date.today()).isoformat(),
            "total_companies": int(result['total_companies'] or 0),
            "pledged_companies": int(result['total_companies'] or 0),
            "total_pledged_shares": int(result['total_shares'] or 0),
            "total_pledged_market_value": 0,  # 需要从其他数据源计算
            "average_pledge_ratio": float(result['avg_ratio'] or 0),
            "pledge_ratio_distribution": risk_distribution
        }
    
    async def _calculate_risk_distribution(self, conn) -> Dict[str, float]:
        """计算风险分布"""
        query = """
        SELECT 
            stock_code,
            SUM(pledge_ratio_of_total_shares) as total_ratio
        FROM pledge_details
        GROUP BY stock_code
        """
        
        results = await conn.fetch(query)
        
        distribution = {"low": 0, "medium": 0, "high": 0, "critical": 0}
        total_companies = len(results)
        
        if total_companies == 0:
            return distribution
        
        for record in results:
            ratio = float(record['total_ratio'] or 0)
            if ratio >= 80:
                distribution["critical"] += 1
            elif ratio >= 50:
                distribution["high"] += 1
            elif ratio >= 30:
                distribution["medium"] += 1
            else:
                distribution["low"] += 1
        
        # 转换为百分比
        for key in distribution:
            distribution[key] = round(distribution[key] / total_companies * 100, 2)
        
        return distribution

    async def get_company_pledge_ratio(self, page: int = 1, size: int = 50) -> List[Dict[str, Any]]:
        """获取公司质押比例排行"""
        try:
            async with self.db_pool.acquire() as conn:
                offset = (page - 1) * size

                query = """
                SELECT
                    stock_code,
                    stock_name,
                    SUM(pledged_shares) as total_pledged_shares,
                    SUM(pledge_ratio_of_total_shares) as total_pledge_ratio,
                    COUNT(*) as pledge_count,
                    MAX(latest_price) as latest_price,
                    MAX(announcement_date) as latest_announcement
                FROM pledge_details
                GROUP BY stock_code, stock_name
                ORDER BY SUM(pledge_ratio_of_total_shares) DESC
                LIMIT $1 OFFSET $2
                """

                results = await conn.fetch(query, size, offset)

                company_ratios = []
                for record in results:
                    company_ratios.append({
                        "company_code": record['stock_code'],
                        "company_name": record['stock_name'],
                        "total_shares": 0,  # 需要从其他数据源获取
                        "pledged_shares": int(record['total_pledged_shares'] or 0),
                        "pledge_ratio": float(record['total_pledge_ratio'] or 0),
                        "market_value": 0,  # 需要计算
                        "pledged_market_value": 0,  # 需要计算
                        "latest_price": float(record['latest_price'] or 0),
                        "price_change": 0,  # 需要从实时数据获取
                        "price_change_ratio": 0,  # 需要从实时数据获取
                        "update_date": (record['latest_announcement'] or date.today()).isoformat()
                    })

                return company_ratios

        except Exception as e:
            logger.error(f"Get company pledge ratio failed: {e}")
            raise

    async def get_pledge_details(
        self,
        company_code: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        page: int = 1,
        size: int = 50
    ) -> List[Dict[str, Any]]:
        """获取质押明细"""
        try:
            async with self.db_pool.acquire() as conn:
                # 构建查询条件
                where_conditions = []
                params = []
                param_count = 0

                if company_code:
                    param_count += 1
                    where_conditions.append(f"stock_code = ${param_count}")
                    params.append(company_code)

                if start_date:
                    param_count += 1
                    where_conditions.append(f"announcement_date >= ${param_count}")
                    params.append(start_date)

                if end_date:
                    param_count += 1
                    where_conditions.append(f"announcement_date <= ${param_count}")
                    params.append(end_date)

                where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

                offset = (page - 1) * size
                param_count += 1
                limit_param = f"${param_count}"
                param_count += 1
                offset_param = f"${param_count}"
                params.extend([size, offset])

                query = f"""
                SELECT
                    id::text as id,
                    stock_code,
                    stock_name,
                    announcement_date,
                    shareholder_name as pledger,
                    pledge_institution as pledgee,
                    pledged_shares as pledge_shares,
                    pledge_ratio_of_total_shares as pledge_ratio,
                    pledge_start_date,
                    'active' as pledge_purpose,
                    pledge_ratio_of_total_shares as cumulative_pledge_ratio
                FROM pledge_details
                WHERE {where_clause}
                ORDER BY announcement_date DESC, pledge_start_date DESC
                LIMIT {limit_param} OFFSET {offset_param}
                """

                results = await conn.fetch(query, *params)

                details = []
                for record in results:
                    details.append({
                        "id": record['id'],
                        "company_code": record['stock_code'],
                        "company_name": record['stock_name'],
                        "announcement_date": (record['announcement_date'] or date.today()).isoformat(),
                        "pledger": record['pledger'],
                        "pledgee": record['pledgee'] or "未知",
                        "pledge_shares": int(record['pledge_shares'] or 0),
                        "pledge_ratio": float(record['pledge_ratio'] or 0),
                        "release_shares": None,
                        "pledge_purpose": record['pledge_purpose'],
                        "cumulative_pledge_ratio": float(record['cumulative_pledge_ratio'] or 0)
                    })

                return details

        except Exception as e:
            logger.error(f"Get pledge details failed: {e}")
            raise

    async def get_institution_stats(self, institution_type: str) -> List[Dict[str, Any]]:
        """获取机构统计"""
        try:
            async with self.db_pool.acquire() as conn:
                # 根据类型选择表名
                if institution_type == "securities":
                    table_name = "pledge_institution_company"
                elif institution_type == "bank":
                    table_name = "pledge_institution_bank"
                else:
                    raise ValueError(f"Invalid institution type: {institution_type}")

                query = f"""
                SELECT
                    institution_name,
                    pledge_transaction_count as pledge_count,
                    pledge_quantity as total_pledge_amount,
                    CASE
                        WHEN pledge_transaction_count > 0
                        THEN pledge_quantity / pledge_transaction_count
                        ELSE 0
                    END as average_pledge_amount,
                    pledge_transaction_count as active_pledges,
                    0 as released_pledges,
                    0 as default_pledges,
                    above_liquidation_ratio as risk_ratio
                FROM {table_name}
                ORDER BY pledge_transaction_count DESC
                """

                results = await conn.fetch(query)

                stats = []
                for record in results:
                    stats.append({
                        "institution_name": record['institution_name'],
                        "pledge_count": int(record['pledge_count'] or 0),
                        "total_pledge_amount": int(record['total_pledge_amount'] or 0),
                        "average_pledge_amount": int(record['average_pledge_amount'] or 0),
                        "active_pledges": int(record['active_pledges'] or 0),
                        "released_pledges": int(record['released_pledges'] or 0),
                        "default_pledges": int(record['default_pledges'] or 0),
                        "risk_ratio": float(record['risk_ratio'] or 0)
                    })

                return stats

        except Exception as e:
            logger.error(f"Get institution stats failed: {e}")
            raise

    async def search_companies(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索质押公司"""
        try:
            async with self.db_pool.acquire() as conn:
                query = """
                SELECT DISTINCT
                    stock_code as code,
                    stock_name as name,
                    'A股' as market
                FROM pledge_details
                WHERE stock_code ILIKE $1 OR stock_name ILIKE $1
                ORDER BY stock_code
                LIMIT 20
                """

                search_pattern = f"%{keyword}%"
                results = await conn.fetch(query, search_pattern)

                suggestions = []
                for record in results:
                    suggestions.append({
                        "code": record['code'],
                        "name": record['name'],
                        "market": record['market']
                    })

                return suggestions

        except Exception as e:
            logger.error(f"Search companies failed: {e}")
            raise

    async def get_risk_analysis(self, company_code: str) -> Dict[str, Any]:
        """获取风险分析"""
        try:
            async with self.db_pool.acquire() as conn:
                # 获取公司质押风险指标
                query = """
                SELECT
                    SUM(pledge_ratio_of_total_shares) as total_ratio,
                    COUNT(*) as pledge_count,
                    COUNT(DISTINCT pledge_institution) as institution_count,
                    AVG(latest_price) as avg_price,
                    AVG(estimated_liquidation_line) as avg_liquidation_line
                FROM pledge_details
                WHERE stock_code = $1
                """

                result = await conn.fetchrow(query, company_code)

                if not result:
                    raise ValueError("Company not found")

                total_ratio = float(result['total_ratio'] or 0)
                pledge_count = int(result['pledge_count'] or 0)
                institution_count = int(result['institution_count'] or 0)
                avg_price = float(result['avg_price'] or 0)
                avg_liquidation_line = float(result['avg_liquidation_line'] or 0)

                # 计算风险指标
                risk_indicators = {
                    "pledge_concentration": total_ratio,
                    "institution_diversification": institution_count,
                    "liquidation_risk": (avg_price - avg_liquidation_line) / avg_price * 100 if avg_price > 0 else 0,
                    "pledge_intensity": pledge_count
                }

                # 风险评估
                risk_assessment = self._calculate_risk_assessment(total_ratio, [])

                return {
                    "company_code": company_code,
                    "risk_indicators": risk_indicators,
                    "risk_assessment": risk_assessment,
                    "analysis_date": datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"Get risk analysis failed: {e}")
            raise
