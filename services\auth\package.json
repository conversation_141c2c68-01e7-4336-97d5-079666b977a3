{"name": "idealab-auth-service", "version": "1.0.0", "description": "IDEALAB Authentication & Authorization Service", "main": "src/index.js", "scripts": {"db:deploy": "prisma migrate deploy", "start": "npm run db:deploy && node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@prisma/client": "^6.12.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "redis": "^4.6.11", "winston": "^3.11.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "prisma": "^6.12.0"}, "keywords": ["auth", "jwt", "microservice"], "author": "IDEALAB Team", "license": "MIT", "prisma": {"schema": "src/prisma/schema.prisma", "seed": "node src/prisma/seed.js"}}