from fastapi import APIRouter, HTTPException, Depends
import logging
import time
from typing import List

from ..models.schemas import (
    SentimentRequest, SentimentResponse, BatchSentimentRequest, 
    BatchSentimentResponse, BaseResponse
)
from ..services.sentiment_service import SentimentService

router = APIRouter()
logger = logging.getLogger(__name__)


async def get_sentiment_service() -> SentimentService:
    """依赖注入：获取情感分析服务实例"""
    return SentimentService()


@router.post("/analyze", response_model=SentimentResponse)
async def analyze_sentiment(
    request: SentimentRequest,
    sentiment_service: SentimentService = Depends(get_sentiment_service)
):
    """单条文本情感分析"""
    start_time = time.time()
    
    try:
        logger.info(f"Analyzing sentiment for text: {request.text[:50]}...")
        
        # 执行情感分析
        result = await sentiment_service.analyze_sentiment(
            text=request.text,
            source=request.source,
            keywords=request.keywords
        )
        
        processing_time = time.time() - start_time
        result.processing_time = processing_time
        
        logger.info(f"Sentiment analysis completed in {processing_time:.2f}s")
        
        return result
        
    except Exception as e:
        logger.error(f"Sentiment analysis error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to analyze sentiment: {str(e)}"
        )


@router.post("/batch-analyze", response_model=BatchSentimentResponse)
async def batch_analyze_sentiment(
    request: BatchSentimentRequest,
    sentiment_service: SentimentService = Depends(get_sentiment_service)
):
    """批量文本情感分析"""
    start_time = time.time()
    
    try:
        logger.info(f"Batch analyzing sentiment for {len(request.texts)} texts...")
        
        # 执行批量情感分析
        results = await sentiment_service.batch_analyze_sentiment(
            texts=request.texts,
            source=request.source
        )
        
        total_time = time.time() - start_time
        
        # 计算汇总统计
        sentiments = [r.sentiment_result.sentiment for r in results]
        summary_stats = {
            "total_texts": len(request.texts),
            "positive_count": sentiments.count("positive"),
            "negative_count": sentiments.count("negative"),
            "neutral_count": sentiments.count("neutral"),
            "average_confidence": sum(r.sentiment_result.confidence for r in results) / len(results),
            "average_score": sum(r.sentiment_result.score for r in results) / len(results)
        }
        
        logger.info(f"Batch sentiment analysis completed in {total_time:.2f}s")
        
        return BatchSentimentResponse(
            results=results,
            summary_stats=summary_stats,
            total_processing_time=total_time
        )
        
    except Exception as e:
        logger.error(f"Batch sentiment analysis error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to analyze batch sentiment: {str(e)}"
        )


@router.post("/report/generate")
async def generate_sentiment_report(
    request: BatchSentimentRequest,
    sentiment_service: SentimentService = Depends(get_sentiment_service)
):
    """生成舆情分析报告"""
    try:
        logger.info(f"Generating sentiment report for {len(request.texts)} texts...")
        
        # 生成详细报告
        report = await sentiment_service.generate_report(
            texts=request.texts,
            source=request.source
        )
        
        logger.info("Sentiment report generated successfully")
        
        return {
            "success": True,
            "report": report,
            "generated_at": time.time()
        }
        
    except Exception as e:
        logger.error(f"Generate sentiment report error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate sentiment report: {str(e)}"
        )


@router.get("/keywords/trending")
async def get_trending_keywords(
    limit: int = 10,
    sentiment_service: SentimentService = Depends(get_sentiment_service)
):
    """获取热门关键词"""
    try:
        keywords = await sentiment_service.get_trending_keywords(limit)
        
        return {
            "success": True,
            "trending_keywords": keywords,
            "count": len(keywords)
        }
        
    except Exception as e:
        logger.error(f"Get trending keywords error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get trending keywords: {str(e)}"
        )


@router.get("/statistics/daily")
async def get_daily_statistics(
    days: int = 7,
    sentiment_service: SentimentService = Depends(get_sentiment_service)
):
    """获取每日情感分析统计"""
    try:
        stats = await sentiment_service.get_daily_statistics(days)
        
        return {
            "success": True,
            "statistics": stats,
            "period_days": days
        }
        
    except Exception as e:
        logger.error(f"Get daily statistics error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get daily statistics: {str(e)}"
        )