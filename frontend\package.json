{"name": "idealab-frontend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@faker-js/faker": "^9.9.0", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@types/cytoscape": "^3.21.9", "@types/d3": "^7.4.3", "antd": "^5.12.8", "axios": "^1.6.2", "clsx": "^2.1.1", "cytoscape": "^3.32.1", "cytoscape-avsdf": "^1.0.0", "cytoscape-cose-bilkent": "^4.1.0", "cytoscape-dagre": "^2.5.0", "cytoscape-fcose": "^2.2.0", "d3": "^7.9.0", "framer-motion": "^12.23.6", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "react-spring": "^10.0.1", "socket.io-client": "^4.7.4", "tailwind-merge": "^3.3.1", "zustand": "^4.4.7"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8"}}