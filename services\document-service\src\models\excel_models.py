from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from enum import Enum


class MergeMode(str, Enum):
    """合并模式枚举"""
    MULTIPLE_FILES = "multiple_files"  # 多个Excel文件合并为一个
    MULTIPLE_SHEETS = "multiple_sheets"  # 一个Excel文件的多个sheet合并


class ExcelMergeConfig(BaseModel):
    """Excel合并配置"""
    merge_mode: MergeMode = Field(..., description="合并模式")
    sheet_indices: Optional[List[int]] = Field(None, description="要合并的sheet索引列表")
    skip_header_rows: int = Field(0, description="跳过的头部行数")
    skip_footer_rows: int = Field(0, description="跳过的尾部行数")
    output_filename: Optional[str] = Field(None, description="输出文件名")
    preserve_formatting: bool = Field(True, description="是否保留格式")


class ExcelMergeRequest(BaseModel):
    """Excel合并请求"""
    config: ExcelMergeConfig = Field(..., description="合并配置")
    file_ids: List[str] = Field(..., description="文件ID列表")


class ExcelFileInfo(BaseModel):
    """Excel文件信息"""
    filename: str = Field(..., description="文件名")
    sheet_count: int = Field(..., description="工作表数量")
    sheet_names: List[str] = Field(..., description="工作表名称列表")
    total_rows: int = Field(..., description="总行数")
    total_columns: int = Field(..., description="总列数")
    file_size: int = Field(..., description="文件大小(字节)")


class ExcelSheetInfo(BaseModel):
    """Excel工作表信息"""
    name: str = Field(..., description="工作表名称")
    index: int = Field(..., description="工作表索引")
    rows: int = Field(..., description="行数")
    columns: int = Field(..., description="列数")
    has_data: bool = Field(..., description="是否包含数据")


class ExcelPreviewData(BaseModel):
    """Excel预览数据"""
    filename: str = Field(..., description="文件名")
    sheet_name: str = Field(..., description="当前工作表名称")
    sheet_index: int = Field(..., description="当前工作表索引")
    headers: List[str] = Field(..., description="列标题")
    data: List[List[Any]] = Field(..., description="预览数据")
    total_rows: int = Field(..., description="总行数")
    total_columns: int = Field(..., description="总列数")
    preview_rows: int = Field(..., description="预览行数")


class ExcelMergeProgress(BaseModel):
    """Excel合并进度"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="状态: pending, processing, completed, failed")
    progress: float = Field(0.0, description="进度百分比 (0-100)")
    current_file: Optional[str] = Field(None, description="当前处理的文件")
    processed_files: int = Field(0, description="已处理文件数")
    total_files: int = Field(0, description="总文件数")
    message: Optional[str] = Field(None, description="状态消息")
    error: Optional[str] = Field(None, description="错误信息")


class ExcelMergeResponse(BaseModel):
    """Excel合并响应"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="合并状态")
    output_filename: str = Field(..., description="输出文件名")
    output_file_id: str = Field(..., description="输出文件ID")
    merged_files_count: int = Field(..., description="合并的文件数量")
    merged_sheets_count: int = Field(..., description="合并的工作表数量")
    total_rows: int = Field(..., description="合并后总行数")
    processing_time: float = Field(..., description="处理时间(秒)")
    file_size: int = Field(..., description="输出文件大小(字节)")
    download_url: str = Field(..., description="下载链接")


class ExcelMergeStats(BaseModel):
    """Excel合并统计"""
    source_files: List[ExcelFileInfo] = Field(..., description="源文件信息")
    merge_config: ExcelMergeConfig = Field(..., description="合并配置")
    output_info: ExcelFileInfo = Field(..., description="输出文件信息")
    processing_summary: Dict[str, Any] = Field(..., description="处理摘要")


class VBACommand(BaseModel):
    """VBA命令信息"""
    code: str = Field(..., description="VBA代码")
    instructions: List[str] = Field(..., description="使用说明")
    features: List[str] = Field(..., description="功能特性")
    requirements: List[str] = Field(default_factory=list, description="系统要求")


class LocalToolInfo(BaseModel):
    """本地工具信息"""
    name: str = Field(..., description="工具名称")
    version: str = Field(..., description="版本号")
    description: str = Field(..., description="工具描述")
    download_url: str = Field(..., description="下载链接")
    file_size: int = Field(..., description="文件大小(字节)")
    requirements: List[str] = Field(..., description="系统要求")
    features: List[str] = Field(..., description="功能特性")
