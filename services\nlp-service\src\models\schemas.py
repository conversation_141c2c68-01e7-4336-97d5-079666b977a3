from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime


# 聊天机器人相关模型
class ChatMessage(BaseModel):
    """聊天消息模型"""
    role: str = Field(..., description="消息角色：user或assistant")
    content: str = Field(..., description="消息内容")
    timestamp: Optional[datetime] = Field(default=None, description="消息时间戳")


class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str = Field(..., description="用户消息", min_length=1, max_length=1000)
    conversation_id: Optional[str] = Field(None, description="会话ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    context: Optional[Dict[str, Any]] = Field(default={}, description="上下文信息")


class ChatResponse(BaseModel):
    """聊天响应模型"""
    response: str = Field(..., description="AI回复")
    conversation_id: str = Field(..., description="会话ID")
    confidence: Optional[float] = Field(None, description="回复置信度")
    tokens_used: Optional[int] = Field(None, description="使用的token数量")
    response_time: Optional[float] = Field(None, description="响应时间(秒)")


# 舆情分析相关模型
class SentimentRequest(BaseModel):
    """舆情分析请求模型"""
    text: str = Field(..., description="待分析文本", min_length=1, max_length=5000)
    source: Optional[str] = Field(None, description="文本来源")
    keywords: Optional[List[str]] = Field(default=[], description="关键词列表")


class SentimentResult(BaseModel):
    """情感分析结果"""
    sentiment: str = Field(..., description="情感倾向：positive/negative/neutral")
    confidence: float = Field(..., description="置信度", ge=0.0, le=1.0)
    score: float = Field(..., description="情感得分", ge=-1.0, le=1.0)


class KeywordAnalysis(BaseModel):
    """关键词分析结果"""
    keyword: str = Field(..., description="关键词")
    frequency: int = Field(..., description="出现频次")
    importance: float = Field(..., description="重要性得分", ge=0.0, le=1.0)


class SentimentResponse(BaseModel):
    """舆情分析响应模型"""
    sentiment_result: SentimentResult = Field(..., description="情感分析结果")
    keywords: List[KeywordAnalysis] = Field(default=[], description="关键词分析")
    summary: str = Field(..., description="分析摘要")
    entities: Optional[List[str]] = Field(default=[], description="提取的实体")
    processing_time: Optional[float] = Field(None, description="处理时间(秒)")


# 批量处理模型
class BatchSentimentRequest(BaseModel):
    """批量舆情分析请求"""
    texts: List[str] = Field(..., description="文本列表", min_items=1, max_items=100)
    source: Optional[str] = Field(None, description="文本来源")


class BatchSentimentResponse(BaseModel):
    """批量舆情分析响应"""
    results: List[SentimentResponse] = Field(..., description="分析结果列表")
    summary_stats: Dict[str, Any] = Field(..., description="汇总统计")
    total_processing_time: float = Field(..., description="总处理时间")


# 通用响应模型
class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = Field(True, description="操作是否成功")
    message: str = Field("", description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")


class ErrorResponse(BaseResponse):
    """错误响应模型"""
    success: bool = Field(False, description="操作失败")
    error_code: Optional[str] = Field(None, description="错误代码")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")