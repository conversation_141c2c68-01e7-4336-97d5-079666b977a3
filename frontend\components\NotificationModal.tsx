import React from 'react';
import { <PERSON><PERSON>, Button, Space, Typography } from 'antd';
import {
    ExclamationCircleOutlined,
    InfoCircleOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    QuestionCircleOutlined
} from '@ant-design/icons';

const { Text, Title } = Typography;

export type NotificationType = 'info' | 'success' | 'warning' | 'error' | 'confirm';

export interface NotificationModalProps {
    visible: boolean;
    type: NotificationType;
    title: string;
    message: string | React.ReactNode;
    details?: string | React.ReactNode;
    onConfirm?: () => void;
    onCancel?: () => void;
    onClose?: () => void;
    confirmText?: string;
    cancelText?: string;
    showCancel?: boolean;
    width?: number;
    maskClosable?: boolean;
    centered?: boolean;
}

const getIconAndColor = (type: NotificationType) => {
    switch (type) {
        case 'info':
            return {
                icon: <InfoCircleOutlined style={{ fontSize: '24px', color: '#2563eb' }} />,
                titleColor: '#2563eb',
                borderColor: '#2563eb'
            };
        case 'success':
            return {
                icon: <CheckCircleOutlined style={{ fontSize: '24px', color: '#10b981' }} />,
                titleColor: '#10b981',
                borderColor: '#10b981'
            };
        case 'warning':
            return {
                icon: <ExclamationCircleOutlined style={{ fontSize: '24px', color: '#f97316' }} />,
                titleColor: '#f97316',
                borderColor: '#f97316'
            };
        case 'error':
            return {
                icon: <CloseCircleOutlined style={{ fontSize: '24px', color: '#dc2626' }} />,
                titleColor: '#dc2626',
                borderColor: '#dc2626'
            };
        case 'confirm':
            return {
                icon: <QuestionCircleOutlined style={{ fontSize: '24px', color: '#f97316' }} />,
                titleColor: '#f97316',
                borderColor: '#f97316'
            };
        default:
            return {
                icon: <InfoCircleOutlined style={{ fontSize: '24px', color: '#2563eb' }} />,
                titleColor: '#2563eb',
                borderColor: '#2563eb'
            };
    }
};

export const NotificationModal: React.FC<NotificationModalProps> = ({
    visible,
    type,
    title,
    message,
    details,
    onConfirm,
    onCancel,
    onClose,
    confirmText = '确定',
    cancelText = '取消',
    showCancel = type === 'confirm',
    width = 520,
    maskClosable = false,
    centered = true
}) => {
    const { icon, titleColor, borderColor } = getIconAndColor(type);

    const handleConfirm = () => {
        onConfirm?.();
        onClose?.();
    };

    const handleCancel = () => {
        onCancel?.();
        onClose?.();
    };

    const handleModalClose = () => {
        if (type === 'confirm') {
            onCancel?.();
        }
        onClose?.();
    };

    return (
        <Modal
            open={visible}
            onCancel={handleModalClose}
            width={width}
            centered={centered}
            maskClosable={maskClosable}
            footer={null}
            styles={{
                content: {
                    backgroundColor: 'rgba(15, 23, 42, 0.95)',
                    backdropFilter: 'blur(20px)',
                    border: `1px solid ${borderColor}`,
                    borderRadius: '12px',
                    boxShadow: `0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px ${borderColor}20`
                },
                header: {
                    backgroundColor: 'transparent',
                    borderBottom: 'none',
                    paddingBottom: 0
                },
                body: {
                    padding: '24px'
                }
            }}
            closeIcon={
                <div style={{
                    width: '32px',
                    height: '32px',
                    borderRadius: '50%',
                    backgroundColor: 'rgba(255, 255, 255, 0.08)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.2s',
                    border: '1px solid rgba(255, 255, 255, 0.15)'
                }}>
                    <CloseCircleOutlined style={{ fontSize: '16px', color: 'rgba(255, 255, 255, 0.75)' }} />
                </div>
            }
        >
            <div style={{ padding: '8px 0' }}>
                {/* 标题区域 */}
                <div style={{ 
                    display: 'flex', 
                    alignItems: 'flex-start', 
                    marginBottom: '16px',
                    gap: '12px'
                }}>
                    <div style={{ flexShrink: 0, marginTop: '2px' }}>
                        {icon}
                    </div>
                    <div style={{ flex: 1 }}>
                        <Title 
                            level={4} 
                            style={{ 
                                margin: 0, 
                                color: titleColor,
                                fontSize: '18px',
                                fontWeight: 600,
                                lineHeight: '26px'
                            }}
                        >
                            {title}
                        </Title>
                    </div>
                </div>

                {/* 消息内容 */}
                <div style={{
                    marginLeft: '36px',
                    marginBottom: details ? '16px' : '24px'
                }}>
                    <div style={{
                        color: 'rgba(255, 255, 255, 0.85)',
                        fontSize: '14px',
                        lineHeight: '22px'
                    }}>
                        {typeof message === 'string' ? (
                            <Text style={{ color: 'rgba(255, 255, 255, 0.85)' }}>{message}</Text>
                        ) : (
                            message
                        )}
                    </div>
                </div>

                {/* 详细信息 */}
                {details && (
                    <div style={{
                        marginLeft: '36px',
                        marginBottom: '24px',
                        padding: '12px',
                        backgroundColor: 'rgba(255, 255, 255, 0.05)',
                        borderRadius: '8px',
                        border: '1px solid rgba(255, 255, 255, 0.1)'
                    }}>
                        <div style={{
                            color: 'rgba(255, 255, 255, 0.75)',
                            fontSize: '13px',
                            lineHeight: '20px'
                        }}>
                            {typeof details === 'string' ? (
                                <Text style={{ color: 'rgba(255, 255, 255, 0.75)' }}>{details}</Text>
                            ) : (
                                details
                            )}
                        </div>
                    </div>
                )}

                {/* 按钮区域 */}
                <div style={{ 
                    display: 'flex', 
                    justifyContent: 'flex-end',
                    gap: '8px',
                    marginLeft: '36px'
                }}>
                    {showCancel && (
                        <Button
                            onClick={handleCancel}
                            style={{
                                borderRadius: '8px',
                                height: '36px',
                                paddingLeft: '16px',
                                paddingRight: '16px',
                                backgroundColor: 'rgba(255, 255, 255, 0.05)',
                                borderColor: 'rgba(255, 255, 255, 0.15)',
                                color: 'rgba(255, 255, 255, 0.95)'
                            }}
                        >
                            {cancelText}
                        </Button>
                    )}
                    <Button
                        type="primary"
                        onClick={handleConfirm}
                        style={{
                            backgroundColor: titleColor,
                            borderColor: titleColor,
                            borderRadius: '8px',
                            height: '36px',
                            paddingLeft: '16px',
                            paddingRight: '16px',
                            boxShadow: `0 4px 12px ${titleColor}30`,
                            color: '#ffffff',
                            fontWeight: '500'
                        }}
                    >
                        {confirmText}
                    </Button>
                </div>
            </div>
        </Modal>
    );
};

// 便捷方法
export const showNotification = {
    info: (props: Omit<NotificationModalProps, 'type'>) => 
        ({ ...props, type: 'info' as const }),
    success: (props: Omit<NotificationModalProps, 'type'>) => 
        ({ ...props, type: 'success' as const }),
    warning: (props: Omit<NotificationModalProps, 'type'>) => 
        ({ ...props, type: 'warning' as const }),
    error: (props: Omit<NotificationModalProps, 'type'>) => 
        ({ ...props, type: 'error' as const }),
    confirm: (props: Omit<NotificationModalProps, 'type'>) => 
        ({ ...props, type: 'confirm' as const })
};

export default NotificationModal;
